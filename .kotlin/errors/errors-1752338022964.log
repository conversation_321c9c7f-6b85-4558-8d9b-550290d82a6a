kotlin version: 2.0.21
error message: org.jetbrains.kotlin.backend.common.BackendException: Backend Internal error: Exception during IR lowering
File being compiled: /Users/<USER>/AndroidStudioProjects/MyApplication/opencv/java/src/org/opencv/core/MatAt.kt
The root cause java.lang.RuntimeException was thrown at: org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.generate(FunctionCodegen.kt:47)
	at org.jetbrains.kotlin.backend.common.CodegenUtil.reportBackendException(CodegenUtil.kt:253)
	at org.jetbrains.kotlin.backend.common.CodegenUtil.reportBackendException$default(CodegenUtil.kt:236)
	at org.jetbrains.kotlin.backend.common.phaser.PerformByIrFilePhase.invokeSequential(performByIrFile.kt:65)
	at org.jetbrains.kotlin.backend.common.phaser.PerformByIrFilePhase.invoke(performByIrFile.kt:52)
	at org.jetbrains.kotlin.backend.common.phaser.PerformByIrFilePhase.invoke(performByIrFile.kt:38)
	at org.jetbrains.kotlin.backend.common.phaser.NamedCompilerPhase.phaseBody(CompilerPhase.kt:166)
	at org.jetbrains.kotlin.backend.common.phaser.AbstractNamedCompilerPhase.invoke(CompilerPhase.kt:113)
	at org.jetbrains.kotlin.backend.common.phaser.CompositePhase.invoke(PhaseBuilders.kt:27)
	at org.jetbrains.kotlin.backend.common.phaser.CompositePhase.invoke(PhaseBuilders.kt:14)
	at org.jetbrains.kotlin.backend.common.phaser.NamedCompilerPhase.phaseBody(CompilerPhase.kt:166)
	at org.jetbrains.kotlin.backend.common.phaser.AbstractNamedCompilerPhase.invoke(CompilerPhase.kt:113)
	at org.jetbrains.kotlin.backend.common.phaser.CompilerPhaseKt.invokeToplevel(CompilerPhase.kt:62)
	at org.jetbrains.kotlin.backend.jvm.JvmIrCodegenFactory.invokeCodegen(JvmIrCodegenFactory.kt:371)
	at org.jetbrains.kotlin.codegen.CodegenFactory.generateModule(CodegenFactory.kt:47)
	at org.jetbrains.kotlin.backend.jvm.JvmIrCodegenFactory.generateModuleInFrontendIRMode(JvmIrCodegenFactory.kt:433)
	at org.jetbrains.kotlin.cli.jvm.compiler.pipeline.JvmCompilerPipelineKt.generateCodeFromIr(jvmCompilerPipeline.kt:246)
	at org.jetbrains.kotlin.cli.jvm.compiler.pipeline.JvmCompilerPipelineKt.compileModulesUsingFrontendIrAndLightTree(jvmCompilerPipeline.kt:142)
	at org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(K2JVMCompiler.kt:148)
	at org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(K2JVMCompiler.kt:43)
	at org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(CLICompiler.kt:103)
	at org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(CLICompiler.kt:49)
	at org.jetbrains.kotlin.cli.common.CLITool.exec(CLITool.kt:101)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.runCompiler(IncrementalJvmCompilerRunner.kt:464)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.runCompiler(IncrementalJvmCompilerRunner.kt:73)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.doCompile(IncrementalCompilerRunner.kt:506)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compileImpl(IncrementalCompilerRunner.kt:423)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compileNonIncrementally(IncrementalCompilerRunner.kt:301)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compile(IncrementalCompilerRunner.kt:129)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.execIncrementalCompiler(CompileServiceImpl.kt:675)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.access$execIncrementalCompiler(CompileServiceImpl.kt:92)
	at org.jetbrains.kotlin.daemon.CompileServiceImpl.compile(CompileServiceImpl.kt:1660)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:587)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:828)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:705)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:704)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: Exception while generating code for:
FUN name:setV2c visibility:public modality:OPEN <> ($this:org.opencv.core.AtableUByte, v:org.opencv.core.Mat.Tuple2<kotlin.UByte>) returnType:kotlin.Unit
  overridden:
    public abstract fun setV2c (v: @[FlexibleNullability] org.opencv.core.Mat.Tuple2<@[FlexibleNullability] T of org.opencv.core.Mat.Atable?>?): kotlin.Unit declared in org.opencv.core.Mat.Atable
  $this: VALUE_PARAMETER name:<this> type:org.opencv.core.AtableUByte
  VALUE_PARAMETER name:v index:0 type:org.opencv.core.Mat.Tuple2<kotlin.UByte>
  BLOCK_BODY
    VAR name:data type:kotlin.UByteArray [val]
      CALL 'public final fun <unsafe-coerce> <T, R> (v: T of kotlin.jvm.internal.<unsafe-coerce>): R of kotlin.jvm.internal.<unsafe-coerce> declared in kotlin.jvm.internal' type=kotlin.UByteArray origin=null
        <T>: kotlin.ByteArray
        <R>: kotlin.UByteArray
        v: BLOCK type=kotlin.ByteArray origin=null
          VAR IR_TEMPORARY_VARIABLE name:tmp0 type:kotlin.ByteArray [val]
            CONSTRUCTOR_CALL 'public constructor <init> (size: kotlin.Int) [primary] declared in kotlin.ByteArray' type=kotlin.ByteArray origin=null
              size: CONST Int type=kotlin.Int value=2
          CALL 'public final fun set (index: kotlin.Int, value: kotlin.Byte): kotlin.Unit [operator] declared in kotlin.ByteArray' type=kotlin.Unit origin=null
            $this: GET_VAR 'val tmp0: kotlin.ByteArray [val] declared in org.opencv.core.AtableUByte.setV2c' type=kotlin.ByteArray origin=null
            index: CONST Int type=kotlin.Int value=0
            value: CALL 'public final fun <unsafe-coerce> <T, R> (v: T of kotlin.jvm.internal.<unsafe-coerce>): R of kotlin.jvm.internal.<unsafe-coerce> declared in kotlin.jvm.internal' type=kotlin.Byte origin=null
              <T>: @[FlexibleNullability] kotlin.UByte?
              <R>: kotlin.Byte
              v: CALL 'public open fun get_0 (): @[FlexibleNullability] T of org.opencv.core.Mat.Tuple2? declared in org.opencv.core.Mat.Tuple2' type=@[FlexibleNullability] kotlin.UByte? origin=GET_PROPERTY
                $this: GET_VAR 'v: org.opencv.core.Mat.Tuple2<kotlin.UByte> declared in org.opencv.core.AtableUByte.setV2c' type=org.opencv.core.Mat.Tuple2<kotlin.UByte> origin=null
          CALL 'public final fun set (index: kotlin.Int, value: kotlin.Byte): kotlin.Unit [operator] declared in kotlin.ByteArray' type=kotlin.Unit origin=null
            $this: GET_VAR 'val tmp0: kotlin.ByteArray [val] declared in org.opencv.core.AtableUByte.setV2c' type=kotlin.ByteArray origin=null
            index: CONST Int type=kotlin.Int value=1
            value: CALL 'public final fun <unsafe-coerce> <T, R> (v: T of kotlin.jvm.internal.<unsafe-coerce>): R of kotlin.jvm.internal.<unsafe-coerce> declared in kotlin.jvm.internal' type=kotlin.Byte origin=null
              <T>: @[FlexibleNullability] kotlin.UByte?
              <R>: kotlin.Byte
              v: CALL 'public open fun get_1 (): @[FlexibleNullability] T of org.opencv.core.Mat.Tuple2? declared in org.opencv.core.Mat.Tuple2' type=@[FlexibleNullability] kotlin.UByte? origin=GET_PROPERTY
                $this: GET_VAR 'v: org.opencv.core.Mat.Tuple2<kotlin.UByte> declared in org.opencv.core.AtableUByte.setV2c' type=org.opencv.core.Mat.Tuple2<kotlin.UByte> origin=null
          GET_VAR 'val tmp0: kotlin.ByteArray [val] declared in org.opencv.core.AtableUByte.setV2c' type=kotlin.ByteArray origin=null
    COMPOSITE type=kotlin.Unit origin=null
      CALL 'public final fun put-7tiRaYo (indices: kotlin.IntArray, data: kotlin.UByteArray): kotlin.Int declared in org.opencv.core.MatAtKt' type=kotlin.Int origin=null
        $receiver: GET_FIELD 'FIELD PROPERTY_BACKING_FIELD name:mat type:org.opencv.core.Mat visibility:private [final]' type=org.opencv.core.Mat origin=null
          receiver: GET_VAR '<this>: org.opencv.core.AtableUByte declared in org.opencv.core.AtableUByte.setV2c' type=org.opencv.core.AtableUByte origin=null
        indices: GET_FIELD 'FIELD PROPERTY_BACKING_FIELD name:indices type:kotlin.IntArray visibility:private [final]' type=kotlin.IntArray origin=null
          receiver: GET_VAR '<this>: org.opencv.core.AtableUByte declared in org.opencv.core.AtableUByte.setV2c' type=org.opencv.core.AtableUByte origin=null
        data: GET_VAR 'val data: kotlin.UByteArray [val] declared in org.opencv.core.AtableUByte.setV2c' type=kotlin.UByteArray origin=null
      COMPOSITE type=kotlin.Unit origin=null

	at org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.generate(FunctionCodegen.kt:47)
	at org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.generate$default(FunctionCodegen.kt:40)
	at org.jetbrains.kotlin.backend.jvm.codegen.ClassCodegen.generateMethodNode(ClassCodegen.kt:406)
	at org.jetbrains.kotlin.backend.jvm.codegen.ClassCodegen.generateMethod(ClassCodegen.kt:423)
	at org.jetbrains.kotlin.backend.jvm.codegen.ClassCodegen.generate(ClassCodegen.kt:168)
	at org.jetbrains.kotlin.backend.jvm.FileCodegen.lower(JvmPhases.kt:39)
	at org.jetbrains.kotlin.backend.common.phaser.PhaseFactoriesKt.createFilePhase$lambda$4(PhaseFactories.kt:71)
	at org.jetbrains.kotlin.backend.common.phaser.PhaseBuildersKt$createSimpleNamedCompilerPhase$1.phaseBody(PhaseBuilders.kt:69)
	at org.jetbrains.kotlin.backend.common.phaser.SimpleNamedCompilerPhase.phaseBody(CompilerPhase.kt:226)
	at org.jetbrains.kotlin.backend.common.phaser.AbstractNamedCompilerPhase.invoke(CompilerPhase.kt:113)
	at org.jetbrains.kotlin.backend.common.phaser.PerformByIrFilePhase.invokeSequential(performByIrFile.kt:62)
	... 45 more
Caused by: java.lang.IllegalArgumentException: Inline class types should have the same representation: Lkotlin/UByte; != B
	at org.jetbrains.kotlin.backend.jvm.intrinsics.UnsafeCoerce.invoke(UnsafeCoerce.kt:26)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:600)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:138)
	at org.jetbrains.kotlin.ir.expressions.IrCall.accept(IrCall.kt:24)
	at org.jetbrains.kotlin.backend.jvm.intrinsics.ArraySet.invoke(ArraySet.kt:32)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:600)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:138)
	at org.jetbrains.kotlin.ir.expressions.IrCall.accept(IrCall.kt:24)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitStatementContainer(ExpressionCodegen.kt:579)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitContainerExpression(ExpressionCodegen.kt:593)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitContainerExpression(ExpressionCodegen.kt:138)
	at org.jetbrains.kotlin.ir.visitors.IrElementVisitor$DefaultImpls.visitBlock(IrElementVisitor.kt:122)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitBlock(ExpressionCodegen.kt:413)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitBlock(ExpressionCodegen.kt:138)
	at org.jetbrains.kotlin.ir.expressions.IrBlock.accept(IrBlock.kt:18)
	at org.jetbrains.kotlin.backend.jvm.intrinsics.UnsafeCoerce.invoke(UnsafeCoerce.kt:30)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:600)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitCall(ExpressionCodegen.kt:138)
	at org.jetbrains.kotlin.ir.expressions.IrCall.accept(IrCall.kt:24)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitVariable(ExpressionCodegen.kt:790)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitVariable(ExpressionCodegen.kt:138)
	at org.jetbrains.kotlin.ir.declarations.IrVariable.accept(IrVariable.kt:36)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitStatementContainer(ExpressionCodegen.kt:579)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitBlockBody(ExpressionCodegen.kt:584)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.visitBlockBody(ExpressionCodegen.kt:138)
	at org.jetbrains.kotlin.ir.expressions.IrBlockBody.accept(IrBlockBody.kt:20)
	at org.jetbrains.kotlin.backend.jvm.codegen.ExpressionCodegen.generate(ExpressionCodegen.kt:240)
	at org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.doGenerate(FunctionCodegen.kt:123)
	at org.jetbrains.kotlin.backend.jvm.codegen.FunctionCodegen.generate(FunctionCodegen.kt:44)
	... 55 more


