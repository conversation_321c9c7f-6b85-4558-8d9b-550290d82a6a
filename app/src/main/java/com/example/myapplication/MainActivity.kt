package com.example.myapplication

import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.myapplication.ui.theme.MyApplicationTheme
import java.io.InputStream

class MainActivity : ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            MyApplicationTheme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    ImagePickerScreen(
                        modifier = Modifier.padding(innerPadding)
                    )
                }
            }
        }
    }
}

@Composable
fun ImagePickerScreen(modifier: Modifier = Modifier) {
    val context = LocalContext.current
    var image1Uri by remember { mutableStateOf<Uri?>(null) }
    var image2Uri by remember { mutableStateOf<Uri?>(null) }
    var image1Bitmap by remember { mutableStateOf<Bitmap?>(null) }
    var image2Bitmap by remember { mutableStateOf<Bitmap?>(null) }
    var resultText by remember { mutableStateOf("Chọn 2 hình ảnh để so sánh thông tin") }

    val launcher1 = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        image1Uri = uri
        uri?.let {
            try {
                val inputStream: InputStream? = context.contentResolver.openInputStream(it)
                image1Bitmap = BitmapFactory.decodeStream(inputStream)
                inputStream?.close()
            } catch (e: Exception) {
                Log.e("ImagePicker", "Error loading image 1: ${e.message}")
            }
        }
    }

    val launcher2 = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        image2Uri = uri
        uri?.let {
            try {
                val inputStream: InputStream? = context.contentResolver.openInputStream(it)
                image2Bitmap = BitmapFactory.decodeStream(inputStream)
                inputStream?.close()
            } catch (e: Exception) {
                Log.e("ImagePicker", "Error loading image 2: ${e.message}")
            }
        }
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "Image Picker Demo",
            style = MaterialTheme.typography.headlineMedium
        )

        // Button để chọn ảnh 1
        Button(
            onClick = { launcher1.launch("image/*") },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Chọn ảnh 1")
        }

        // Hiển thị ảnh 1
        image1Bitmap?.let { bitmap ->
            Image(
                bitmap = bitmap.asImageBitmap(),
                contentDescription = "Image 1",
                modifier = Modifier
                    .size(200.dp)
                    .padding(8.dp)
            )
        }

        // Button để chọn ảnh 2
        Button(
            onClick = { launcher2.launch("image/*") },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Chọn ảnh 2")
        }

        // Hiển thị ảnh 2
        image2Bitmap?.let { bitmap ->
            Image(
                bitmap = bitmap.asImageBitmap(),
                contentDescription = "Image 2",
                modifier = Modifier
                    .size(200.dp)
                    .padding(8.dp)
            )
        }

        // Button để tính toán phase correlation
        Button(
            onClick = {
                if (image1Bitmap != null && image2Bitmap != null) {
                    try {
                        val result = calculateImageInfo(image1Bitmap!!, image2Bitmap!!)
                        resultText = result
                    } catch (e: Exception) {
                        resultText = "Lỗi: ${e.message}"
                        Log.e("ImageInfo", "Error: ${e.message}")
                    }
                } else {
                    resultText = "Vui lòng chọn cả 2 hình ảnh"
                }
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = image1Bitmap != null && image2Bitmap != null
        ) {
            Text("So sánh thông tin hình ảnh")
        }

        // Hiển thị kết quả
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = resultText,
                modifier = Modifier.padding(16.dp),
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

fun calculateImageInfo(bitmap1: Bitmap, bitmap2: Bitmap): String {
    try {
        // Lấy thông tin cơ bản về hình ảnh
        val width1 = bitmap1.width
        val height1 = bitmap1.height
        val width2 = bitmap2.width
        val height2 = bitmap2.height

        // Tính toán một số thông tin đơn giản
        val area1 = width1 * height1
        val area2 = width2 * height2
        val aspectRatio1 = width1.toFloat() / height1.toFloat()
        val aspectRatio2 = width2.toFloat() / height2.toFloat()

        // Tính toán sự khác biệt
        val sizeDifference = kotlin.math.abs(area1 - area2)
        val aspectRatioDifference = kotlin.math.abs(aspectRatio1 - aspectRatio2)

        Log.d("ImageInfo", "Image 1: ${width1}x${height1}, Image 2: ${width2}x${height2}")

        return """
            Thông tin hình ảnh:

            Ảnh 1: ${width1} x ${height1} pixels
            Diện tích: ${area1} pixels
            Tỷ lệ khung hình: ${String.format("%.2f", aspectRatio1)}

            Ảnh 2: ${width2} x ${height2} pixels
            Diện tích: ${area2} pixels
            Tỷ lệ khung hình: ${String.format("%.2f", aspectRatio2)}

            So sánh:
            Chênh lệch diện tích: ${sizeDifference} pixels
            Chênh lệch tỷ lệ: ${String.format("%.3f", aspectRatioDifference)}

            Lưu ý: Để sử dụng Phase Correlation,
            cần cài đặt OpenCV library.
        """.trimIndent()

    } catch (e: Exception) {
        Log.e("ImageInfo", "Error in calculateImageInfo: ${e.message}")
        throw e
    }
}

@Preview(showBackground = true)
@Composable
fun ImagePickerPreview() {
    MyApplicationTheme {
        ImagePickerScreen()
    }
}