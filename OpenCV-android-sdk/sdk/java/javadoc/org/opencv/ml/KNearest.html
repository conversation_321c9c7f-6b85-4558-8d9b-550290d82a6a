<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:24 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>KNearest (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="KNearest (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":9,"i10":10,"i11":10,"i12":10,"i13":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/ml/EM.html" title="class in org.opencv.ml"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/ml/LogisticRegression.html" title="class in org.opencv.ml"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/KNearest.html" target="_top">Frames</a></li>
<li><a href="KNearest.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.ml</div>
<h2 title="Class KNearest" class="title">Class KNearest</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">org.opencv.ml.StatModel</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.ml.KNearest</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">KNearest</span>
extends <a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></pre>
<div class="block">The class implements K-Nearest Neighbors model

 SEE: REF: ml_intro_knn</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/KNearest.html#BRUTE_FORCE">BRUTE_FORCE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/KNearest.html#KDTREE">KDTREE</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.opencv.ml.StatModel">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="../../../org/opencv/ml/StatModel.html#COMPRESSED_INPUT">COMPRESSED_INPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#PREPROCESSED_INPUT">PREPROCESSED_INPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#RAW_OUTPUT">RAW_OUTPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#UPDATE_MODEL">UPDATE_MODEL</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/KNearest.html" title="class in org.opencv.ml">KNearest</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/KNearest.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/KNearest.html" title="class in org.opencv.ml">KNearest</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/KNearest.html#create--">create</a></span>()</code>
<div class="block">Creates the empty model

     The static method creates empty %KNearest classifier.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/KNearest.html#findNearest-org.opencv.core.Mat-int-org.opencv.core.Mat-">findNearest</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
           int&nbsp;k,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results)</code>
<div class="block">Finds the neighbors and predicts responses for input vectors.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/KNearest.html#findNearest-org.opencv.core.Mat-int-org.opencv.core.Mat-org.opencv.core.Mat-">findNearest</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
           int&nbsp;k,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;neighborResponses)</code>
<div class="block">Finds the neighbors and predicts responses for input vectors.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/KNearest.html#findNearest-org.opencv.core.Mat-int-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">findNearest</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
           int&nbsp;k,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;neighborResponses,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dist)</code>
<div class="block">Finds the neighbors and predicts responses for input vectors.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/KNearest.html#getAlgorithmType--">getAlgorithmType</a></span>()</code>
<div class="block">SEE: setAlgorithmType</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/KNearest.html#getDefaultK--">getDefaultK</a></span>()</code>
<div class="block">SEE: setDefaultK</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/KNearest.html#getEmax--">getEmax</a></span>()</code>
<div class="block">SEE: setEmax</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/KNearest.html#getIsClassifier--">getIsClassifier</a></span>()</code>
<div class="block">SEE: setIsClassifier</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/KNearest.html" title="class in org.opencv.ml">KNearest</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/KNearest.html#load-java.lang.String-">load</a></span>(java.lang.String&nbsp;filepath)</code>
<div class="block">Loads and creates a serialized knearest from a file

 Use KNearest::save to serialize and store an KNearest to disk.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/KNearest.html#setAlgorithmType-int-">setAlgorithmType</a></span>(int&nbsp;val)</code>
<div class="block">getAlgorithmType SEE: getAlgorithmType</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/KNearest.html#setDefaultK-int-">setDefaultK</a></span>(int&nbsp;val)</code>
<div class="block">getDefaultK SEE: getDefaultK</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/KNearest.html#setEmax-int-">setEmax</a></span>(int&nbsp;val)</code>
<div class="block">getEmax SEE: getEmax</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/KNearest.html#setIsClassifier-boolean-">setIsClassifier</a></span>(boolean&nbsp;val)</code>
<div class="block">getIsClassifier SEE: getIsClassifier</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.ml.StatModel">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="../../../org/opencv/ml/StatModel.html#calcError-org.opencv.ml.TrainData-boolean-org.opencv.core.Mat-">calcError</a>, <a href="../../../org/opencv/ml/StatModel.html#empty--">empty</a>, <a href="../../../org/opencv/ml/StatModel.html#getVarCount--">getVarCount</a>, <a href="../../../org/opencv/ml/StatModel.html#isClassifier--">isClassifier</a>, <a href="../../../org/opencv/ml/StatModel.html#isTrained--">isTrained</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-int-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.core.Mat-int-org.opencv.core.Mat-">train</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.ml.TrainData-">train</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.ml.TrainData-int-">train</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="BRUTE_FORCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BRUTE_FORCE</h4>
<pre>public static final&nbsp;int BRUTE_FORCE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.KNearest.BRUTE_FORCE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="KDTREE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>KDTREE</h4>
<pre>public static final&nbsp;int KDTREE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.KNearest.KDTREE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/KNearest.html" title="class in org.opencv.ml">KNearest</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/KNearest.html" title="class in org.opencv.ml">KNearest</a>&nbsp;create()</pre>
<div class="block">Creates the empty model

     The static method creates empty %KNearest classifier. It should be then trained using StatModel::train method.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="findNearest-org.opencv.core.Mat-int-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findNearest</h4>
<pre>public&nbsp;float&nbsp;findNearest(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                         int&nbsp;k,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results)</pre>
<div class="block">Finds the neighbors and predicts responses for input vectors.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - Input samples stored by rows. It is a single-precision floating-point matrix of
         <code>&amp;lt;number_of_samples&amp;gt; * k</code> size.</dd>
<dd><code>k</code> - Number of used nearest neighbors. Should be greater than 1.</dd>
<dd><code>results</code> - Vector with results of prediction (regression or classification) for each input
         sample. It is a single-precision floating-point vector with <code>&amp;lt;number_of_samples&amp;gt;</code> elements.
         precision floating-point matrix of <code>&amp;lt;number_of_samples&amp;gt; * k</code> size.
         is a single-precision floating-point matrix of <code>&amp;lt;number_of_samples&amp;gt; * k</code> size.

     For each input vector (a row of the matrix samples), the method finds the k nearest neighbors.
     In case of regression, the predicted result is a mean value of the particular vector's neighbor
     responses. In case of classification, the class is determined by voting.

     For each input vector, the neighbors are sorted by their distances to the vector.

     In case of C++ interface you can use output pointers to empty matrices and the function will
     allocate memory itself.

     If only a single input vector is passed, all output matrices are optional and the predicted
     value is returned by the method.

     The function is parallelized with the TBB library.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="findNearest-org.opencv.core.Mat-int-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findNearest</h4>
<pre>public&nbsp;float&nbsp;findNearest(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                         int&nbsp;k,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;neighborResponses)</pre>
<div class="block">Finds the neighbors and predicts responses for input vectors.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - Input samples stored by rows. It is a single-precision floating-point matrix of
         <code>&amp;lt;number_of_samples&amp;gt; * k</code> size.</dd>
<dd><code>k</code> - Number of used nearest neighbors. Should be greater than 1.</dd>
<dd><code>results</code> - Vector with results of prediction (regression or classification) for each input
         sample. It is a single-precision floating-point vector with <code>&amp;lt;number_of_samples&amp;gt;</code> elements.</dd>
<dd><code>neighborResponses</code> - Optional output values for corresponding neighbors. It is a single-
         precision floating-point matrix of <code>&amp;lt;number_of_samples&amp;gt; * k</code> size.
         is a single-precision floating-point matrix of <code>&amp;lt;number_of_samples&amp;gt; * k</code> size.

     For each input vector (a row of the matrix samples), the method finds the k nearest neighbors.
     In case of regression, the predicted result is a mean value of the particular vector's neighbor
     responses. In case of classification, the class is determined by voting.

     For each input vector, the neighbors are sorted by their distances to the vector.

     In case of C++ interface you can use output pointers to empty matrices and the function will
     allocate memory itself.

     If only a single input vector is passed, all output matrices are optional and the predicted
     value is returned by the method.

     The function is parallelized with the TBB library.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="findNearest-org.opencv.core.Mat-int-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findNearest</h4>
<pre>public&nbsp;float&nbsp;findNearest(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                         int&nbsp;k,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;neighborResponses,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dist)</pre>
<div class="block">Finds the neighbors and predicts responses for input vectors.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - Input samples stored by rows. It is a single-precision floating-point matrix of
         <code>&amp;lt;number_of_samples&amp;gt; * k</code> size.</dd>
<dd><code>k</code> - Number of used nearest neighbors. Should be greater than 1.</dd>
<dd><code>results</code> - Vector with results of prediction (regression or classification) for each input
         sample. It is a single-precision floating-point vector with <code>&amp;lt;number_of_samples&amp;gt;</code> elements.</dd>
<dd><code>neighborResponses</code> - Optional output values for corresponding neighbors. It is a single-
         precision floating-point matrix of <code>&amp;lt;number_of_samples&amp;gt; * k</code> size.</dd>
<dd><code>dist</code> - Optional output distances from the input vectors to the corresponding neighbors. It
         is a single-precision floating-point matrix of <code>&amp;lt;number_of_samples&amp;gt; * k</code> size.

     For each input vector (a row of the matrix samples), the method finds the k nearest neighbors.
     In case of regression, the predicted result is a mean value of the particular vector's neighbor
     responses. In case of classification, the class is determined by voting.

     For each input vector, the neighbors are sorted by their distances to the vector.

     In case of C++ interface you can use output pointers to empty matrices and the function will
     allocate memory itself.

     If only a single input vector is passed, all output matrices are optional and the predicted
     value is returned by the method.

     The function is parallelized with the TBB library.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getAlgorithmType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAlgorithmType</h4>
<pre>public&nbsp;int&nbsp;getAlgorithmType()</pre>
<div class="block">SEE: setAlgorithmType</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getDefaultK--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultK</h4>
<pre>public&nbsp;int&nbsp;getDefaultK()</pre>
<div class="block">SEE: setDefaultK</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getEmax--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEmax</h4>
<pre>public&nbsp;int&nbsp;getEmax()</pre>
<div class="block">SEE: setEmax</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getIsClassifier--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIsClassifier</h4>
<pre>public&nbsp;boolean&nbsp;getIsClassifier()</pre>
<div class="block">SEE: setIsClassifier</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="load-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>load</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/KNearest.html" title="class in org.opencv.ml">KNearest</a>&nbsp;load(java.lang.String&nbsp;filepath)</pre>
<div class="block">Loads and creates a serialized knearest from a file

 Use KNearest::save to serialize and store an KNearest to disk.
 Load the KNearest from this file again, by calling this function with the path to the file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filepath</code> - path to serialized KNearest</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setAlgorithmType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAlgorithmType</h4>
<pre>public&nbsp;void&nbsp;setAlgorithmType(int&nbsp;val)</pre>
<div class="block">getAlgorithmType SEE: getAlgorithmType</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setDefaultK-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultK</h4>
<pre>public&nbsp;void&nbsp;setDefaultK(int&nbsp;val)</pre>
<div class="block">getDefaultK SEE: getDefaultK</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setEmax-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEmax</h4>
<pre>public&nbsp;void&nbsp;setEmax(int&nbsp;val)</pre>
<div class="block">getEmax SEE: getEmax</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setIsClassifier-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setIsClassifier</h4>
<pre>public&nbsp;void&nbsp;setIsClassifier(boolean&nbsp;val)</pre>
<div class="block">getIsClassifier SEE: getIsClassifier</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/ml/EM.html" title="class in org.opencv.ml"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/ml/LogisticRegression.html" title="class in org.opencv.ml"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/KNearest.html" target="_top">Frames</a></li>
<li><a href="KNearest.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
