<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:25 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.opencv.ml (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../org/opencv/ml/package-summary.html" target="classFrame">org.opencv.ml</a></h1>
<div class="indexContainer">
<h2 title="Classes">Classes</h2>
<ul title="Classes">
<li><a href="ANN_MLP.html" title="class in org.opencv.ml" target="classFrame">ANN_MLP</a></li>
<li><a href="Boost.html" title="class in org.opencv.ml" target="classFrame">Boost</a></li>
<li><a href="DTrees.html" title="class in org.opencv.ml" target="classFrame">DTrees</a></li>
<li><a href="EM.html" title="class in org.opencv.ml" target="classFrame">EM</a></li>
<li><a href="KNearest.html" title="class in org.opencv.ml" target="classFrame">KNearest</a></li>
<li><a href="LogisticRegression.html" title="class in org.opencv.ml" target="classFrame">LogisticRegression</a></li>
<li><a href="Ml.html" title="class in org.opencv.ml" target="classFrame">Ml</a></li>
<li><a href="NormalBayesClassifier.html" title="class in org.opencv.ml" target="classFrame">NormalBayesClassifier</a></li>
<li><a href="ParamGrid.html" title="class in org.opencv.ml" target="classFrame">ParamGrid</a></li>
<li><a href="RTrees.html" title="class in org.opencv.ml" target="classFrame">RTrees</a></li>
<li><a href="StatModel.html" title="class in org.opencv.ml" target="classFrame">StatModel</a></li>
<li><a href="SVM.html" title="class in org.opencv.ml" target="classFrame">SVM</a></li>
<li><a href="SVMSGD.html" title="class in org.opencv.ml" target="classFrame">SVMSGD</a></li>
<li><a href="TrainData.html" title="class in org.opencv.ml" target="classFrame">TrainData</a></li>
</ul>
</div>
</body>
</html>
