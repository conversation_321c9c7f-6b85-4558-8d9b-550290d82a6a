<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:24 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>LogisticRegression (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="LogisticRegression (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":9,"i10":9,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/ml/KNearest.html" title="class in org.opencv.ml"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/ml/Ml.html" title="class in org.opencv.ml"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/LogisticRegression.html" target="_top">Frames</a></li>
<li><a href="LogisticRegression.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.ml</div>
<h2 title="Class LogisticRegression" class="title">Class LogisticRegression</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">org.opencv.ml.StatModel</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.ml.LogisticRegression</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">LogisticRegression</span>
extends <a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></pre>
<div class="block">Implements Logistic Regression classifier.

 SEE: REF: ml_intro_lr</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#BATCH">BATCH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#MINI_BATCH">MINI_BATCH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#REG_DISABLE">REG_DISABLE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#REG_L1">REG_L1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#REG_L2">REG_L2</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.opencv.ml.StatModel">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="../../../org/opencv/ml/StatModel.html#COMPRESSED_INPUT">COMPRESSED_INPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#PREPROCESSED_INPUT">PREPROCESSED_INPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#RAW_OUTPUT">RAW_OUTPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#UPDATE_MODEL">UPDATE_MODEL</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/LogisticRegression.html" title="class in org.opencv.ml">LogisticRegression</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/LogisticRegression.html" title="class in org.opencv.ml">LogisticRegression</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#create--">create</a></span>()</code>
<div class="block">Creates empty model.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#get_learnt_thetas--">get_learnt_thetas</a></span>()</code>
<div class="block">This function returns the trained parameters arranged across rows.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#getIterations--">getIterations</a></span>()</code>
<div class="block">SEE: setIterations</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#getLearningRate--">getLearningRate</a></span>()</code>
<div class="block">SEE: setLearningRate</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#getMiniBatchSize--">getMiniBatchSize</a></span>()</code>
<div class="block">SEE: setMiniBatchSize</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#getRegularization--">getRegularization</a></span>()</code>
<div class="block">SEE: setRegularization</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#getTermCriteria--">getTermCriteria</a></span>()</code>
<div class="block">SEE: setTermCriteria</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#getTrainMethod--">getTrainMethod</a></span>()</code>
<div class="block">SEE: setTrainMethod</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/LogisticRegression.html" title="class in org.opencv.ml">LogisticRegression</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#load-java.lang.String-">load</a></span>(java.lang.String&nbsp;filepath)</code>
<div class="block">Loads and creates a serialized LogisticRegression from a file

 Use LogisticRegression::save to serialize and store an LogisticRegression to disk.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/LogisticRegression.html" title="class in org.opencv.ml">LogisticRegression</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#load-java.lang.String-java.lang.String-">load</a></span>(java.lang.String&nbsp;filepath,
    java.lang.String&nbsp;nodeName)</code>
<div class="block">Loads and creates a serialized LogisticRegression from a file

 Use LogisticRegression::save to serialize and store an LogisticRegression to disk.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#predict-org.opencv.core.Mat-">predict</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples)</code>
<div class="block">Predicts responses for input samples and returns a float type.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-">predict</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results)</code>
<div class="block">Predicts responses for input samples and returns a float type.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-int-">predict</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results,
       int&nbsp;flags)</code>
<div class="block">Predicts responses for input samples and returns a float type.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#setIterations-int-">setIterations</a></span>(int&nbsp;val)</code>
<div class="block">getIterations SEE: getIterations</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#setLearningRate-double-">setLearningRate</a></span>(double&nbsp;val)</code>
<div class="block">getLearningRate SEE: getLearningRate</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#setMiniBatchSize-int-">setMiniBatchSize</a></span>(int&nbsp;val)</code>
<div class="block">getMiniBatchSize SEE: getMiniBatchSize</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#setRegularization-int-">setRegularization</a></span>(int&nbsp;val)</code>
<div class="block">getRegularization SEE: getRegularization</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#setTermCriteria-org.opencv.core.TermCriteria-">setTermCriteria</a></span>(<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</code>
<div class="block">getTermCriteria SEE: getTermCriteria</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/LogisticRegression.html#setTrainMethod-int-">setTrainMethod</a></span>(int&nbsp;val)</code>
<div class="block">getTrainMethod SEE: getTrainMethod</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.ml.StatModel">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="../../../org/opencv/ml/StatModel.html#calcError-org.opencv.ml.TrainData-boolean-org.opencv.core.Mat-">calcError</a>, <a href="../../../org/opencv/ml/StatModel.html#empty--">empty</a>, <a href="../../../org/opencv/ml/StatModel.html#getVarCount--">getVarCount</a>, <a href="../../../org/opencv/ml/StatModel.html#isClassifier--">isClassifier</a>, <a href="../../../org/opencv/ml/StatModel.html#isTrained--">isTrained</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.core.Mat-int-org.opencv.core.Mat-">train</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.ml.TrainData-">train</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.ml.TrainData-int-">train</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="BATCH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BATCH</h4>
<pre>public static final&nbsp;int BATCH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.LogisticRegression.BATCH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MINI_BATCH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MINI_BATCH</h4>
<pre>public static final&nbsp;int MINI_BATCH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.LogisticRegression.MINI_BATCH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="REG_DISABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REG_DISABLE</h4>
<pre>public static final&nbsp;int REG_DISABLE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.LogisticRegression.REG_DISABLE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="REG_L1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REG_L1</h4>
<pre>public static final&nbsp;int REG_L1</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.LogisticRegression.REG_L1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="REG_L2">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>REG_L2</h4>
<pre>public static final&nbsp;int REG_L2</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.LogisticRegression.REG_L2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/LogisticRegression.html" title="class in org.opencv.ml">LogisticRegression</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/LogisticRegression.html" title="class in org.opencv.ml">LogisticRegression</a>&nbsp;create()</pre>
<div class="block">Creates empty model.

     Creates Logistic Regression model with parameters given.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="get_learnt_thetas--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_learnt_thetas</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;get_learnt_thetas()</pre>
<div class="block">This function returns the trained parameters arranged across rows.

     For a two class classification problem, it returns a row matrix. It returns learnt parameters of
     the Logistic Regression as a matrix of type CV_32F.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getIterations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIterations</h4>
<pre>public&nbsp;int&nbsp;getIterations()</pre>
<div class="block">SEE: setIterations</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getLearningRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLearningRate</h4>
<pre>public&nbsp;double&nbsp;getLearningRate()</pre>
<div class="block">SEE: setLearningRate</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getMiniBatchSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMiniBatchSize</h4>
<pre>public&nbsp;int&nbsp;getMiniBatchSize()</pre>
<div class="block">SEE: setMiniBatchSize</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getRegularization--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRegularization</h4>
<pre>public&nbsp;int&nbsp;getRegularization()</pre>
<div class="block">SEE: setRegularization</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getTermCriteria--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTermCriteria</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;getTermCriteria()</pre>
<div class="block">SEE: setTermCriteria</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getTrainMethod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrainMethod</h4>
<pre>public&nbsp;int&nbsp;getTrainMethod()</pre>
<div class="block">SEE: setTrainMethod</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="load-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>load</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/LogisticRegression.html" title="class in org.opencv.ml">LogisticRegression</a>&nbsp;load(java.lang.String&nbsp;filepath)</pre>
<div class="block">Loads and creates a serialized LogisticRegression from a file

 Use LogisticRegression::save to serialize and store an LogisticRegression to disk.
 Load the LogisticRegression from this file again, by calling this function with the path to the file.
 Optionally specify the node for the file containing the classifier</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filepath</code> - path to serialized LogisticRegression</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="load-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>load</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/LogisticRegression.html" title="class in org.opencv.ml">LogisticRegression</a>&nbsp;load(java.lang.String&nbsp;filepath,
                                      java.lang.String&nbsp;nodeName)</pre>
<div class="block">Loads and creates a serialized LogisticRegression from a file

 Use LogisticRegression::save to serialize and store an LogisticRegression to disk.
 Load the LogisticRegression from this file again, by calling this function with the path to the file.
 Optionally specify the node for the file containing the classifier</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filepath</code> - path to serialized LogisticRegression</dd>
<dd><code>nodeName</code> - name of node containing the classifier</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="predict-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>predict</h4>
<pre>public&nbsp;float&nbsp;predict(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples)</pre>
<div class="block">Predicts responses for input samples and returns a float type.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-">predict</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - The input data for the prediction algorithm. Matrix [m x n], where each row
         contains variables (features) of one object being classified. Should have data type CV_32F.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="predict-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>predict</h4>
<pre>public&nbsp;float&nbsp;predict(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results)</pre>
<div class="block">Predicts responses for input samples and returns a float type.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-">predict</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - The input data for the prediction algorithm. Matrix [m x n], where each row
         contains variables (features) of one object being classified. Should have data type CV_32F.</dd>
<dd><code>results</code> - Predicted labels as a column matrix of type CV_32S.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="predict-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>predict</h4>
<pre>public&nbsp;float&nbsp;predict(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;results,
                     int&nbsp;flags)</pre>
<div class="block">Predicts responses for input samples and returns a float type.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-int-">predict</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - The input data for the prediction algorithm. Matrix [m x n], where each row
         contains variables (features) of one object being classified. Should have data type CV_32F.</dd>
<dd><code>results</code> - Predicted labels as a column matrix of type CV_32S.</dd>
<dd><code>flags</code> - Not used.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setIterations-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIterations</h4>
<pre>public&nbsp;void&nbsp;setIterations(int&nbsp;val)</pre>
<div class="block">getIterations SEE: getIterations</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setLearningRate-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLearningRate</h4>
<pre>public&nbsp;void&nbsp;setLearningRate(double&nbsp;val)</pre>
<div class="block">getLearningRate SEE: getLearningRate</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setMiniBatchSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMiniBatchSize</h4>
<pre>public&nbsp;void&nbsp;setMiniBatchSize(int&nbsp;val)</pre>
<div class="block">getMiniBatchSize SEE: getMiniBatchSize</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setRegularization-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRegularization</h4>
<pre>public&nbsp;void&nbsp;setRegularization(int&nbsp;val)</pre>
<div class="block">getRegularization SEE: getRegularization</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setTermCriteria-org.opencv.core.TermCriteria-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTermCriteria</h4>
<pre>public&nbsp;void&nbsp;setTermCriteria(<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</pre>
<div class="block">getTermCriteria SEE: getTermCriteria</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setTrainMethod-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setTrainMethod</h4>
<pre>public&nbsp;void&nbsp;setTrainMethod(int&nbsp;val)</pre>
<div class="block">getTrainMethod SEE: getTrainMethod</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/ml/KNearest.html" title="class in org.opencv.ml"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/ml/Ml.html" title="class in org.opencv.ml"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/LogisticRegression.html" target="_top">Frames</a></li>
<li><a href="LogisticRegression.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
