<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:24 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>SVMSGD (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SVMSGD (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":9,"i11":9,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/ml/SVM.html" title="class in org.opencv.ml"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/ml/TrainData.html" title="class in org.opencv.ml"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/SVMSGD.html" target="_top">Frames</a></li>
<li><a href="SVMSGD.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.ml</div>
<h2 title="Class SVMSGD" class="title">Class SVMSGD</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">org.opencv.ml.StatModel</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.ml.SVMSGD</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">SVMSGD</span>
extends <a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></pre>
<div class="block">*************************************************************************************\
 Stochastic Gradient Descent SVM Classifier                      *
 \***************************************************************************************</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#ASGD">ASGD</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#HARD_MARGIN">HARD_MARGIN</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#SGD">SGD</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#SOFT_MARGIN">SOFT_MARGIN</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.opencv.ml.StatModel">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="../../../org/opencv/ml/StatModel.html#COMPRESSED_INPUT">COMPRESSED_INPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#PREPROCESSED_INPUT">PREPROCESSED_INPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#RAW_OUTPUT">RAW_OUTPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#UPDATE_MODEL">UPDATE_MODEL</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/SVMSGD.html" title="class in org.opencv.ml">SVMSGD</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/SVMSGD.html" title="class in org.opencv.ml">SVMSGD</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#create--">create</a></span>()</code>
<div class="block">Creates empty model.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#getInitialStepSize--">getInitialStepSize</a></span>()</code>
<div class="block">SEE: setInitialStepSize</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#getMarginRegularization--">getMarginRegularization</a></span>()</code>
<div class="block">SEE: setMarginRegularization</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#getMarginType--">getMarginType</a></span>()</code>
<div class="block">SEE: setMarginType</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#getShift--">getShift</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#getStepDecreasingPower--">getStepDecreasingPower</a></span>()</code>
<div class="block">SEE: setStepDecreasingPower</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#getSvmsgdType--">getSvmsgdType</a></span>()</code>
<div class="block">SEE: setSvmsgdType</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#getTermCriteria--">getTermCriteria</a></span>()</code>
<div class="block">SEE: setTermCriteria</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#getWeights--">getWeights</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/SVMSGD.html" title="class in org.opencv.ml">SVMSGD</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#load-java.lang.String-">load</a></span>(java.lang.String&nbsp;filepath)</code>
<div class="block">Loads and creates a serialized SVMSGD from a file

 Use SVMSGD::save to serialize and store an SVMSGD to disk.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/SVMSGD.html" title="class in org.opencv.ml">SVMSGD</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#load-java.lang.String-java.lang.String-">load</a></span>(java.lang.String&nbsp;filepath,
    java.lang.String&nbsp;nodeName)</code>
<div class="block">Loads and creates a serialized SVMSGD from a file

 Use SVMSGD::save to serialize and store an SVMSGD to disk.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#setInitialStepSize-float-">setInitialStepSize</a></span>(float&nbsp;InitialStepSize)</code>
<div class="block">getInitialStepSize SEE: getInitialStepSize</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#setMarginRegularization-float-">setMarginRegularization</a></span>(float&nbsp;marginRegularization)</code>
<div class="block">getMarginRegularization SEE: getMarginRegularization</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#setMarginType-int-">setMarginType</a></span>(int&nbsp;marginType)</code>
<div class="block">getMarginType SEE: getMarginType</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#setOptimalParameters--">setOptimalParameters</a></span>()</code>
<div class="block">Function sets optimal parameters values for chosen SVM SGD model.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#setOptimalParameters-int-">setOptimalParameters</a></span>(int&nbsp;svmsgdType)</code>
<div class="block">Function sets optimal parameters values for chosen SVM SGD model.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#setOptimalParameters-int-int-">setOptimalParameters</a></span>(int&nbsp;svmsgdType,
                    int&nbsp;marginType)</code>
<div class="block">Function sets optimal parameters values for chosen SVM SGD model.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#setStepDecreasingPower-float-">setStepDecreasingPower</a></span>(float&nbsp;stepDecreasingPower)</code>
<div class="block">getStepDecreasingPower SEE: getStepDecreasingPower</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#setSvmsgdType-int-">setSvmsgdType</a></span>(int&nbsp;svmsgdType)</code>
<div class="block">getSvmsgdType SEE: getSvmsgdType</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/SVMSGD.html#setTermCriteria-org.opencv.core.TermCriteria-">setTermCriteria</a></span>(<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</code>
<div class="block">getTermCriteria SEE: getTermCriteria</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.ml.StatModel">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="../../../org/opencv/ml/StatModel.html#calcError-org.opencv.ml.TrainData-boolean-org.opencv.core.Mat-">calcError</a>, <a href="../../../org/opencv/ml/StatModel.html#empty--">empty</a>, <a href="../../../org/opencv/ml/StatModel.html#getVarCount--">getVarCount</a>, <a href="../../../org/opencv/ml/StatModel.html#isClassifier--">isClassifier</a>, <a href="../../../org/opencv/ml/StatModel.html#isTrained--">isTrained</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-int-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.core.Mat-int-org.opencv.core.Mat-">train</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.ml.TrainData-">train</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.ml.TrainData-int-">train</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="ASGD">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ASGD</h4>
<pre>public static final&nbsp;int ASGD</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVMSGD.ASGD">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HARD_MARGIN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HARD_MARGIN</h4>
<pre>public static final&nbsp;int HARD_MARGIN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVMSGD.HARD_MARGIN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SGD">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SGD</h4>
<pre>public static final&nbsp;int SGD</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVMSGD.SGD">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SOFT_MARGIN">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SOFT_MARGIN</h4>
<pre>public static final&nbsp;int SOFT_MARGIN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.SVMSGD.SOFT_MARGIN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/SVMSGD.html" title="class in org.opencv.ml">SVMSGD</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/SVMSGD.html" title="class in org.opencv.ml">SVMSGD</a>&nbsp;create()</pre>
<div class="block">Creates empty model.
 Use StatModel::train to train the model. Since %SVMSGD has several parameters, you may want to
 find the best parameters for your problem or use setOptimalParameters() to set some default parameters.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getInitialStepSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInitialStepSize</h4>
<pre>public&nbsp;float&nbsp;getInitialStepSize()</pre>
<div class="block">SEE: setInitialStepSize</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getMarginRegularization--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMarginRegularization</h4>
<pre>public&nbsp;float&nbsp;getMarginRegularization()</pre>
<div class="block">SEE: setMarginRegularization</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getMarginType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMarginType</h4>
<pre>public&nbsp;int&nbsp;getMarginType()</pre>
<div class="block">SEE: setMarginType</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getShift--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShift</h4>
<pre>public&nbsp;float&nbsp;getShift()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the shift of the trained model (decision function f(x) = weights * x + shift).</dd>
</dl>
</li>
</ul>
<a name="getStepDecreasingPower--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStepDecreasingPower</h4>
<pre>public&nbsp;float&nbsp;getStepDecreasingPower()</pre>
<div class="block">SEE: setStepDecreasingPower</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getSvmsgdType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSvmsgdType</h4>
<pre>public&nbsp;int&nbsp;getSvmsgdType()</pre>
<div class="block">SEE: setSvmsgdType</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getTermCriteria--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTermCriteria</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;getTermCriteria()</pre>
<div class="block">SEE: setTermCriteria</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getWeights--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWeights</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getWeights()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the weights of the trained model (decision function f(x) = weights * x + shift).</dd>
</dl>
</li>
</ul>
<a name="load-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>load</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/SVMSGD.html" title="class in org.opencv.ml">SVMSGD</a>&nbsp;load(java.lang.String&nbsp;filepath)</pre>
<div class="block">Loads and creates a serialized SVMSGD from a file

 Use SVMSGD::save to serialize and store an SVMSGD to disk.
 Load the SVMSGD from this file again, by calling this function with the path to the file.
 Optionally specify the node for the file containing the classifier</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filepath</code> - path to serialized SVMSGD</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="load-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>load</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/SVMSGD.html" title="class in org.opencv.ml">SVMSGD</a>&nbsp;load(java.lang.String&nbsp;filepath,
                          java.lang.String&nbsp;nodeName)</pre>
<div class="block">Loads and creates a serialized SVMSGD from a file

 Use SVMSGD::save to serialize and store an SVMSGD to disk.
 Load the SVMSGD from this file again, by calling this function with the path to the file.
 Optionally specify the node for the file containing the classifier</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filepath</code> - path to serialized SVMSGD</dd>
<dd><code>nodeName</code> - name of node containing the classifier</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setInitialStepSize-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInitialStepSize</h4>
<pre>public&nbsp;void&nbsp;setInitialStepSize(float&nbsp;InitialStepSize)</pre>
<div class="block">getInitialStepSize SEE: getInitialStepSize</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>InitialStepSize</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setMarginRegularization-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMarginRegularization</h4>
<pre>public&nbsp;void&nbsp;setMarginRegularization(float&nbsp;marginRegularization)</pre>
<div class="block">getMarginRegularization SEE: getMarginRegularization</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>marginRegularization</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setMarginType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMarginType</h4>
<pre>public&nbsp;void&nbsp;setMarginType(int&nbsp;marginType)</pre>
<div class="block">getMarginType SEE: getMarginType</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>marginType</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setOptimalParameters--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOptimalParameters</h4>
<pre>public&nbsp;void&nbsp;setOptimalParameters()</pre>
<div class="block">Function sets optimal parameters values for chosen SVM SGD model.</div>
</li>
</ul>
<a name="setOptimalParameters-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOptimalParameters</h4>
<pre>public&nbsp;void&nbsp;setOptimalParameters(int&nbsp;svmsgdType)</pre>
<div class="block">Function sets optimal parameters values for chosen SVM SGD model.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>svmsgdType</code> - is the type of SVMSGD classifier.</dd>
</dl>
</li>
</ul>
<a name="setOptimalParameters-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOptimalParameters</h4>
<pre>public&nbsp;void&nbsp;setOptimalParameters(int&nbsp;svmsgdType,
                                 int&nbsp;marginType)</pre>
<div class="block">Function sets optimal parameters values for chosen SVM SGD model.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>svmsgdType</code> - is the type of SVMSGD classifier.</dd>
<dd><code>marginType</code> - is the type of margin constraint.</dd>
</dl>
</li>
</ul>
<a name="setStepDecreasingPower-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStepDecreasingPower</h4>
<pre>public&nbsp;void&nbsp;setStepDecreasingPower(float&nbsp;stepDecreasingPower)</pre>
<div class="block">getStepDecreasingPower SEE: getStepDecreasingPower</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stepDecreasingPower</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setSvmsgdType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSvmsgdType</h4>
<pre>public&nbsp;void&nbsp;setSvmsgdType(int&nbsp;svmsgdType)</pre>
<div class="block">getSvmsgdType SEE: getSvmsgdType</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>svmsgdType</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setTermCriteria-org.opencv.core.TermCriteria-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setTermCriteria</h4>
<pre>public&nbsp;void&nbsp;setTermCriteria(<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</pre>
<div class="block">getTermCriteria SEE: getTermCriteria</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/ml/SVM.html" title="class in org.opencv.ml"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/ml/TrainData.html" title="class in org.opencv.ml"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/SVMSGD.html" target="_top">Frames</a></li>
<li><a href="SVMSGD.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
