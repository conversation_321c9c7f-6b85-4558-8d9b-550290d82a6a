<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:23 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>BFMatcher (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BFMatcher (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/BOWImgDescriptorExtractor.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/BFMatcher.html" target="_top">Frames</a></li>
<li><a href="BFMatcher.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.org.opencv.features2d.DescriptorMatcher">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.features2d</div>
<h2 title="Class BFMatcher" class="title">Class BFMatcher</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">org.opencv.features2d.DescriptorMatcher</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.features2d.BFMatcher</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">BFMatcher</span>
extends <a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></pre>
<div class="block">Brute-force descriptor matcher.

 For each descriptor in the first set, this matcher finds the closest descriptor in the second set
 by trying each one. This descriptor matcher supports masking permissible matches of descriptor
 sets.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.opencv.features2d.DescriptorMatcher">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.opencv.features2d.<a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></h3>
<code><a href="../../../org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE">BRUTEFORCE</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE_HAMMING">BRUTEFORCE_HAMMING</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE_HAMMINGLUT">BRUTEFORCE_HAMMINGLUT</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE_L1">BRUTEFORCE_L1</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE_SL2">BRUTEFORCE_SL2</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#FLANNBASED">FLANNBASED</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BFMatcher.html#BFMatcher--">BFMatcher</a></span>()</code>
<div class="block">Brute-force matcher constructor (obsolete).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BFMatcher.html#BFMatcher-int-">BFMatcher</a></span>(int&nbsp;normType)</code>
<div class="block">Brute-force matcher constructor (obsolete).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BFMatcher.html#BFMatcher-int-boolean-">BFMatcher</a></span>(int&nbsp;normType,
         boolean&nbsp;crossCheck)</code>
<div class="block">Brute-force matcher constructor (obsolete).</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/BFMatcher.html" title="class in org.opencv.features2d">BFMatcher</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BFMatcher.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/BFMatcher.html" title="class in org.opencv.features2d">BFMatcher</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BFMatcher.html#create--">create</a></span>()</code>
<div class="block">Brute-force matcher create method.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/BFMatcher.html" title="class in org.opencv.features2d">BFMatcher</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BFMatcher.html#create-int-">create</a></span>(int&nbsp;normType)</code>
<div class="block">Brute-force matcher create method.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/BFMatcher.html" title="class in org.opencv.features2d">BFMatcher</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BFMatcher.html#create-int-boolean-">create</a></span>(int&nbsp;normType,
      boolean&nbsp;crossCheck)</code>
<div class="block">Brute-force matcher create method.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.features2d.DescriptorMatcher">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.features2d.<a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></h3>
<code><a href="../../../org/opencv/features2d/DescriptorMatcher.html#add-java.util.List-">add</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#clear--">clear</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#clone--">clone</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#clone-boolean-">clone</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#create-java.lang.String-">create</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#empty--">empty</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#getTrainDescriptors--">getTrainDescriptors</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#isMaskSupported--">isMaskSupported</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#knnMatch-org.opencv.core.Mat-java.util.List-int-">knnMatch</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#knnMatch-org.opencv.core.Mat-java.util.List-int-java.util.List-">knnMatch</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#knnMatch-org.opencv.core.Mat-java.util.List-int-java.util.List-boolean-">knnMatch</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#knnMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-int-">knnMatch</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#knnMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-int-org.opencv.core.Mat-">knnMatch</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#knnMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-int-org.opencv.core.Mat-boolean-">knnMatch</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#match-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-">match</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#match-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-">match</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#match-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-">match</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#match-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-java.util.List-">match</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#radiusMatch-org.opencv.core.Mat-java.util.List-float-">radiusMatch</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#radiusMatch-org.opencv.core.Mat-java.util.List-float-java.util.List-">radiusMatch</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#radiusMatch-org.opencv.core.Mat-java.util.List-float-java.util.List-boolean-">radiusMatch</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#radiusMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-float-">radiusMatch</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#radiusMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-float-org.opencv.core.Mat-">radiusMatch</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#radiusMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-float-org.opencv.core.Mat-boolean-">radiusMatch</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#read-java.lang.String-">read</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#train--">train</a>, <a href="../../../org/opencv/features2d/DescriptorMatcher.html#write-java.lang.String-">write</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="BFMatcher--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BFMatcher</h4>
<pre>public&nbsp;BFMatcher()</pre>
<div class="block">Brute-force matcher constructor (obsolete). Please use BFMatcher.create()</div>
</li>
</ul>
<a name="BFMatcher-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BFMatcher</h4>
<pre>public&nbsp;BFMatcher(int&nbsp;normType)</pre>
<div class="block">Brute-force matcher constructor (obsolete). Please use BFMatcher.create()</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>normType</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="BFMatcher-int-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BFMatcher</h4>
<pre>public&nbsp;BFMatcher(int&nbsp;normType,
                 boolean&nbsp;crossCheck)</pre>
<div class="block">Brute-force matcher constructor (obsolete). Please use BFMatcher.create()</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>normType</code> - automatically generated</dd>
<dd><code>crossCheck</code> - automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/BFMatcher.html" title="class in org.opencv.features2d">BFMatcher</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/BFMatcher.html" title="class in org.opencv.features2d">BFMatcher</a>&nbsp;create()</pre>
<div class="block">Brute-force matcher create method.
     preferable choices for SIFT and SURF descriptors, NORM_HAMMING should be used with ORB, BRISK and
     BRIEF, NORM_HAMMING2 should be used with ORB when WTA_K==3 or 4 (see ORB::ORB constructor
     description).
     nearest neighbors for each query descriptor. If crossCheck==true, then the knnMatch() method with
     k=1 will only return pairs (i,j) such that for i-th query descriptor the j-th descriptor in the
     matcher's collection is the nearest and vice versa, i.e. the BFMatcher will only return consistent
     pairs. Such technique usually produces best results with minimal number of outliers when there are
     enough matches. This is alternative to the ratio test, used by D. Lowe in SIFT paper.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/BFMatcher.html" title="class in org.opencv.features2d">BFMatcher</a>&nbsp;create(int&nbsp;normType)</pre>
<div class="block">Brute-force matcher create method.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>normType</code> - One of NORM_L1, NORM_L2, NORM_HAMMING, NORM_HAMMING2. L1 and L2 norms are
     preferable choices for SIFT and SURF descriptors, NORM_HAMMING should be used with ORB, BRISK and
     BRIEF, NORM_HAMMING2 should be used with ORB when WTA_K==3 or 4 (see ORB::ORB constructor
     description).
     nearest neighbors for each query descriptor. If crossCheck==true, then the knnMatch() method with
     k=1 will only return pairs (i,j) such that for i-th query descriptor the j-th descriptor in the
     matcher's collection is the nearest and vice versa, i.e. the BFMatcher will only return consistent
     pairs. Such technique usually produces best results with minimal number of outliers when there are
     enough matches. This is alternative to the ratio test, used by D. Lowe in SIFT paper.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/BFMatcher.html" title="class in org.opencv.features2d">BFMatcher</a>&nbsp;create(int&nbsp;normType,
                               boolean&nbsp;crossCheck)</pre>
<div class="block">Brute-force matcher create method.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>normType</code> - One of NORM_L1, NORM_L2, NORM_HAMMING, NORM_HAMMING2. L1 and L2 norms are
     preferable choices for SIFT and SURF descriptors, NORM_HAMMING should be used with ORB, BRISK and
     BRIEF, NORM_HAMMING2 should be used with ORB when WTA_K==3 or 4 (see ORB::ORB constructor
     description).</dd>
<dd><code>crossCheck</code> - If it is false, this is will be default BFMatcher behaviour when it finds the k
     nearest neighbors for each query descriptor. If crossCheck==true, then the knnMatch() method with
     k=1 will only return pairs (i,j) such that for i-th query descriptor the j-th descriptor in the
     matcher's collection is the nearest and vice versa, i.e. the BFMatcher will only return consistent
     pairs. Such technique usually produces best results with minimal number of outliers when there are
     enough matches. This is alternative to the ratio test, used by D. Lowe in SIFT paper.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/BOWImgDescriptorExtractor.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/BFMatcher.html" target="_top">Frames</a></li>
<li><a href="BFMatcher.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.org.opencv.features2d.DescriptorMatcher">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
