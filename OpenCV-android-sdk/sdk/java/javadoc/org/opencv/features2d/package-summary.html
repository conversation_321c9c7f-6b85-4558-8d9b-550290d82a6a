<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:25 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.opencv.features2d (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.opencv.features2d (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/dnn/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../org/opencv/imgcodecs/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;org.opencv.features2d</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/AffineFeature.html" title="class in org.opencv.features2d">AffineFeature</a></td>
<td class="colLast">
<div class="block">Class for implementing the wrapper which makes detectors and extractors to be affine invariant,
 described as ASIFT in CITE: YM11 .</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/AgastFeatureDetector.html" title="class in org.opencv.features2d">AgastFeatureDetector</a></td>
<td class="colLast">
<div class="block">Wrapping class for feature detection using the AGAST method.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a></td>
<td class="colLast">
<div class="block">Class implementing the AKAZE keypoint detector and descriptor extractor, described in CITE: ANB13.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/BFMatcher.html" title="class in org.opencv.features2d">BFMatcher</a></td>
<td class="colLast">
<div class="block">Brute-force descriptor matcher.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/BOWImgDescriptorExtractor.html" title="class in org.opencv.features2d">BOWImgDescriptorExtractor</a></td>
<td class="colLast">
<div class="block">Class to compute an image descriptor using the *bag of visual words*.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/BOWKMeansTrainer.html" title="class in org.opencv.features2d">BOWKMeansTrainer</a></td>
<td class="colLast">
<div class="block">kmeans -based class to train visual vocabulary using the *bag of visual words* approach.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/BOWTrainer.html" title="class in org.opencv.features2d">BOWTrainer</a></td>
<td class="colLast">
<div class="block">Abstract base class for training the *bag of visual words* vocabulary from a set of descriptors.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a></td>
<td class="colLast">
<div class="block">Class implementing the BRISK keypoint detector and descriptor extractor, described in CITE: LCS11 .</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></td>
<td class="colLast">
<div class="block">Abstract base class for matching keypoint descriptors.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/FastFeatureDetector.html" title="class in org.opencv.features2d">FastFeatureDetector</a></td>
<td class="colLast">
<div class="block">Wrapping class for feature detection using the FAST method.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></td>
<td class="colLast">
<div class="block">Abstract base class for 2D image feature detectors and descriptor extractors</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/Features2d.html" title="class in org.opencv.features2d">Features2d</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/FlannBasedMatcher.html" title="class in org.opencv.features2d">FlannBasedMatcher</a></td>
<td class="colLast">
<div class="block">Flann-based descriptor matcher.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/GFTTDetector.html" title="class in org.opencv.features2d">GFTTDetector</a></td>
<td class="colLast">
<div class="block">Wrapping class for feature detection using the goodFeaturesToTrack function.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/KAZE.html" title="class in org.opencv.features2d">KAZE</a></td>
<td class="colLast">
<div class="block">Class implementing the KAZE keypoint detector and descriptor extractor, described in CITE: ABD12 .</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/MSER.html" title="class in org.opencv.features2d">MSER</a></td>
<td class="colLast">
<div class="block">Maximally stable extremal region extractor

 The class encapsulates all the parameters of the %MSER extraction algorithm (see [wiki
 article](http://en.wikipedia.org/wiki/Maximally_stable_extremal_regions)).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a></td>
<td class="colLast">
<div class="block">Class implementing the ORB (*oriented BRIEF*) keypoint detector and descriptor extractor

 described in CITE: RRKB11 .</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a></td>
<td class="colLast">
<div class="block">Class for extracting keypoints and computing descriptors using the Scale Invariant Feature Transform
 (SIFT) algorithm by D.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/SimpleBlobDetector.html" title="class in org.opencv.features2d">SimpleBlobDetector</a></td>
<td class="colLast">
<div class="block">Class for extracting blobs from an image.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/features2d/SimpleBlobDetector_Params.html" title="class in org.opencv.features2d">SimpleBlobDetector_Params</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/dnn/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../org/opencv/imgcodecs/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
