<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:23 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>SIFT (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SIFT (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/SimpleBlobDetector.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/SIFT.html" target="_top">Frames</a></li>
<li><a href="SIFT.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.features2d</div>
<h2 title="Class SIFT" class="title">Class SIFT</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">org.opencv.features2d.Feature2D</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.features2d.SIFT</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">SIFT</span>
extends <a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></pre>
<div class="block">Class for extracting keypoints and computing descriptors using the Scale Invariant Feature Transform
 (SIFT) algorithm by D. Lowe CITE: Lowe04 .</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#create--">create</a></span>()</code>
<div class="block">(measured in SIFT algorithm as the local contrast)

     number of octaves is computed automatically from the image resolution.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#create-int-">create</a></span>(int&nbsp;nfeatures)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#create-int-int-">create</a></span>(int&nbsp;nfeatures,
      int&nbsp;nOctaveLayers)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#create-int-int-double-">create</a></span>(int&nbsp;nfeatures,
      int&nbsp;nOctaveLayers,
      double&nbsp;contrastThreshold)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#create-int-int-double-double-">create</a></span>(int&nbsp;nfeatures,
      int&nbsp;nOctaveLayers,
      double&nbsp;contrastThreshold,
      double&nbsp;edgeThreshold)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#create-int-int-double-double-double-">create</a></span>(int&nbsp;nfeatures,
      int&nbsp;nOctaveLayers,
      double&nbsp;contrastThreshold,
      double&nbsp;edgeThreshold,
      double&nbsp;sigma)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#create-int-int-double-double-double-boolean-">create</a></span>(int&nbsp;nfeatures,
      int&nbsp;nOctaveLayers,
      double&nbsp;contrastThreshold,
      double&nbsp;edgeThreshold,
      double&nbsp;sigma,
      boolean&nbsp;enable_precise_upscale)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#create-int-int-double-double-double-int-">create</a></span>(int&nbsp;nfeatures,
      int&nbsp;nOctaveLayers,
      double&nbsp;contrastThreshold,
      double&nbsp;edgeThreshold,
      double&nbsp;sigma,
      int&nbsp;descriptorType)</code>
<div class="block">Create SIFT with specified descriptorType.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#create-int-int-double-double-double-int-boolean-">create</a></span>(int&nbsp;nfeatures,
      int&nbsp;nOctaveLayers,
      double&nbsp;contrastThreshold,
      double&nbsp;edgeThreshold,
      double&nbsp;sigma,
      int&nbsp;descriptorType,
      boolean&nbsp;enable_precise_upscale)</code>
<div class="block">Create SIFT with specified descriptorType.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#getContrastThreshold--">getContrastThreshold</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#getDefaultName--">getDefaultName</a></span>()</code>
<div class="block">Returns the algorithm string identifier.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#getEdgeThreshold--">getEdgeThreshold</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#getNFeatures--">getNFeatures</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#getNOctaveLayers--">getNOctaveLayers</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#getSigma--">getSigma</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#setContrastThreshold-double-">setContrastThreshold</a></span>(double&nbsp;contrastThreshold)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#setEdgeThreshold-double-">setEdgeThreshold</a></span>(double&nbsp;edgeThreshold)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#setNFeatures-int-">setNFeatures</a></span>(int&nbsp;maxFeatures)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#setNOctaveLayers-int-">setNOctaveLayers</a></span>(int&nbsp;nOctaveLayers)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/SIFT.html#setSigma-double-">setSigma</a></span>(double&nbsp;sigma)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.features2d.Feature2D">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.features2d.<a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></h3>
<code><a href="../../../org/opencv/features2d/Feature2D.html#compute-java.util.List-java.util.List-java.util.List-">compute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#compute-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">compute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#defaultNorm--">defaultNorm</a>, <a href="../../../org/opencv/features2d/Feature2D.html#descriptorSize--">descriptorSize</a>, <a href="../../../org/opencv/features2d/Feature2D.html#descriptorType--">descriptorType</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-java.util.List-java.util.List-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-java.util.List-java.util.List-java.util.List-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detectAndCompute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">detectAndCompute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detectAndCompute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-boolean-">detectAndCompute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#empty--">empty</a>, <a href="../../../org/opencv/features2d/Feature2D.html#read-java.lang.String-">read</a>, <a href="../../../org/opencv/features2d/Feature2D.html#write-java.lang.String-">write</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a>&nbsp;create()</pre>
<div class="block">(measured in SIFT algorithm as the local contrast)

     number of octaves is computed automatically from the image resolution.

     (low-contrast) regions. The larger the threshold, the less features are produced by the detector.

     <b>Note:</b> The contrast threshold will be divided by nOctaveLayers when the filtering is applied. When
     nOctaveLayers is set to default and if you want to use the value used in D. Lowe paper, 0.03, set
     this argument to 0.09.

     is different from the contrastThreshold, i.e. the larger the edgeThreshold, the less features are
     filtered out (more features are retained).

     is captured with a weak camera with soft lenses, you might want to reduce the number.

     index \(\texttt{x}\) to \(\texttt{2x}\). This prevents localization bias. The option
     is disabled by default.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a>&nbsp;create(int&nbsp;nfeatures)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nfeatures</code> - The number of best features to retain. The features are ranked by their scores
     (measured in SIFT algorithm as the local contrast)

     number of octaves is computed automatically from the image resolution.

     (low-contrast) regions. The larger the threshold, the less features are produced by the detector.

     <b>Note:</b> The contrast threshold will be divided by nOctaveLayers when the filtering is applied. When
     nOctaveLayers is set to default and if you want to use the value used in D. Lowe paper, 0.03, set
     this argument to 0.09.

     is different from the contrastThreshold, i.e. the larger the edgeThreshold, the less features are
     filtered out (more features are retained).

     is captured with a weak camera with soft lenses, you might want to reduce the number.

     index \(\texttt{x}\) to \(\texttt{2x}\). This prevents localization bias. The option
     is disabled by default.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a>&nbsp;create(int&nbsp;nfeatures,
                          int&nbsp;nOctaveLayers)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nfeatures</code> - The number of best features to retain. The features are ranked by their scores
     (measured in SIFT algorithm as the local contrast)</dd>
<dd><code>nOctaveLayers</code> - The number of layers in each octave. 3 is the value used in D. Lowe paper. The
     number of octaves is computed automatically from the image resolution.

     (low-contrast) regions. The larger the threshold, the less features are produced by the detector.

     <b>Note:</b> The contrast threshold will be divided by nOctaveLayers when the filtering is applied. When
     nOctaveLayers is set to default and if you want to use the value used in D. Lowe paper, 0.03, set
     this argument to 0.09.

     is different from the contrastThreshold, i.e. the larger the edgeThreshold, the less features are
     filtered out (more features are retained).

     is captured with a weak camera with soft lenses, you might want to reduce the number.

     index \(\texttt{x}\) to \(\texttt{2x}\). This prevents localization bias. The option
     is disabled by default.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a>&nbsp;create(int&nbsp;nfeatures,
                          int&nbsp;nOctaveLayers,
                          double&nbsp;contrastThreshold)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nfeatures</code> - The number of best features to retain. The features are ranked by their scores
     (measured in SIFT algorithm as the local contrast)</dd>
<dd><code>nOctaveLayers</code> - The number of layers in each octave. 3 is the value used in D. Lowe paper. The
     number of octaves is computed automatically from the image resolution.</dd>
<dd><code>contrastThreshold</code> - The contrast threshold used to filter out weak features in semi-uniform
     (low-contrast) regions. The larger the threshold, the less features are produced by the detector.

     <b>Note:</b> The contrast threshold will be divided by nOctaveLayers when the filtering is applied. When
     nOctaveLayers is set to default and if you want to use the value used in D. Lowe paper, 0.03, set
     this argument to 0.09.

     is different from the contrastThreshold, i.e. the larger the edgeThreshold, the less features are
     filtered out (more features are retained).

     is captured with a weak camera with soft lenses, you might want to reduce the number.

     index \(\texttt{x}\) to \(\texttt{2x}\). This prevents localization bias. The option
     is disabled by default.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a>&nbsp;create(int&nbsp;nfeatures,
                          int&nbsp;nOctaveLayers,
                          double&nbsp;contrastThreshold,
                          double&nbsp;edgeThreshold)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nfeatures</code> - The number of best features to retain. The features are ranked by their scores
     (measured in SIFT algorithm as the local contrast)</dd>
<dd><code>nOctaveLayers</code> - The number of layers in each octave. 3 is the value used in D. Lowe paper. The
     number of octaves is computed automatically from the image resolution.</dd>
<dd><code>contrastThreshold</code> - The contrast threshold used to filter out weak features in semi-uniform
     (low-contrast) regions. The larger the threshold, the less features are produced by the detector.

     <b>Note:</b> The contrast threshold will be divided by nOctaveLayers when the filtering is applied. When
     nOctaveLayers is set to default and if you want to use the value used in D. Lowe paper, 0.03, set
     this argument to 0.09.</dd>
<dd><code>edgeThreshold</code> - The threshold used to filter out edge-like features. Note that the its meaning
     is different from the contrastThreshold, i.e. the larger the edgeThreshold, the less features are
     filtered out (more features are retained).

     is captured with a weak camera with soft lenses, you might want to reduce the number.

     index \(\texttt{x}\) to \(\texttt{2x}\). This prevents localization bias. The option
     is disabled by default.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-double-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a>&nbsp;create(int&nbsp;nfeatures,
                          int&nbsp;nOctaveLayers,
                          double&nbsp;contrastThreshold,
                          double&nbsp;edgeThreshold,
                          double&nbsp;sigma)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nfeatures</code> - The number of best features to retain. The features are ranked by their scores
     (measured in SIFT algorithm as the local contrast)</dd>
<dd><code>nOctaveLayers</code> - The number of layers in each octave. 3 is the value used in D. Lowe paper. The
     number of octaves is computed automatically from the image resolution.</dd>
<dd><code>contrastThreshold</code> - The contrast threshold used to filter out weak features in semi-uniform
     (low-contrast) regions. The larger the threshold, the less features are produced by the detector.

     <b>Note:</b> The contrast threshold will be divided by nOctaveLayers when the filtering is applied. When
     nOctaveLayers is set to default and if you want to use the value used in D. Lowe paper, 0.03, set
     this argument to 0.09.</dd>
<dd><code>edgeThreshold</code> - The threshold used to filter out edge-like features. Note that the its meaning
     is different from the contrastThreshold, i.e. the larger the edgeThreshold, the less features are
     filtered out (more features are retained).</dd>
<dd><code>sigma</code> - The sigma of the Gaussian applied to the input image at the octave \#0. If your image
     is captured with a weak camera with soft lenses, you might want to reduce the number.

     index \(\texttt{x}\) to \(\texttt{2x}\). This prevents localization bias. The option
     is disabled by default.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-double-double-double-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a>&nbsp;create(int&nbsp;nfeatures,
                          int&nbsp;nOctaveLayers,
                          double&nbsp;contrastThreshold,
                          double&nbsp;edgeThreshold,
                          double&nbsp;sigma,
                          boolean&nbsp;enable_precise_upscale)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nfeatures</code> - The number of best features to retain. The features are ranked by their scores
     (measured in SIFT algorithm as the local contrast)</dd>
<dd><code>nOctaveLayers</code> - The number of layers in each octave. 3 is the value used in D. Lowe paper. The
     number of octaves is computed automatically from the image resolution.</dd>
<dd><code>contrastThreshold</code> - The contrast threshold used to filter out weak features in semi-uniform
     (low-contrast) regions. The larger the threshold, the less features are produced by the detector.

     <b>Note:</b> The contrast threshold will be divided by nOctaveLayers when the filtering is applied. When
     nOctaveLayers is set to default and if you want to use the value used in D. Lowe paper, 0.03, set
     this argument to 0.09.</dd>
<dd><code>edgeThreshold</code> - The threshold used to filter out edge-like features. Note that the its meaning
     is different from the contrastThreshold, i.e. the larger the edgeThreshold, the less features are
     filtered out (more features are retained).</dd>
<dd><code>sigma</code> - The sigma of the Gaussian applied to the input image at the octave \#0. If your image
     is captured with a weak camera with soft lenses, you might want to reduce the number.</dd>
<dd><code>enable_precise_upscale</code> - Whether to enable precise upscaling in the scale pyramid, which maps
     index \(\texttt{x}\) to \(\texttt{2x}\). This prevents localization bias. The option
     is disabled by default.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-double-double-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a>&nbsp;create(int&nbsp;nfeatures,
                          int&nbsp;nOctaveLayers,
                          double&nbsp;contrastThreshold,
                          double&nbsp;edgeThreshold,
                          double&nbsp;sigma,
                          int&nbsp;descriptorType)</pre>
<div class="block">Create SIFT with specified descriptorType.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nfeatures</code> - The number of best features to retain. The features are ranked by their scores
     (measured in SIFT algorithm as the local contrast)</dd>
<dd><code>nOctaveLayers</code> - The number of layers in each octave. 3 is the value used in D. Lowe paper. The
     number of octaves is computed automatically from the image resolution.</dd>
<dd><code>contrastThreshold</code> - The contrast threshold used to filter out weak features in semi-uniform
     (low-contrast) regions. The larger the threshold, the less features are produced by the detector.

     <b>Note:</b> The contrast threshold will be divided by nOctaveLayers when the filtering is applied. When
     nOctaveLayers is set to default and if you want to use the value used in D. Lowe paper, 0.03, set
     this argument to 0.09.</dd>
<dd><code>edgeThreshold</code> - The threshold used to filter out edge-like features. Note that the its meaning
     is different from the contrastThreshold, i.e. the larger the edgeThreshold, the less features are
     filtered out (more features are retained).</dd>
<dd><code>sigma</code> - The sigma of the Gaussian applied to the input image at the octave \#0. If your image
     is captured with a weak camera with soft lenses, you might want to reduce the number.</dd>
<dd><code>descriptorType</code> - The type of descriptors. Only CV_32F and CV_8U are supported.

     index \(\texttt{x}\) to \(\texttt{2x}\). This prevents localization bias. The option
     is disabled by default.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-double-double-double-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/SIFT.html" title="class in org.opencv.features2d">SIFT</a>&nbsp;create(int&nbsp;nfeatures,
                          int&nbsp;nOctaveLayers,
                          double&nbsp;contrastThreshold,
                          double&nbsp;edgeThreshold,
                          double&nbsp;sigma,
                          int&nbsp;descriptorType,
                          boolean&nbsp;enable_precise_upscale)</pre>
<div class="block">Create SIFT with specified descriptorType.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nfeatures</code> - The number of best features to retain. The features are ranked by their scores
     (measured in SIFT algorithm as the local contrast)</dd>
<dd><code>nOctaveLayers</code> - The number of layers in each octave. 3 is the value used in D. Lowe paper. The
     number of octaves is computed automatically from the image resolution.</dd>
<dd><code>contrastThreshold</code> - The contrast threshold used to filter out weak features in semi-uniform
     (low-contrast) regions. The larger the threshold, the less features are produced by the detector.

     <b>Note:</b> The contrast threshold will be divided by nOctaveLayers when the filtering is applied. When
     nOctaveLayers is set to default and if you want to use the value used in D. Lowe paper, 0.03, set
     this argument to 0.09.</dd>
<dd><code>edgeThreshold</code> - The threshold used to filter out edge-like features. Note that the its meaning
     is different from the contrastThreshold, i.e. the larger the edgeThreshold, the less features are
     filtered out (more features are retained).</dd>
<dd><code>sigma</code> - The sigma of the Gaussian applied to the input image at the octave \#0. If your image
     is captured with a weak camera with soft lenses, you might want to reduce the number.</dd>
<dd><code>descriptorType</code> - The type of descriptors. Only CV_32F and CV_8U are supported.</dd>
<dd><code>enable_precise_upscale</code> - Whether to enable precise upscaling in the scale pyramid, which maps
     index \(\texttt{x}\) to \(\texttt{2x}\). This prevents localization bias. The option
     is disabled by default.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getContrastThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContrastThreshold</h4>
<pre>public&nbsp;double&nbsp;getContrastThreshold()</pre>
</li>
</ul>
<a name="getDefaultName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDefaultName()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">Algorithm</a></code></span></div>
<div class="block">Returns the algorithm string identifier.
 This string is used as top level xml/yml node tag when the object is saved to a file or string.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/opencv/features2d/Feature2D.html#getDefaultName--">getDefaultName</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getEdgeThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEdgeThreshold</h4>
<pre>public&nbsp;double&nbsp;getEdgeThreshold()</pre>
</li>
</ul>
<a name="getNFeatures--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNFeatures</h4>
<pre>public&nbsp;int&nbsp;getNFeatures()</pre>
</li>
</ul>
<a name="getNOctaveLayers--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNOctaveLayers</h4>
<pre>public&nbsp;int&nbsp;getNOctaveLayers()</pre>
</li>
</ul>
<a name="getSigma--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSigma</h4>
<pre>public&nbsp;double&nbsp;getSigma()</pre>
</li>
</ul>
<a name="setContrastThreshold-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContrastThreshold</h4>
<pre>public&nbsp;void&nbsp;setContrastThreshold(double&nbsp;contrastThreshold)</pre>
</li>
</ul>
<a name="setEdgeThreshold-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEdgeThreshold</h4>
<pre>public&nbsp;void&nbsp;setEdgeThreshold(double&nbsp;edgeThreshold)</pre>
</li>
</ul>
<a name="setNFeatures-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNFeatures</h4>
<pre>public&nbsp;void&nbsp;setNFeatures(int&nbsp;maxFeatures)</pre>
</li>
</ul>
<a name="setNOctaveLayers-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNOctaveLayers</h4>
<pre>public&nbsp;void&nbsp;setNOctaveLayers(int&nbsp;nOctaveLayers)</pre>
</li>
</ul>
<a name="setSigma-double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setSigma</h4>
<pre>public&nbsp;void&nbsp;setSigma(double&nbsp;sigma)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/ORB.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/SimpleBlobDetector.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/SIFT.html" target="_top">Frames</a></li>
<li><a href="SIFT.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
