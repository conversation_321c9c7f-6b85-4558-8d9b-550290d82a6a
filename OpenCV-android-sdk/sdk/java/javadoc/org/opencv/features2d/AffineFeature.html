<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:23 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AffineFeature (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AffineFeature (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":10,"i7":10,"i8":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/opencv/features2d/AgastFeatureDetector.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/AffineFeature.html" target="_top">Frames</a></li>
<li><a href="AffineFeature.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.features2d</div>
<h2 title="Class AffineFeature" class="title">Class AffineFeature</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">org.opencv.features2d.Feature2D</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.features2d.AffineFeature</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">AffineFeature</span>
extends <a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></pre>
<div class="block">Class for implementing the wrapper which makes detectors and extractors to be affine invariant,
 described as ASIFT in CITE: YM11 .</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/AffineFeature.html" title="class in org.opencv.features2d">AffineFeature</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AffineFeature.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/AffineFeature.html" title="class in org.opencv.features2d">AffineFeature</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AffineFeature.html#create-org.opencv.features2d.Feature2D-">create</a></span>(<a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a>&nbsp;backend)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/AffineFeature.html" title="class in org.opencv.features2d">AffineFeature</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AffineFeature.html#create-org.opencv.features2d.Feature2D-int-">create</a></span>(<a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a>&nbsp;backend,
      int&nbsp;maxTilt)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/AffineFeature.html" title="class in org.opencv.features2d">AffineFeature</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AffineFeature.html#create-org.opencv.features2d.Feature2D-int-int-">create</a></span>(<a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a>&nbsp;backend,
      int&nbsp;maxTilt,
      int&nbsp;minTilt)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/AffineFeature.html" title="class in org.opencv.features2d">AffineFeature</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AffineFeature.html#create-org.opencv.features2d.Feature2D-int-int-float-">create</a></span>(<a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a>&nbsp;backend,
      int&nbsp;maxTilt,
      int&nbsp;minTilt,
      float&nbsp;tiltStep)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/AffineFeature.html" title="class in org.opencv.features2d">AffineFeature</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AffineFeature.html#create-org.opencv.features2d.Feature2D-int-int-float-float-">create</a></span>(<a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a>&nbsp;backend,
      int&nbsp;maxTilt,
      int&nbsp;minTilt,
      float&nbsp;tiltStep,
      float&nbsp;rotateStepBase)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AffineFeature.html#getDefaultName--">getDefaultName</a></span>()</code>
<div class="block">Returns the algorithm string identifier.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AffineFeature.html#getViewParams-org.opencv.core.MatOfFloat-org.opencv.core.MatOfFloat-">getViewParams</a></span>(<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;tilts,
             <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;rolls)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/AffineFeature.html#setViewParams-org.opencv.core.MatOfFloat-org.opencv.core.MatOfFloat-">setViewParams</a></span>(<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;tilts,
             <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;rolls)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.features2d.Feature2D">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.features2d.<a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></h3>
<code><a href="../../../org/opencv/features2d/Feature2D.html#compute-java.util.List-java.util.List-java.util.List-">compute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#compute-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">compute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#defaultNorm--">defaultNorm</a>, <a href="../../../org/opencv/features2d/Feature2D.html#descriptorSize--">descriptorSize</a>, <a href="../../../org/opencv/features2d/Feature2D.html#descriptorType--">descriptorType</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-java.util.List-java.util.List-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-java.util.List-java.util.List-java.util.List-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detectAndCompute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">detectAndCompute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detectAndCompute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-boolean-">detectAndCompute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#empty--">empty</a>, <a href="../../../org/opencv/features2d/Feature2D.html#read-java.lang.String-">read</a>, <a href="../../../org/opencv/features2d/Feature2D.html#write-java.lang.String-">write</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/AffineFeature.html" title="class in org.opencv.features2d">AffineFeature</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create-org.opencv.features2d.Feature2D-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/AffineFeature.html" title="class in org.opencv.features2d">AffineFeature</a>&nbsp;create(<a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a>&nbsp;backend)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>backend</code> - The detector/extractor you want to use as backend.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-org.opencv.features2d.Feature2D-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/AffineFeature.html" title="class in org.opencv.features2d">AffineFeature</a>&nbsp;create(<a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a>&nbsp;backend,
                                   int&nbsp;maxTilt)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>backend</code> - The detector/extractor you want to use as backend.</dd>
<dd><code>maxTilt</code> - The highest power index of tilt factor. 5 is used in the paper as tilt sampling range n.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-org.opencv.features2d.Feature2D-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/AffineFeature.html" title="class in org.opencv.features2d">AffineFeature</a>&nbsp;create(<a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a>&nbsp;backend,
                                   int&nbsp;maxTilt,
                                   int&nbsp;minTilt)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>backend</code> - The detector/extractor you want to use as backend.</dd>
<dd><code>maxTilt</code> - The highest power index of tilt factor. 5 is used in the paper as tilt sampling range n.</dd>
<dd><code>minTilt</code> - The lowest power index of tilt factor. 0 is used in the paper.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-org.opencv.features2d.Feature2D-int-int-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/AffineFeature.html" title="class in org.opencv.features2d">AffineFeature</a>&nbsp;create(<a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a>&nbsp;backend,
                                   int&nbsp;maxTilt,
                                   int&nbsp;minTilt,
                                   float&nbsp;tiltStep)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>backend</code> - The detector/extractor you want to use as backend.</dd>
<dd><code>maxTilt</code> - The highest power index of tilt factor. 5 is used in the paper as tilt sampling range n.</dd>
<dd><code>minTilt</code> - The lowest power index of tilt factor. 0 is used in the paper.</dd>
<dd><code>tiltStep</code> - Tilt sampling step \(\delta_t\) in Algorithm 1 in the paper.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-org.opencv.features2d.Feature2D-int-int-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/AffineFeature.html" title="class in org.opencv.features2d">AffineFeature</a>&nbsp;create(<a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a>&nbsp;backend,
                                   int&nbsp;maxTilt,
                                   int&nbsp;minTilt,
                                   float&nbsp;tiltStep,
                                   float&nbsp;rotateStepBase)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>backend</code> - The detector/extractor you want to use as backend.</dd>
<dd><code>maxTilt</code> - The highest power index of tilt factor. 5 is used in the paper as tilt sampling range n.</dd>
<dd><code>minTilt</code> - The lowest power index of tilt factor. 0 is used in the paper.</dd>
<dd><code>tiltStep</code> - Tilt sampling step \(\delta_t\) in Algorithm 1 in the paper.</dd>
<dd><code>rotateStepBase</code> - Rotation sampling step factor b in Algorithm 1 in the paper.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getDefaultName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDefaultName()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">Algorithm</a></code></span></div>
<div class="block">Returns the algorithm string identifier.
 This string is used as top level xml/yml node tag when the object is saved to a file or string.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/opencv/features2d/Feature2D.html#getDefaultName--">getDefaultName</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getViewParams-org.opencv.core.MatOfFloat-org.opencv.core.MatOfFloat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getViewParams</h4>
<pre>public&nbsp;void&nbsp;getViewParams(<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;tilts,
                          <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;rolls)</pre>
</li>
</ul>
<a name="setViewParams-org.opencv.core.MatOfFloat-org.opencv.core.MatOfFloat-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setViewParams</h4>
<pre>public&nbsp;void&nbsp;setViewParams(<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;tilts,
                          <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;rolls)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/opencv/features2d/AgastFeatureDetector.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/AffineFeature.html" target="_top">Frames</a></li>
<li><a href="AffineFeature.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
