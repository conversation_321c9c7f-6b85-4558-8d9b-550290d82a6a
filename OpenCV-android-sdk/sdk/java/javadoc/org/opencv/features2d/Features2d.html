<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:23 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Features2d (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Features2d (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/FlannBasedMatcher.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/Features2d.html" target="_top">Frames</a></li>
<li><a href="Features2d.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.features2d</div>
<h2 title="Class Features2d" class="title">Class Features2d</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.features2d.Features2d</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Features2d</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#DrawMatchesFlags_DEFAULT">DrawMatchesFlags_DEFAULT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#DrawMatchesFlags_DRAW_OVER_OUTIMG">DrawMatchesFlags_DRAW_OVER_OUTIMG</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#DrawMatchesFlags_DRAW_RICH_KEYPOINTS">DrawMatchesFlags_DRAW_RICH_KEYPOINTS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS">DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#Features2d--">Features2d</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#drawKeypoints-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">drawKeypoints</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
             <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImage)</code>
<div class="block">Draws keypoints.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#drawKeypoints-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.Scalar-">drawKeypoints</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
             <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImage,
             <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;color)</code>
<div class="block">Draws keypoints.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#drawKeypoints-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.Scalar-int-">drawKeypoints</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
             <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImage,
             <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;color,
             int&nbsp;flags)</code>
<div class="block">Draws keypoints.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-">drawMatches</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
           <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg)</code>
<div class="block">Draws the found matches of keypoints from two images.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-int-">drawMatches</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
           <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
           int&nbsp;matchesThickness)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-int-org.opencv.core.Scalar-">drawMatches</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
           <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
           int&nbsp;matchesThickness,
           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-int-org.opencv.core.Scalar-org.opencv.core.Scalar-">drawMatches</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
           <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
           int&nbsp;matchesThickness,
           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-int-org.opencv.core.Scalar-org.opencv.core.Scalar-org.opencv.core.MatOfByte-">drawMatches</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
           <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
           int&nbsp;matchesThickness,
           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
           <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;matchesMask)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-int-org.opencv.core.Scalar-org.opencv.core.Scalar-org.opencv.core.MatOfByte-int-">drawMatches</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
           <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
           int&nbsp;matchesThickness,
           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
           <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;matchesMask,
           int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-org.opencv.core.Scalar-">drawMatches</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
           <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor)</code>
<div class="block">Draws the found matches of keypoints from two images.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Scalar-">drawMatches</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
           <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor)</code>
<div class="block">Draws the found matches of keypoints from two images.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Scalar-org.opencv.core.MatOfByte-">drawMatches</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
           <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
           <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;matchesMask)</code>
<div class="block">Draws the found matches of keypoints from two images.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Scalar-org.opencv.core.MatOfByte-int-">drawMatches</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
           <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
           <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
           <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;matchesMask,
           int&nbsp;flags)</code>
<div class="block">Draws the found matches of keypoints from two images.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#drawMatchesKnn-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-java.util.List-org.opencv.core.Mat-">drawMatchesKnn</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
              <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
              <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
              java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#drawMatchesKnn-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-java.util.List-org.opencv.core.Mat-org.opencv.core.Scalar-">drawMatchesKnn</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
              <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
              <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
              java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
              <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#drawMatchesKnn-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-java.util.List-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Scalar-">drawMatchesKnn</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
              <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
              <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
              java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
              <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
              <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#drawMatchesKnn-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-java.util.List-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Scalar-java.util.List-">drawMatchesKnn</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
              <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
              <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
              java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
              <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
              <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
              java.util.List&lt;<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&gt;&nbsp;matchesMask)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/Features2d.html#drawMatchesKnn-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-java.util.List-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Scalar-java.util.List-int-">drawMatchesKnn</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
              <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
              <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
              java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
              <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
              <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
              java.util.List&lt;<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&gt;&nbsp;matchesMask,
              int&nbsp;flags)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="DrawMatchesFlags_DEFAULT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrawMatchesFlags_DEFAULT</h4>
<pre>public static final&nbsp;int DrawMatchesFlags_DEFAULT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.Features2d.DrawMatchesFlags_DEFAULT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DrawMatchesFlags_DRAW_OVER_OUTIMG">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrawMatchesFlags_DRAW_OVER_OUTIMG</h4>
<pre>public static final&nbsp;int DrawMatchesFlags_DRAW_OVER_OUTIMG</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.Features2d.DrawMatchesFlags_DRAW_OVER_OUTIMG">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DrawMatchesFlags_DRAW_RICH_KEYPOINTS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrawMatchesFlags_DRAW_RICH_KEYPOINTS</h4>
<pre>public static final&nbsp;int DrawMatchesFlags_DRAW_RICH_KEYPOINTS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.Features2d.DrawMatchesFlags_DRAW_RICH_KEYPOINTS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS</h4>
<pre>public static final&nbsp;int DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.Features2d.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Features2d--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Features2d</h4>
<pre>public&nbsp;Features2d()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="drawKeypoints-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawKeypoints</h4>
<pre>public static&nbsp;void&nbsp;drawKeypoints(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                 <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImage)</pre>
<div class="block">Draws keypoints.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - Source image.</dd>
<dd><code>keypoints</code> - Keypoints from the source image.</dd>
<dd><code>outImage</code> - Output image. Its content depends on the flags value defining what is drawn in the
 output image. See possible flags bit values below.
 DrawMatchesFlags. See details above in drawMatches .

 <b>Note:</b>
 For Python API, flags are modified as cv.DRAW_MATCHES_FLAGS_DEFAULT,
 cv.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS, cv.DRAW_MATCHES_FLAGS_DRAW_OVER_OUTIMG,
 cv.DRAW_MATCHES_FLAGS_NOT_DRAW_SINGLE_POINTS</dd>
</dl>
</li>
</ul>
<a name="drawKeypoints-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawKeypoints</h4>
<pre>public static&nbsp;void&nbsp;drawKeypoints(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                 <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImage,
                                 <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;color)</pre>
<div class="block">Draws keypoints.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - Source image.</dd>
<dd><code>keypoints</code> - Keypoints from the source image.</dd>
<dd><code>outImage</code> - Output image. Its content depends on the flags value defining what is drawn in the
 output image. See possible flags bit values below.</dd>
<dd><code>color</code> - Color of keypoints.
 DrawMatchesFlags. See details above in drawMatches .

 <b>Note:</b>
 For Python API, flags are modified as cv.DRAW_MATCHES_FLAGS_DEFAULT,
 cv.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS, cv.DRAW_MATCHES_FLAGS_DRAW_OVER_OUTIMG,
 cv.DRAW_MATCHES_FLAGS_NOT_DRAW_SINGLE_POINTS</dd>
</dl>
</li>
</ul>
<a name="drawKeypoints-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.Scalar-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawKeypoints</h4>
<pre>public static&nbsp;void&nbsp;drawKeypoints(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                 <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImage,
                                 <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;color,
                                 int&nbsp;flags)</pre>
<div class="block">Draws keypoints.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - Source image.</dd>
<dd><code>keypoints</code> - Keypoints from the source image.</dd>
<dd><code>outImage</code> - Output image. Its content depends on the flags value defining what is drawn in the
 output image. See possible flags bit values below.</dd>
<dd><code>color</code> - Color of keypoints.</dd>
<dd><code>flags</code> - Flags setting drawing features. Possible flags bit values are defined by
 DrawMatchesFlags. See details above in drawMatches .

 <b>Note:</b>
 For Python API, flags are modified as cv.DRAW_MATCHES_FLAGS_DEFAULT,
 cv.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS, cv.DRAW_MATCHES_FLAGS_DRAW_OVER_OUTIMG,
 cv.DRAW_MATCHES_FLAGS_NOT_DRAW_SINGLE_POINTS</dd>
</dl>
</li>
</ul>
<a name="drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawMatches</h4>
<pre>public static&nbsp;void&nbsp;drawMatches(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
                               <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg)</pre>
<div class="block">Draws the found matches of keypoints from two images.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img1</code> - First source image.</dd>
<dd><code>keypoints1</code> - Keypoints from the first source image.</dd>
<dd><code>img2</code> - Second source image.</dd>
<dd><code>keypoints2</code> - Keypoints from the second source image.</dd>
<dd><code>matches1to2</code> - Matches from the first image to the second one, which means that keypoints1[i]
 has a corresponding point in keypoints2[matches[i]] .</dd>
<dd><code>outImg</code> - Output image. Its content depends on the flags value defining what is drawn in the
 output image. See possible flags bit values below.
 , the color is generated randomly.
 have the matches. If singlePointColor==Scalar::all(-1) , the color is generated randomly.
 drawn.
 DrawMatchesFlags.

 This function draws matches of keypoints from two images in the output image. Match is a line
 connecting two keypoints (circles). See cv::DrawMatchesFlags.</dd>
</dl>
</li>
</ul>
<a name="drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawMatches</h4>
<pre>public static&nbsp;void&nbsp;drawMatches(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
                               <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
                               int&nbsp;matchesThickness)</pre>
</li>
</ul>
<a name="drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-int-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawMatches</h4>
<pre>public static&nbsp;void&nbsp;drawMatches(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
                               <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
                               int&nbsp;matchesThickness,
                               <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor)</pre>
</li>
</ul>
<a name="drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-int-org.opencv.core.Scalar-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawMatches</h4>
<pre>public static&nbsp;void&nbsp;drawMatches(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
                               <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
                               int&nbsp;matchesThickness,
                               <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
                               <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor)</pre>
</li>
</ul>
<a name="drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-int-org.opencv.core.Scalar-org.opencv.core.Scalar-org.opencv.core.MatOfByte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawMatches</h4>
<pre>public static&nbsp;void&nbsp;drawMatches(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
                               <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
                               int&nbsp;matchesThickness,
                               <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
                               <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
                               <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;matchesMask)</pre>
</li>
</ul>
<a name="drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-int-org.opencv.core.Scalar-org.opencv.core.Scalar-org.opencv.core.MatOfByte-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawMatches</h4>
<pre>public static&nbsp;void&nbsp;drawMatches(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
                               <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
                               int&nbsp;matchesThickness,
                               <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
                               <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
                               <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;matchesMask,
                               int&nbsp;flags)</pre>
</li>
</ul>
<a name="drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawMatches</h4>
<pre>public static&nbsp;void&nbsp;drawMatches(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
                               <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
                               <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor)</pre>
<div class="block">Draws the found matches of keypoints from two images.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img1</code> - First source image.</dd>
<dd><code>keypoints1</code> - Keypoints from the first source image.</dd>
<dd><code>img2</code> - Second source image.</dd>
<dd><code>keypoints2</code> - Keypoints from the second source image.</dd>
<dd><code>matches1to2</code> - Matches from the first image to the second one, which means that keypoints1[i]
 has a corresponding point in keypoints2[matches[i]] .</dd>
<dd><code>outImg</code> - Output image. Its content depends on the flags value defining what is drawn in the
 output image. See possible flags bit values below.</dd>
<dd><code>matchColor</code> - Color of matches (lines and connected keypoints). If matchColor==Scalar::all(-1)
 , the color is generated randomly.
 have the matches. If singlePointColor==Scalar::all(-1) , the color is generated randomly.
 drawn.
 DrawMatchesFlags.

 This function draws matches of keypoints from two images in the output image. Match is a line
 connecting two keypoints (circles). See cv::DrawMatchesFlags.</dd>
</dl>
</li>
</ul>
<a name="drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawMatches</h4>
<pre>public static&nbsp;void&nbsp;drawMatches(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
                               <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
                               <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
                               <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor)</pre>
<div class="block">Draws the found matches of keypoints from two images.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img1</code> - First source image.</dd>
<dd><code>keypoints1</code> - Keypoints from the first source image.</dd>
<dd><code>img2</code> - Second source image.</dd>
<dd><code>keypoints2</code> - Keypoints from the second source image.</dd>
<dd><code>matches1to2</code> - Matches from the first image to the second one, which means that keypoints1[i]
 has a corresponding point in keypoints2[matches[i]] .</dd>
<dd><code>outImg</code> - Output image. Its content depends on the flags value defining what is drawn in the
 output image. See possible flags bit values below.</dd>
<dd><code>matchColor</code> - Color of matches (lines and connected keypoints). If matchColor==Scalar::all(-1)
 , the color is generated randomly.</dd>
<dd><code>singlePointColor</code> - Color of single keypoints (circles), which means that keypoints do not
 have the matches. If singlePointColor==Scalar::all(-1) , the color is generated randomly.
 drawn.
 DrawMatchesFlags.

 This function draws matches of keypoints from two images in the output image. Match is a line
 connecting two keypoints (circles). See cv::DrawMatchesFlags.</dd>
</dl>
</li>
</ul>
<a name="drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Scalar-org.opencv.core.MatOfByte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawMatches</h4>
<pre>public static&nbsp;void&nbsp;drawMatches(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
                               <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
                               <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
                               <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
                               <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;matchesMask)</pre>
<div class="block">Draws the found matches of keypoints from two images.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img1</code> - First source image.</dd>
<dd><code>keypoints1</code> - Keypoints from the first source image.</dd>
<dd><code>img2</code> - Second source image.</dd>
<dd><code>keypoints2</code> - Keypoints from the second source image.</dd>
<dd><code>matches1to2</code> - Matches from the first image to the second one, which means that keypoints1[i]
 has a corresponding point in keypoints2[matches[i]] .</dd>
<dd><code>outImg</code> - Output image. Its content depends on the flags value defining what is drawn in the
 output image. See possible flags bit values below.</dd>
<dd><code>matchColor</code> - Color of matches (lines and connected keypoints). If matchColor==Scalar::all(-1)
 , the color is generated randomly.</dd>
<dd><code>singlePointColor</code> - Color of single keypoints (circles), which means that keypoints do not
 have the matches. If singlePointColor==Scalar::all(-1) , the color is generated randomly.</dd>
<dd><code>matchesMask</code> - Mask determining which matches are drawn. If the mask is empty, all matches are
 drawn.
 DrawMatchesFlags.

 This function draws matches of keypoints from two images in the output image. Match is a line
 connecting two keypoints (circles). See cv::DrawMatchesFlags.</dd>
</dl>
</li>
</ul>
<a name="drawMatches-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Scalar-org.opencv.core.MatOfByte-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawMatches</h4>
<pre>public static&nbsp;void&nbsp;drawMatches(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
                               <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
                               <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches1to2,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
                               <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
                               <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
                               <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;matchesMask,
                               int&nbsp;flags)</pre>
<div class="block">Draws the found matches of keypoints from two images.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img1</code> - First source image.</dd>
<dd><code>keypoints1</code> - Keypoints from the first source image.</dd>
<dd><code>img2</code> - Second source image.</dd>
<dd><code>keypoints2</code> - Keypoints from the second source image.</dd>
<dd><code>matches1to2</code> - Matches from the first image to the second one, which means that keypoints1[i]
 has a corresponding point in keypoints2[matches[i]] .</dd>
<dd><code>outImg</code> - Output image. Its content depends on the flags value defining what is drawn in the
 output image. See possible flags bit values below.</dd>
<dd><code>matchColor</code> - Color of matches (lines and connected keypoints). If matchColor==Scalar::all(-1)
 , the color is generated randomly.</dd>
<dd><code>singlePointColor</code> - Color of single keypoints (circles), which means that keypoints do not
 have the matches. If singlePointColor==Scalar::all(-1) , the color is generated randomly.</dd>
<dd><code>matchesMask</code> - Mask determining which matches are drawn. If the mask is empty, all matches are
 drawn.</dd>
<dd><code>flags</code> - Flags setting drawing features. Possible flags bit values are defined by
 DrawMatchesFlags.

 This function draws matches of keypoints from two images in the output image. Match is a line
 connecting two keypoints (circles). See cv::DrawMatchesFlags.</dd>
</dl>
</li>
</ul>
<a name="drawMatchesKnn-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-java.util.List-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawMatchesKnn</h4>
<pre>public static&nbsp;void&nbsp;drawMatchesKnn(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
                                  <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
                                  <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
                                  java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg)</pre>
</li>
</ul>
<a name="drawMatchesKnn-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-java.util.List-org.opencv.core.Mat-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawMatchesKnn</h4>
<pre>public static&nbsp;void&nbsp;drawMatchesKnn(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
                                  <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
                                  <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
                                  java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
                                  <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor)</pre>
</li>
</ul>
<a name="drawMatchesKnn-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-java.util.List-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawMatchesKnn</h4>
<pre>public static&nbsp;void&nbsp;drawMatchesKnn(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
                                  <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
                                  <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
                                  java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
                                  <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
                                  <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor)</pre>
</li>
</ul>
<a name="drawMatchesKnn-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-java.util.List-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Scalar-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawMatchesKnn</h4>
<pre>public static&nbsp;void&nbsp;drawMatchesKnn(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
                                  <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
                                  <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
                                  java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
                                  <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
                                  <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
                                  java.util.List&lt;<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&gt;&nbsp;matchesMask)</pre>
</li>
</ul>
<a name="drawMatchesKnn-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-java.util.List-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Scalar-java.util.List-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>drawMatchesKnn</h4>
<pre>public static&nbsp;void&nbsp;drawMatchesKnn(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1,
                                  <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints1,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img2,
                                  <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints2,
                                  java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches1to2,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;outImg,
                                  <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;matchColor,
                                  <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;singlePointColor,
                                  java.util.List&lt;<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&gt;&nbsp;matchesMask,
                                  int&nbsp;flags)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/FlannBasedMatcher.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/Features2d.html" target="_top">Frames</a></li>
<li><a href="Features2d.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
