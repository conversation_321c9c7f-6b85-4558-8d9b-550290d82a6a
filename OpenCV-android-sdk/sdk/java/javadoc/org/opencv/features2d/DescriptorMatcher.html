<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:23 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DescriptorMatcher (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DescriptorMatcher (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":9,"i6":9,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/FastFeatureDetector.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/DescriptorMatcher.html" target="_top">Frames</a></li>
<li><a href="DescriptorMatcher.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.features2d</div>
<h2 title="Class DescriptorMatcher" class="title">Class DescriptorMatcher</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.features2d.DescriptorMatcher</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../org/opencv/features2d/BFMatcher.html" title="class in org.opencv.features2d">BFMatcher</a>, <a href="../../../org/opencv/features2d/FlannBasedMatcher.html" title="class in org.opencv.features2d">FlannBasedMatcher</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">DescriptorMatcher</span>
extends <a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></pre>
<div class="block">Abstract base class for matching keypoint descriptors.

 It has two groups of match methods: for matching descriptors of an image with another image or with
 an image set.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE">BRUTEFORCE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE_HAMMING">BRUTEFORCE_HAMMING</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE_HAMMINGLUT">BRUTEFORCE_HAMMINGLUT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE_L1">BRUTEFORCE_L1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE_SL2">BRUTEFORCE_SL2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#FLANNBASED">FLANNBASED</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#add-java.util.List-">add</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;descriptors)</code>
<div class="block">Adds descriptors to train a CPU(trainDescCollectionis) or GPU(utrainDescCollectionis) descriptor
     collection.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#clear--">clear</a></span>()</code>
<div class="block">Clears the train descriptor collections.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#clone--">clone</a></span>()</code>
<div class="block">Clones the matcher.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#clone-boolean-">clone</a></span>(boolean&nbsp;emptyTrainData)</code>
<div class="block">Clones the matcher.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#create-int-">create</a></span>(int&nbsp;matcherType)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#create-java.lang.String-">create</a></span>(java.lang.String&nbsp;descriptorMatcherType)</code>
<div class="block">Creates a descriptor matcher of a given type with the default parameters (using default
     constructor).</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#empty--">empty</a></span>()</code>
<div class="block">Returns true if there are no train descriptors in the both collections.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#getTrainDescriptors--">getTrainDescriptors</a></span>()</code>
<div class="block">Returns a constant link to the train descriptor collection trainDescCollection .</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#isMaskSupported--">isMaskSupported</a></span>()</code>
<div class="block">Returns true if the descriptor matcher supports masking permissible matches.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#knnMatch-org.opencv.core.Mat-java.util.List-int-">knnMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
        int&nbsp;k)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#knnMatch-org.opencv.core.Mat-java.util.List-int-java.util.List-">knnMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
        int&nbsp;k,
        java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#knnMatch-org.opencv.core.Mat-java.util.List-int-java.util.List-boolean-">knnMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
        int&nbsp;k,
        java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks,
        boolean&nbsp;compactResult)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#knnMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-int-">knnMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
        int&nbsp;k)</code>
<div class="block">Finds the k best matches for each descriptor from a query set.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#knnMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-int-org.opencv.core.Mat-">knnMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
        int&nbsp;k,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>
<div class="block">Finds the k best matches for each descriptor from a query set.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#knnMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-int-org.opencv.core.Mat-boolean-">knnMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
        int&nbsp;k,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
        boolean&nbsp;compactResult)</code>
<div class="block">Finds the k best matches for each descriptor from a query set.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#match-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-">match</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
     <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches)</code>
<div class="block">Finds the best match for each descriptor from a query set.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#match-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-">match</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
     <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>
<div class="block">Finds the best match for each descriptor from a query set.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#match-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-">match</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
     <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#match-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-java.util.List-">match</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
     <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches,
     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#radiusMatch-org.opencv.core.Mat-java.util.List-float-">radiusMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
           java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
           float&nbsp;maxDistance)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#radiusMatch-org.opencv.core.Mat-java.util.List-float-java.util.List-">radiusMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
           java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
           float&nbsp;maxDistance,
           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#radiusMatch-org.opencv.core.Mat-java.util.List-float-java.util.List-boolean-">radiusMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
           java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
           float&nbsp;maxDistance,
           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks,
           boolean&nbsp;compactResult)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#radiusMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-float-">radiusMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
           java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
           float&nbsp;maxDistance)</code>
<div class="block">For each query descriptor, finds the training descriptors not farther than the specified distance.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#radiusMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-float-org.opencv.core.Mat-">radiusMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
           java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
           float&nbsp;maxDistance,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>
<div class="block">For each query descriptor, finds the training descriptors not farther than the specified distance.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#radiusMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-float-org.opencv.core.Mat-boolean-">radiusMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
           java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
           float&nbsp;maxDistance,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
           boolean&nbsp;compactResult)</code>
<div class="block">For each query descriptor, finds the training descriptors not farther than the specified distance.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#read-java.lang.String-">read</a></span>(java.lang.String&nbsp;fileName)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#train--">train</a></span>()</code>
<div class="block">Trains a descriptor matcher

     Trains a descriptor matcher (for example, the flann index).</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#write-java.lang.String-">write</a></span>(java.lang.String&nbsp;fileName)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="BRUTEFORCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BRUTEFORCE</h4>
<pre>public static final&nbsp;int BRUTEFORCE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorMatcher.BRUTEFORCE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BRUTEFORCE_HAMMING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BRUTEFORCE_HAMMING</h4>
<pre>public static final&nbsp;int BRUTEFORCE_HAMMING</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorMatcher.BRUTEFORCE_HAMMING">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BRUTEFORCE_HAMMINGLUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BRUTEFORCE_HAMMINGLUT</h4>
<pre>public static final&nbsp;int BRUTEFORCE_HAMMINGLUT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorMatcher.BRUTEFORCE_HAMMINGLUT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BRUTEFORCE_L1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BRUTEFORCE_L1</h4>
<pre>public static final&nbsp;int BRUTEFORCE_L1</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorMatcher.BRUTEFORCE_L1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BRUTEFORCE_SL2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BRUTEFORCE_SL2</h4>
<pre>public static final&nbsp;int BRUTEFORCE_SL2</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorMatcher.BRUTEFORCE_SL2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FLANNBASED">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>FLANNBASED</h4>
<pre>public static final&nbsp;int FLANNBASED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorMatcher.FLANNBASED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="add-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public&nbsp;void&nbsp;add(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;descriptors)</pre>
<div class="block">Adds descriptors to train a CPU(trainDescCollectionis) or GPU(utrainDescCollectionis) descriptor
     collection.

     If the collection is not empty, the new descriptors are added to existing train descriptors.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>descriptors</code> - Descriptors to add. Each descriptors[i] is a set of descriptors from the same
     train image.</dd>
</dl>
</li>
</ul>
<a name="clear--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clear</h4>
<pre>public&nbsp;void&nbsp;clear()</pre>
<div class="block">Clears the train descriptor collections.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></code></dd>
</dl>
</li>
</ul>
<a name="clone--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a>&nbsp;clone()</pre>
<div class="block">Clones the matcher.

     that is, copies both parameters and train data. If emptyTrainData is true, the method creates an
     object copy with the current parameters but with empty train data.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>clone</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="clone-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a>&nbsp;clone(boolean&nbsp;emptyTrainData)</pre>
<div class="block">Clones the matcher.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>emptyTrainData</code> - If emptyTrainData is false, the method creates a deep copy of the object,
     that is, copies both parameters and train data. If emptyTrainData is true, the method creates an
     object copy with the current parameters but with empty train data.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a>&nbsp;create(int&nbsp;matcherType)</pre>
</li>
</ul>
<a name="create-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a>&nbsp;create(java.lang.String&nbsp;descriptorMatcherType)</pre>
<div class="block">Creates a descriptor matcher of a given type with the default parameters (using default
     constructor).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>descriptorMatcherType</code> - Descriptor matcher type. Now the following matcher types are
     supported:
 <ul>
   <li>
        <code>BruteForce</code> (it uses L2 )
   </li>
   <li>
        <code>BruteForce-L1</code>
   </li>
   <li>
        <code>BruteForce-Hamming</code>
   </li>
   <li>
        <code>BruteForce-Hamming(2)</code>
   </li>
   <li>
        <code>FlannBased</code>
   </li>
 </ul></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="empty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>empty</h4>
<pre>public&nbsp;boolean&nbsp;empty()</pre>
<div class="block">Returns true if there are no train descriptors in the both collections.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/opencv/core/Algorithm.html#empty--">empty</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getTrainDescriptors--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrainDescriptors</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;getTrainDescriptors()</pre>
<div class="block">Returns a constant link to the train descriptor collection trainDescCollection .</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="isMaskSupported--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMaskSupported</h4>
<pre>public&nbsp;boolean&nbsp;isMaskSupported()</pre>
<div class="block">Returns true if the descriptor matcher supports masking permissible matches.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="knnMatch-org.opencv.core.Mat-java.util.List-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>knnMatch</h4>
<pre>public&nbsp;void&nbsp;knnMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                     java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                     int&nbsp;k)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>matches</code> - Matches. Each matches[i] is k or less matches for the same query descriptor.</dd>
<dd><code>k</code> - Count of best matches found per each query descriptor or less if a query descriptor has
     less than k possible matches in total.
     descriptors and stored train descriptors from the i-th image trainDescCollection[i].
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.</dd>
</dl>
</li>
</ul>
<a name="knnMatch-org.opencv.core.Mat-java.util.List-int-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>knnMatch</h4>
<pre>public&nbsp;void&nbsp;knnMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                     java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                     int&nbsp;k,
                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>matches</code> - Matches. Each matches[i] is k or less matches for the same query descriptor.</dd>
<dd><code>k</code> - Count of best matches found per each query descriptor or less if a query descriptor has
     less than k possible matches in total.</dd>
<dd><code>masks</code> - Set of masks. Each masks[i] specifies permissible matches between the input query
     descriptors and stored train descriptors from the i-th image trainDescCollection[i].
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.</dd>
</dl>
</li>
</ul>
<a name="knnMatch-org.opencv.core.Mat-java.util.List-int-java.util.List-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>knnMatch</h4>
<pre>public&nbsp;void&nbsp;knnMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                     java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                     int&nbsp;k,
                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks,
                     boolean&nbsp;compactResult)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>matches</code> - Matches. Each matches[i] is k or less matches for the same query descriptor.</dd>
<dd><code>k</code> - Count of best matches found per each query descriptor or less if a query descriptor has
     less than k possible matches in total.</dd>
<dd><code>masks</code> - Set of masks. Each masks[i] specifies permissible matches between the input query
     descriptors and stored train descriptors from the i-th image trainDescCollection[i].</dd>
<dd><code>compactResult</code> - Parameter used when the mask (or masks) is not empty. If compactResult is
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.</dd>
</dl>
</li>
</ul>
<a name="knnMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>knnMatch</h4>
<pre>public&nbsp;void&nbsp;knnMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
                     java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                     int&nbsp;k)</pre>
<div class="block">Finds the k best matches for each descriptor from a query set.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>trainDescriptors</code> - Train set of descriptors. This set is not added to the train descriptors
     collection stored in the class object.
     descriptors.</dd>
<dd><code>matches</code> - Matches. Each matches[i] is k or less matches for the same query descriptor.</dd>
<dd><code>k</code> - Count of best matches found per each query descriptor or less if a query descriptor has
     less than k possible matches in total.
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.

     These extended variants of DescriptorMatcher::match methods find several best matches for each query
     descriptor. The matches are returned in the distance increasing order. See DescriptorMatcher::match
     for the details about query and train descriptors.</dd>
</dl>
</li>
</ul>
<a name="knnMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-int-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>knnMatch</h4>
<pre>public&nbsp;void&nbsp;knnMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
                     java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                     int&nbsp;k,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
<div class="block">Finds the k best matches for each descriptor from a query set.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>trainDescriptors</code> - Train set of descriptors. This set is not added to the train descriptors
     collection stored in the class object.</dd>
<dd><code>mask</code> - Mask specifying permissible matches between an input query and train matrices of
     descriptors.</dd>
<dd><code>matches</code> - Matches. Each matches[i] is k or less matches for the same query descriptor.</dd>
<dd><code>k</code> - Count of best matches found per each query descriptor or less if a query descriptor has
     less than k possible matches in total.
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.

     These extended variants of DescriptorMatcher::match methods find several best matches for each query
     descriptor. The matches are returned in the distance increasing order. See DescriptorMatcher::match
     for the details about query and train descriptors.</dd>
</dl>
</li>
</ul>
<a name="knnMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-int-org.opencv.core.Mat-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>knnMatch</h4>
<pre>public&nbsp;void&nbsp;knnMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
                     java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                     int&nbsp;k,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                     boolean&nbsp;compactResult)</pre>
<div class="block">Finds the k best matches for each descriptor from a query set.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>trainDescriptors</code> - Train set of descriptors. This set is not added to the train descriptors
     collection stored in the class object.</dd>
<dd><code>mask</code> - Mask specifying permissible matches between an input query and train matrices of
     descriptors.</dd>
<dd><code>matches</code> - Matches. Each matches[i] is k or less matches for the same query descriptor.</dd>
<dd><code>k</code> - Count of best matches found per each query descriptor or less if a query descriptor has
     less than k possible matches in total.</dd>
<dd><code>compactResult</code> - Parameter used when the mask (or masks) is not empty. If compactResult is
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.

     These extended variants of DescriptorMatcher::match methods find several best matches for each query
     descriptor. The matches are returned in the distance increasing order. See DescriptorMatcher::match
     for the details about query and train descriptors.</dd>
</dl>
</li>
</ul>
<a name="match-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>match</h4>
<pre>public&nbsp;void&nbsp;match(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
                  <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches)</pre>
<div class="block">Finds the best match for each descriptor from a query set.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>trainDescriptors</code> - Train set of descriptors. This set is not added to the train descriptors
     collection stored in the class object.</dd>
<dd><code>matches</code> - Matches. If a query descriptor is masked out in mask , no match is added for this
     descriptor. So, matches size may be smaller than the query descriptors count.
     descriptors.

     In the first variant of this method, the train descriptors are passed as an input argument. In the
     second variant of the method, train descriptors collection that was set by DescriptorMatcher::add is
     used. Optional mask (or masks) can be passed to specify which query and training descriptors can be
     matched. Namely, queryDescriptors[i] can be matched with trainDescriptors[j] only if
     mask.at&lt;uchar&gt;(i,j) is non-zero.</dd>
</dl>
</li>
</ul>
<a name="match-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>match</h4>
<pre>public&nbsp;void&nbsp;match(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
                  <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
<div class="block">Finds the best match for each descriptor from a query set.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>trainDescriptors</code> - Train set of descriptors. This set is not added to the train descriptors
     collection stored in the class object.</dd>
<dd><code>matches</code> - Matches. If a query descriptor is masked out in mask , no match is added for this
     descriptor. So, matches size may be smaller than the query descriptors count.</dd>
<dd><code>mask</code> - Mask specifying permissible matches between an input query and train matrices of
     descriptors.

     In the first variant of this method, the train descriptors are passed as an input argument. In the
     second variant of the method, train descriptors collection that was set by DescriptorMatcher::add is
     used. Optional mask (or masks) can be passed to specify which query and training descriptors can be
     matched. Namely, queryDescriptors[i] can be matched with trainDescriptors[j] only if
     mask.at&lt;uchar&gt;(i,j) is non-zero.</dd>
</dl>
</li>
</ul>
<a name="match-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>match</h4>
<pre>public&nbsp;void&nbsp;match(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                  <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>matches</code> - Matches. If a query descriptor is masked out in mask , no match is added for this
     descriptor. So, matches size may be smaller than the query descriptors count.
     descriptors and stored train descriptors from the i-th image trainDescCollection[i].</dd>
</dl>
</li>
</ul>
<a name="match-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>match</h4>
<pre>public&nbsp;void&nbsp;match(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                  <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches,
                  java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>matches</code> - Matches. If a query descriptor is masked out in mask , no match is added for this
     descriptor. So, matches size may be smaller than the query descriptors count.</dd>
<dd><code>masks</code> - Set of masks. Each masks[i] specifies permissible matches between the input query
     descriptors and stored train descriptors from the i-th image trainDescCollection[i].</dd>
</dl>
</li>
</ul>
<a name="radiusMatch-org.opencv.core.Mat-java.util.List-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>radiusMatch</h4>
<pre>public&nbsp;void&nbsp;radiusMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                        float&nbsp;maxDistance)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>matches</code> - Found matches.</dd>
<dd><code>maxDistance</code> - Threshold for the distance between matched descriptors. Distance means here
     metric distance (e.g. Hamming distance), not the distance between coordinates (which is measured
     in Pixels)!
     descriptors and stored train descriptors from the i-th image trainDescCollection[i].
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.</dd>
</dl>
</li>
</ul>
<a name="radiusMatch-org.opencv.core.Mat-java.util.List-float-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>radiusMatch</h4>
<pre>public&nbsp;void&nbsp;radiusMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                        float&nbsp;maxDistance,
                        java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>matches</code> - Found matches.</dd>
<dd><code>maxDistance</code> - Threshold for the distance between matched descriptors. Distance means here
     metric distance (e.g. Hamming distance), not the distance between coordinates (which is measured
     in Pixels)!</dd>
<dd><code>masks</code> - Set of masks. Each masks[i] specifies permissible matches between the input query
     descriptors and stored train descriptors from the i-th image trainDescCollection[i].
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.</dd>
</dl>
</li>
</ul>
<a name="radiusMatch-org.opencv.core.Mat-java.util.List-float-java.util.List-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>radiusMatch</h4>
<pre>public&nbsp;void&nbsp;radiusMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                        float&nbsp;maxDistance,
                        java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks,
                        boolean&nbsp;compactResult)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>matches</code> - Found matches.</dd>
<dd><code>maxDistance</code> - Threshold for the distance between matched descriptors. Distance means here
     metric distance (e.g. Hamming distance), not the distance between coordinates (which is measured
     in Pixels)!</dd>
<dd><code>masks</code> - Set of masks. Each masks[i] specifies permissible matches between the input query
     descriptors and stored train descriptors from the i-th image trainDescCollection[i].</dd>
<dd><code>compactResult</code> - Parameter used when the mask (or masks) is not empty. If compactResult is
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.</dd>
</dl>
</li>
</ul>
<a name="radiusMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>radiusMatch</h4>
<pre>public&nbsp;void&nbsp;radiusMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
                        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                        float&nbsp;maxDistance)</pre>
<div class="block">For each query descriptor, finds the training descriptors not farther than the specified distance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>trainDescriptors</code> - Train set of descriptors. This set is not added to the train descriptors
     collection stored in the class object.</dd>
<dd><code>matches</code> - Found matches.
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.</dd>
<dd><code>maxDistance</code> - Threshold for the distance between matched descriptors. Distance means here
     metric distance (e.g. Hamming distance), not the distance between coordinates (which is measured
     in Pixels)!
     descriptors.

     For each query descriptor, the methods find such training descriptors that the distance between the
     query descriptor and the training descriptor is equal or smaller than maxDistance. Found matches are
     returned in the distance increasing order.</dd>
</dl>
</li>
</ul>
<a name="radiusMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-float-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>radiusMatch</h4>
<pre>public&nbsp;void&nbsp;radiusMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
                        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                        float&nbsp;maxDistance,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
<div class="block">For each query descriptor, finds the training descriptors not farther than the specified distance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>trainDescriptors</code> - Train set of descriptors. This set is not added to the train descriptors
     collection stored in the class object.</dd>
<dd><code>matches</code> - Found matches.
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.</dd>
<dd><code>maxDistance</code> - Threshold for the distance between matched descriptors. Distance means here
     metric distance (e.g. Hamming distance), not the distance between coordinates (which is measured
     in Pixels)!</dd>
<dd><code>mask</code> - Mask specifying permissible matches between an input query and train matrices of
     descriptors.

     For each query descriptor, the methods find such training descriptors that the distance between the
     query descriptor and the training descriptor is equal or smaller than maxDistance. Found matches are
     returned in the distance increasing order.</dd>
</dl>
</li>
</ul>
<a name="radiusMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-float-org.opencv.core.Mat-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>radiusMatch</h4>
<pre>public&nbsp;void&nbsp;radiusMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
                        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                        float&nbsp;maxDistance,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                        boolean&nbsp;compactResult)</pre>
<div class="block">For each query descriptor, finds the training descriptors not farther than the specified distance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryDescriptors</code> - Query set of descriptors.</dd>
<dd><code>trainDescriptors</code> - Train set of descriptors. This set is not added to the train descriptors
     collection stored in the class object.</dd>
<dd><code>matches</code> - Found matches.</dd>
<dd><code>compactResult</code> - Parameter used when the mask (or masks) is not empty. If compactResult is
     false, the matches vector has the same size as queryDescriptors rows. If compactResult is true,
     the matches vector does not contain matches for fully masked-out query descriptors.</dd>
<dd><code>maxDistance</code> - Threshold for the distance between matched descriptors. Distance means here
     metric distance (e.g. Hamming distance), not the distance between coordinates (which is measured
     in Pixels)!</dd>
<dd><code>mask</code> - Mask specifying permissible matches between an input query and train matrices of
     descriptors.

     For each query descriptor, the methods find such training descriptors that the distance between the
     query descriptor and the training descriptor is equal or smaller than maxDistance. Found matches are
     returned in the distance increasing order.</dd>
</dl>
</li>
</ul>
<a name="read-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre>public&nbsp;void&nbsp;read(java.lang.String&nbsp;fileName)</pre>
</li>
</ul>
<a name="train--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>train</h4>
<pre>public&nbsp;void&nbsp;train()</pre>
<div class="block">Trains a descriptor matcher

     Trains a descriptor matcher (for example, the flann index). In all methods to match, the method
     train() is run every time before matching. Some descriptor matchers (for example, BruteForceMatcher)
     have an empty implementation of this method. Other matchers really train their inner structures (for
     example, FlannBasedMatcher trains flann::Index ).</div>
</li>
</ul>
<a name="write-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;void&nbsp;write(java.lang.String&nbsp;fileName)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/FastFeatureDetector.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/DescriptorMatcher.html" target="_top">Frames</a></li>
<li><a href="DescriptorMatcher.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
