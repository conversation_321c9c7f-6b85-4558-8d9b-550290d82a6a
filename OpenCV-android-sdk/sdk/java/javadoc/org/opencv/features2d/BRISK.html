<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:23 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>BRISK (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BRISK (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/BOWTrainer.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/BRISK.html" target="_top">Frames</a></li>
<li><a href="BRISK.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.features2d</div>
<h2 title="Class BRISK" class="title">Class BRISK</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">org.opencv.features2d.Feature2D</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.features2d.BRISK</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">BRISK</span>
extends <a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></pre>
<div class="block">Class implementing the BRISK keypoint detector and descriptor extractor, described in CITE: LCS11 .</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#create--">create</a></span>()</code>
<div class="block">The BRISK constructor

     keypoint.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#create-int-">create</a></span>(int&nbsp;thresh)</code>
<div class="block">The BRISK constructor</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#create-int-int-">create</a></span>(int&nbsp;thresh,
      int&nbsp;octaves)</code>
<div class="block">The BRISK constructor</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#create-int-int-float-">create</a></span>(int&nbsp;thresh,
      int&nbsp;octaves,
      float&nbsp;patternScale)</code>
<div class="block">The BRISK constructor</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#create-int-int-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-">create</a></span>(int&nbsp;thresh,
      int&nbsp;octaves,
      <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;radiusList,
      <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numberList)</code>
<div class="block">The BRISK constructor for a custom pattern, detection threshold and octaves</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#create-int-int-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-float-">create</a></span>(int&nbsp;thresh,
      int&nbsp;octaves,
      <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;radiusList,
      <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numberList,
      float&nbsp;dMax)</code>
<div class="block">The BRISK constructor for a custom pattern, detection threshold and octaves</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#create-int-int-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-float-float-">create</a></span>(int&nbsp;thresh,
      int&nbsp;octaves,
      <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;radiusList,
      <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numberList,
      float&nbsp;dMax,
      float&nbsp;dMin)</code>
<div class="block">The BRISK constructor for a custom pattern, detection threshold and octaves</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#create-int-int-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-float-float-org.opencv.core.MatOfInt-">create</a></span>(int&nbsp;thresh,
      int&nbsp;octaves,
      <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;radiusList,
      <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numberList,
      float&nbsp;dMax,
      float&nbsp;dMin,
      <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indexChange)</code>
<div class="block">The BRISK constructor for a custom pattern, detection threshold and octaves</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#create-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-">create</a></span>(<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;radiusList,
      <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numberList)</code>
<div class="block">The BRISK constructor for a custom pattern</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#create-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-float-">create</a></span>(<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;radiusList,
      <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numberList,
      float&nbsp;dMax)</code>
<div class="block">The BRISK constructor for a custom pattern</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#create-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-float-float-">create</a></span>(<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;radiusList,
      <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numberList,
      float&nbsp;dMax,
      float&nbsp;dMin)</code>
<div class="block">The BRISK constructor for a custom pattern</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#create-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-float-float-org.opencv.core.MatOfInt-">create</a></span>(<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;radiusList,
      <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numberList,
      float&nbsp;dMax,
      float&nbsp;dMin,
      <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indexChange)</code>
<div class="block">The BRISK constructor for a custom pattern</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#getDefaultName--">getDefaultName</a></span>()</code>
<div class="block">Returns the algorithm string identifier.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#getOctaves--">getOctaves</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#getPatternScale--">getPatternScale</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#getThreshold--">getThreshold</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#setOctaves-int-">setOctaves</a></span>(int&nbsp;octaves)</code>
<div class="block">Set detection octaves.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#setPatternScale-float-">setPatternScale</a></span>(float&nbsp;patternScale)</code>
<div class="block">Set detection patternScale.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/BRISK.html#setThreshold-int-">setThreshold</a></span>(int&nbsp;threshold)</code>
<div class="block">Set detection threshold.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.features2d.Feature2D">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.features2d.<a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></h3>
<code><a href="../../../org/opencv/features2d/Feature2D.html#compute-java.util.List-java.util.List-java.util.List-">compute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#compute-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">compute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#defaultNorm--">defaultNorm</a>, <a href="../../../org/opencv/features2d/Feature2D.html#descriptorSize--">descriptorSize</a>, <a href="../../../org/opencv/features2d/Feature2D.html#descriptorType--">descriptorType</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-java.util.List-java.util.List-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-java.util.List-java.util.List-java.util.List-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">detect</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detectAndCompute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">detectAndCompute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#detectAndCompute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-boolean-">detectAndCompute</a>, <a href="../../../org/opencv/features2d/Feature2D.html#empty--">empty</a>, <a href="../../../org/opencv/features2d/Feature2D.html#read-java.lang.String-">read</a>, <a href="../../../org/opencv/features2d/Feature2D.html#write-java.lang.String-">write</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a>&nbsp;create()</pre>
<div class="block">The BRISK constructor

     keypoint.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a>&nbsp;create(int&nbsp;thresh)</pre>
<div class="block">The BRISK constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>thresh</code> - AGAST detection threshold score.
     keypoint.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a>&nbsp;create(int&nbsp;thresh,
                           int&nbsp;octaves)</pre>
<div class="block">The BRISK constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>thresh</code> - AGAST detection threshold score.</dd>
<dd><code>octaves</code> - detection octaves. Use 0 to do single scale.
     keypoint.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a>&nbsp;create(int&nbsp;thresh,
                           int&nbsp;octaves,
                           float&nbsp;patternScale)</pre>
<div class="block">The BRISK constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>thresh</code> - AGAST detection threshold score.</dd>
<dd><code>octaves</code> - detection octaves. Use 0 to do single scale.</dd>
<dd><code>patternScale</code> - apply this scale to the pattern used for sampling the neighbourhood of a
     keypoint.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a>&nbsp;create(int&nbsp;thresh,
                           int&nbsp;octaves,
                           <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;radiusList,
                           <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numberList)</pre>
<div class="block">The BRISK constructor for a custom pattern, detection threshold and octaves</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>thresh</code> - AGAST detection threshold score.</dd>
<dd><code>octaves</code> - detection octaves. Use 0 to do single scale.</dd>
<dd><code>radiusList</code> - defines the radii (in pixels) where the samples around a keypoint are taken (for
     keypoint scale 1).</dd>
<dd><code>numberList</code> - defines the number of sampling points on the sampling circle. Must be the same
     size as radiusList..
     scale 1).
     keypoint scale 1).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a>&nbsp;create(int&nbsp;thresh,
                           int&nbsp;octaves,
                           <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;radiusList,
                           <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numberList,
                           float&nbsp;dMax)</pre>
<div class="block">The BRISK constructor for a custom pattern, detection threshold and octaves</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>thresh</code> - AGAST detection threshold score.</dd>
<dd><code>octaves</code> - detection octaves. Use 0 to do single scale.</dd>
<dd><code>radiusList</code> - defines the radii (in pixels) where the samples around a keypoint are taken (for
     keypoint scale 1).</dd>
<dd><code>numberList</code> - defines the number of sampling points on the sampling circle. Must be the same
     size as radiusList..</dd>
<dd><code>dMax</code> - threshold for the short pairings used for descriptor formation (in pixels for keypoint
     scale 1).
     keypoint scale 1).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a>&nbsp;create(int&nbsp;thresh,
                           int&nbsp;octaves,
                           <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;radiusList,
                           <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numberList,
                           float&nbsp;dMax,
                           float&nbsp;dMin)</pre>
<div class="block">The BRISK constructor for a custom pattern, detection threshold and octaves</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>thresh</code> - AGAST detection threshold score.</dd>
<dd><code>octaves</code> - detection octaves. Use 0 to do single scale.</dd>
<dd><code>radiusList</code> - defines the radii (in pixels) where the samples around a keypoint are taken (for
     keypoint scale 1).</dd>
<dd><code>numberList</code> - defines the number of sampling points on the sampling circle. Must be the same
     size as radiusList..</dd>
<dd><code>dMax</code> - threshold for the short pairings used for descriptor formation (in pixels for keypoint
     scale 1).</dd>
<dd><code>dMin</code> - threshold for the long pairings used for orientation determination (in pixels for
     keypoint scale 1).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-float-float-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a>&nbsp;create(int&nbsp;thresh,
                           int&nbsp;octaves,
                           <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;radiusList,
                           <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numberList,
                           float&nbsp;dMax,
                           float&nbsp;dMin,
                           <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indexChange)</pre>
<div class="block">The BRISK constructor for a custom pattern, detection threshold and octaves</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>thresh</code> - AGAST detection threshold score.</dd>
<dd><code>octaves</code> - detection octaves. Use 0 to do single scale.</dd>
<dd><code>radiusList</code> - defines the radii (in pixels) where the samples around a keypoint are taken (for
     keypoint scale 1).</dd>
<dd><code>numberList</code> - defines the number of sampling points on the sampling circle. Must be the same
     size as radiusList..</dd>
<dd><code>dMax</code> - threshold for the short pairings used for descriptor formation (in pixels for keypoint
     scale 1).</dd>
<dd><code>dMin</code> - threshold for the long pairings used for orientation determination (in pixels for
     keypoint scale 1).</dd>
<dd><code>indexChange</code> - index remapping of the bits.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a>&nbsp;create(<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;radiusList,
                           <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numberList)</pre>
<div class="block">The BRISK constructor for a custom pattern</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radiusList</code> - defines the radii (in pixels) where the samples around a keypoint are taken (for
     keypoint scale 1).</dd>
<dd><code>numberList</code> - defines the number of sampling points on the sampling circle. Must be the same
     size as radiusList..
     scale 1).
     keypoint scale 1).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a>&nbsp;create(<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;radiusList,
                           <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numberList,
                           float&nbsp;dMax)</pre>
<div class="block">The BRISK constructor for a custom pattern</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radiusList</code> - defines the radii (in pixels) where the samples around a keypoint are taken (for
     keypoint scale 1).</dd>
<dd><code>numberList</code> - defines the number of sampling points on the sampling circle. Must be the same
     size as radiusList..</dd>
<dd><code>dMax</code> - threshold for the short pairings used for descriptor formation (in pixels for keypoint
     scale 1).
     keypoint scale 1).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a>&nbsp;create(<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;radiusList,
                           <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numberList,
                           float&nbsp;dMax,
                           float&nbsp;dMin)</pre>
<div class="block">The BRISK constructor for a custom pattern</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radiusList</code> - defines the radii (in pixels) where the samples around a keypoint are taken (for
     keypoint scale 1).</dd>
<dd><code>numberList</code> - defines the number of sampling points on the sampling circle. Must be the same
     size as radiusList..</dd>
<dd><code>dMax</code> - threshold for the short pairings used for descriptor formation (in pixels for keypoint
     scale 1).</dd>
<dd><code>dMin</code> - threshold for the long pairings used for orientation determination (in pixels for
     keypoint scale 1).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-float-float-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/BRISK.html" title="class in org.opencv.features2d">BRISK</a>&nbsp;create(<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;radiusList,
                           <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numberList,
                           float&nbsp;dMax,
                           float&nbsp;dMin,
                           <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;indexChange)</pre>
<div class="block">The BRISK constructor for a custom pattern</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radiusList</code> - defines the radii (in pixels) where the samples around a keypoint are taken (for
     keypoint scale 1).</dd>
<dd><code>numberList</code> - defines the number of sampling points on the sampling circle. Must be the same
     size as radiusList..</dd>
<dd><code>dMax</code> - threshold for the short pairings used for descriptor formation (in pixels for keypoint
     scale 1).</dd>
<dd><code>dMin</code> - threshold for the long pairings used for orientation determination (in pixels for
     keypoint scale 1).</dd>
<dd><code>indexChange</code> - index remapping of the bits.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getDefaultName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDefaultName()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">Algorithm</a></code></span></div>
<div class="block">Returns the algorithm string identifier.
 This string is used as top level xml/yml node tag when the object is saved to a file or string.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/opencv/features2d/Feature2D.html#getDefaultName--">getDefaultName</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/opencv/features2d/Feature2D.html" title="class in org.opencv.features2d">Feature2D</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getOctaves--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOctaves</h4>
<pre>public&nbsp;int&nbsp;getOctaves()</pre>
</li>
</ul>
<a name="getPatternScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPatternScale</h4>
<pre>public&nbsp;float&nbsp;getPatternScale()</pre>
</li>
</ul>
<a name="getThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThreshold</h4>
<pre>public&nbsp;int&nbsp;getThreshold()</pre>
</li>
</ul>
<a name="setOctaves-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOctaves</h4>
<pre>public&nbsp;void&nbsp;setOctaves(int&nbsp;octaves)</pre>
<div class="block">Set detection octaves.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>octaves</code> - detection octaves. Use 0 to do single scale.</dd>
</dl>
</li>
</ul>
<a name="setPatternScale-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPatternScale</h4>
<pre>public&nbsp;void&nbsp;setPatternScale(float&nbsp;patternScale)</pre>
<div class="block">Set detection patternScale.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>patternScale</code> - apply this scale to the pattern used for sampling the neighbourhood of a
     keypoint.</dd>
</dl>
</li>
</ul>
<a name="setThreshold-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setThreshold</h4>
<pre>public&nbsp;void&nbsp;setThreshold(int&nbsp;threshold)</pre>
<div class="block">Set detection threshold.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>threshold</code> - AGAST detection threshold score.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/BOWTrainer.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/BRISK.html" target="_top">Frames</a></li>
<li><a href="BRISK.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
