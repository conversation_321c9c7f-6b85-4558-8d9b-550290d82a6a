<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:24 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Videoio (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Videoio (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/videoio/VideoCapture.html" title="class in org.opencv.videoio"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/videoio/VideoWriter.html" title="class in org.opencv.videoio"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/videoio/Videoio.html" target="_top">Frames</a></li>
<li><a href="Videoio.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.videoio</div>
<h2 title="Class Videoio" class="title">Class Videoio</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.videoio.Videoio</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Videoio</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_ANDROID">CAP_ANDROID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_ANY">CAP_ANY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_ARAVIS">CAP_ARAVIS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_AVFOUNDATION">CAP_AVFOUNDATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_CMU1394">CAP_CMU1394</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_DC1394">CAP_DC1394</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_DSHOW">CAP_DSHOW</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_FFMPEG">CAP_FFMPEG</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_FIREWARE">CAP_FIREWARE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_FIREWIRE">CAP_FIREWIRE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_GIGANETIX">CAP_GIGANETIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_GPHOTO2">CAP_GPHOTO2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_GSTREAMER">CAP_GSTREAMER</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_IEEE1394">CAP_IEEE1394</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_IMAGES">CAP_IMAGES</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_INTEL_MFX">CAP_INTEL_MFX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_INTELPERC">CAP_INTELPERC</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_INTELPERC_DEPTH_GENERATOR">CAP_INTELPERC_DEPTH_GENERATOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_INTELPERC_DEPTH_MAP">CAP_INTELPERC_DEPTH_MAP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_INTELPERC_GENERATORS_MASK">CAP_INTELPERC_GENERATORS_MASK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_INTELPERC_IMAGE">CAP_INTELPERC_IMAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_INTELPERC_IMAGE_GENERATOR">CAP_INTELPERC_IMAGE_GENERATOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_INTELPERC_IR_GENERATOR">CAP_INTELPERC_IR_GENERATOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_INTELPERC_IR_MAP">CAP_INTELPERC_IR_MAP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_INTELPERC_UVDEPTH_MAP">CAP_INTELPERC_UVDEPTH_MAP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_MSMF">CAP_MSMF</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OBSENSOR">CAP_OBSENSOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OBSENSOR_BGR_IMAGE">CAP_OBSENSOR_BGR_IMAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OBSENSOR_DEPTH_GENERATOR">CAP_OBSENSOR_DEPTH_GENERATOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OBSENSOR_DEPTH_MAP">CAP_OBSENSOR_DEPTH_MAP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OBSENSOR_GENERATORS_MASK">CAP_OBSENSOR_GENERATORS_MASK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OBSENSOR_IMAGE_GENERATOR">CAP_OBSENSOR_IMAGE_GENERATOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OBSENSOR_IR_GENERATOR">CAP_OBSENSOR_IR_GENERATOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OBSENSOR_IR_IMAGE">CAP_OBSENSOR_IR_IMAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENCV_MJPEG">CAP_OPENCV_MJPEG</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI">CAP_OPENNI</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_ASUS">CAP_OPENNI_ASUS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_BGR_IMAGE">CAP_OPENNI_BGR_IMAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_DEPTH_GENERATOR">CAP_OPENNI_DEPTH_GENERATOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_DEPTH_GENERATOR_BASELINE">CAP_OPENNI_DEPTH_GENERATOR_BASELINE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_DEPTH_GENERATOR_FOCAL_LENGTH">CAP_OPENNI_DEPTH_GENERATOR_FOCAL_LENGTH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_DEPTH_GENERATOR_PRESENT">CAP_OPENNI_DEPTH_GENERATOR_PRESENT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION">CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION_ON">CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION_ON</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_DEPTH_MAP">CAP_OPENNI_DEPTH_MAP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_DISPARITY_MAP">CAP_OPENNI_DISPARITY_MAP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_DISPARITY_MAP_32F">CAP_OPENNI_DISPARITY_MAP_32F</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_GENERATORS_MASK">CAP_OPENNI_GENERATORS_MASK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_GRAY_IMAGE">CAP_OPENNI_GRAY_IMAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_IMAGE_GENERATOR">CAP_OPENNI_IMAGE_GENERATOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_IMAGE_GENERATOR_OUTPUT_MODE">CAP_OPENNI_IMAGE_GENERATOR_OUTPUT_MODE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_IMAGE_GENERATOR_PRESENT">CAP_OPENNI_IMAGE_GENERATOR_PRESENT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_IR_GENERATOR">CAP_OPENNI_IR_GENERATOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_IR_GENERATOR_PRESENT">CAP_OPENNI_IR_GENERATOR_PRESENT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_IR_IMAGE">CAP_OPENNI_IR_IMAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_POINT_CLOUD_MAP">CAP_OPENNI_POINT_CLOUD_MAP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_QVGA_30HZ">CAP_OPENNI_QVGA_30HZ</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_QVGA_60HZ">CAP_OPENNI_QVGA_60HZ</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_SXGA_15HZ">CAP_OPENNI_SXGA_15HZ</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_SXGA_30HZ">CAP_OPENNI_SXGA_30HZ</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_VALID_DEPTH_MASK">CAP_OPENNI_VALID_DEPTH_MASK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI_VGA_30HZ">CAP_OPENNI_VGA_30HZ</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI2">CAP_OPENNI2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI2_ASTRA">CAP_OPENNI2_ASTRA</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_OPENNI2_ASUS">CAP_OPENNI2_ASUS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_APERTURE">CAP_PROP_APERTURE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_ARAVIS_AUTOTRIGGER">CAP_PROP_ARAVIS_AUTOTRIGGER</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_AUDIO_BASE_INDEX">CAP_PROP_AUDIO_BASE_INDEX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_AUDIO_DATA_DEPTH">CAP_PROP_AUDIO_DATA_DEPTH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_AUDIO_POS">CAP_PROP_AUDIO_POS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_AUDIO_SAMPLES_PER_SECOND">CAP_PROP_AUDIO_SAMPLES_PER_SECOND</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_AUDIO_SHIFT_NSEC">CAP_PROP_AUDIO_SHIFT_NSEC</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_AUDIO_STREAM">CAP_PROP_AUDIO_STREAM</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_AUDIO_SYNCHRONIZE">CAP_PROP_AUDIO_SYNCHRONIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_AUDIO_TOTAL_CHANNELS">CAP_PROP_AUDIO_TOTAL_CHANNELS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_AUDIO_TOTAL_STREAMS">CAP_PROP_AUDIO_TOTAL_STREAMS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_AUTO_EXPOSURE">CAP_PROP_AUTO_EXPOSURE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_AUTO_WB">CAP_PROP_AUTO_WB</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_AUTOFOCUS">CAP_PROP_AUTOFOCUS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_BACKEND">CAP_PROP_BACKEND</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_BACKLIGHT">CAP_PROP_BACKLIGHT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_BITRATE">CAP_PROP_BITRATE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_BRIGHTNESS">CAP_PROP_BRIGHTNESS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_BUFFERSIZE">CAP_PROP_BUFFERSIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_CHANNEL">CAP_PROP_CHANNEL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_CODEC_EXTRADATA_INDEX">CAP_PROP_CODEC_EXTRADATA_INDEX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_CODEC_PIXEL_FORMAT">CAP_PROP_CODEC_PIXEL_FORMAT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_CONTRAST">CAP_PROP_CONTRAST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_CONVERT_RGB">CAP_PROP_CONVERT_RGB</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_DC1394_MAX">CAP_PROP_DC1394_MAX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_DC1394_MODE_AUTO">CAP_PROP_DC1394_MODE_AUTO</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_DC1394_MODE_MANUAL">CAP_PROP_DC1394_MODE_MANUAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_DC1394_MODE_ONE_PUSH_AUTO">CAP_PROP_DC1394_MODE_ONE_PUSH_AUTO</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_DC1394_OFF">CAP_PROP_DC1394_OFF</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_EXPOSURE">CAP_PROP_EXPOSURE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_EXPOSUREPROGRAM">CAP_PROP_EXPOSUREPROGRAM</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_FOCUS">CAP_PROP_FOCUS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_FORMAT">CAP_PROP_FORMAT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_FOURCC">CAP_PROP_FOURCC</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_FPS">CAP_PROP_FPS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_FRAME_COUNT">CAP_PROP_FRAME_COUNT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_FRAME_HEIGHT">CAP_PROP_FRAME_HEIGHT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_FRAME_TYPE">CAP_PROP_FRAME_TYPE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_FRAME_WIDTH">CAP_PROP_FRAME_WIDTH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_GAIN">CAP_PROP_GAIN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_GAMMA">CAP_PROP_GAMMA</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_GIGA_FRAME_HEIGH_MAX">CAP_PROP_GIGA_FRAME_HEIGH_MAX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_GIGA_FRAME_OFFSET_X">CAP_PROP_GIGA_FRAME_OFFSET_X</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_GIGA_FRAME_OFFSET_Y">CAP_PROP_GIGA_FRAME_OFFSET_Y</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_GIGA_FRAME_SENS_HEIGH">CAP_PROP_GIGA_FRAME_SENS_HEIGH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_GIGA_FRAME_SENS_WIDTH">CAP_PROP_GIGA_FRAME_SENS_WIDTH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_GIGA_FRAME_WIDTH_MAX">CAP_PROP_GIGA_FRAME_WIDTH_MAX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_GPHOTO2_COLLECT_MSGS">CAP_PROP_GPHOTO2_COLLECT_MSGS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_GPHOTO2_FLUSH_MSGS">CAP_PROP_GPHOTO2_FLUSH_MSGS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_GPHOTO2_PREVIEW">CAP_PROP_GPHOTO2_PREVIEW</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_GPHOTO2_RELOAD_CONFIG">CAP_PROP_GPHOTO2_RELOAD_CONFIG</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_GPHOTO2_RELOAD_ON_CHANGE">CAP_PROP_GPHOTO2_RELOAD_ON_CHANGE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_GPHOTO2_WIDGET_ENUMERATE">CAP_PROP_GPHOTO2_WIDGET_ENUMERATE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_GSTREAMER_QUEUE_LENGTH">CAP_PROP_GSTREAMER_QUEUE_LENGTH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_GUID">CAP_PROP_GUID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_HUE">CAP_PROP_HUE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_HW_ACCELERATION">CAP_PROP_HW_ACCELERATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_HW_ACCELERATION_USE_OPENCL">CAP_PROP_HW_ACCELERATION_USE_OPENCL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_HW_DEVICE">CAP_PROP_HW_DEVICE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_IMAGES_BASE">CAP_PROP_IMAGES_BASE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_IMAGES_LAST">CAP_PROP_IMAGES_LAST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_INTELPERC_DEPTH_CONFIDENCE_THRESHOLD">CAP_PROP_INTELPERC_DEPTH_CONFIDENCE_THRESHOLD</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_HORZ">CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_HORZ</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_VERT">CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_VERT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_INTELPERC_DEPTH_LOW_CONFIDENCE_VALUE">CAP_PROP_INTELPERC_DEPTH_LOW_CONFIDENCE_VALUE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_INTELPERC_DEPTH_SATURATION_VALUE">CAP_PROP_INTELPERC_DEPTH_SATURATION_VALUE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_INTELPERC_PROFILE_COUNT">CAP_PROP_INTELPERC_PROFILE_COUNT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_INTELPERC_PROFILE_IDX">CAP_PROP_INTELPERC_PROFILE_IDX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_IOS_DEVICE_EXPOSURE">CAP_PROP_IOS_DEVICE_EXPOSURE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_IOS_DEVICE_FLASH">CAP_PROP_IOS_DEVICE_FLASH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_IOS_DEVICE_FOCUS">CAP_PROP_IOS_DEVICE_FOCUS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_IOS_DEVICE_TORCH">CAP_PROP_IOS_DEVICE_TORCH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_IOS_DEVICE_WHITEBALANCE">CAP_PROP_IOS_DEVICE_WHITEBALANCE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_IRIS">CAP_PROP_IRIS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_ISO_SPEED">CAP_PROP_ISO_SPEED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_LRF_HAS_KEY_FRAME">CAP_PROP_LRF_HAS_KEY_FRAME</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_MODE">CAP_PROP_MODE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_MONOCHROME">CAP_PROP_MONOCHROME</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_N_THREADS">CAP_PROP_N_THREADS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_OBSENSOR_INTRINSIC_CX">CAP_PROP_OBSENSOR_INTRINSIC_CX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_OBSENSOR_INTRINSIC_CY">CAP_PROP_OBSENSOR_INTRINSIC_CY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_OBSENSOR_INTRINSIC_FX">CAP_PROP_OBSENSOR_INTRINSIC_FX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_OBSENSOR_INTRINSIC_FY">CAP_PROP_OBSENSOR_INTRINSIC_FY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_OPEN_TIMEOUT_MSEC">CAP_PROP_OPEN_TIMEOUT_MSEC</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_APPROX_FRAME_SYNC">CAP_PROP_OPENNI_APPROX_FRAME_SYNC</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_BASELINE">CAP_PROP_OPENNI_BASELINE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_CIRCLE_BUFFER">CAP_PROP_OPENNI_CIRCLE_BUFFER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_FOCAL_LENGTH">CAP_PROP_OPENNI_FOCAL_LENGTH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_FRAME_MAX_DEPTH">CAP_PROP_OPENNI_FRAME_MAX_DEPTH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_GENERATOR_PRESENT">CAP_PROP_OPENNI_GENERATOR_PRESENT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_MAX_BUFFER_SIZE">CAP_PROP_OPENNI_MAX_BUFFER_SIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_MAX_TIME_DURATION">CAP_PROP_OPENNI_MAX_TIME_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_OUTPUT_MODE">CAP_PROP_OPENNI_OUTPUT_MODE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_REGISTRATION">CAP_PROP_OPENNI_REGISTRATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_REGISTRATION_ON">CAP_PROP_OPENNI_REGISTRATION_ON</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI2_MIRROR">CAP_PROP_OPENNI2_MIRROR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI2_SYNC">CAP_PROP_OPENNI2_SYNC</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_ORIENTATION_AUTO">CAP_PROP_ORIENTATION_AUTO</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_ORIENTATION_META">CAP_PROP_ORIENTATION_META</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_PAN">CAP_PROP_PAN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_POS_AVI_RATIO">CAP_PROP_POS_AVI_RATIO</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_POS_FRAMES">CAP_PROP_POS_FRAMES</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_POS_MSEC">CAP_PROP_POS_MSEC</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_PVAPI_BINNINGX">CAP_PROP_PVAPI_BINNINGX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_PVAPI_BINNINGY">CAP_PROP_PVAPI_BINNINGY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_PVAPI_DECIMATIONHORIZONTAL">CAP_PROP_PVAPI_DECIMATIONHORIZONTAL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_PVAPI_DECIMATIONVERTICAL">CAP_PROP_PVAPI_DECIMATIONVERTICAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_PVAPI_FRAMESTARTTRIGGERMODE">CAP_PROP_PVAPI_FRAMESTARTTRIGGERMODE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_PVAPI_MULTICASTIP">CAP_PROP_PVAPI_MULTICASTIP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_PVAPI_PIXELFORMAT">CAP_PROP_PVAPI_PIXELFORMAT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_READ_TIMEOUT_MSEC">CAP_PROP_READ_TIMEOUT_MSEC</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_RECTIFICATION">CAP_PROP_RECTIFICATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_ROLL">CAP_PROP_ROLL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_SAR_DEN">CAP_PROP_SAR_DEN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_SAR_NUM">CAP_PROP_SAR_NUM</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_SATURATION">CAP_PROP_SATURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_SETTINGS">CAP_PROP_SETTINGS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_SHARPNESS">CAP_PROP_SHARPNESS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_SPEED">CAP_PROP_SPEED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_STREAM_OPEN_TIME_USEC">CAP_PROP_STREAM_OPEN_TIME_USEC</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_TEMPERATURE">CAP_PROP_TEMPERATURE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_TILT">CAP_PROP_TILT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_TRIGGER">CAP_PROP_TRIGGER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_TRIGGER_DELAY">CAP_PROP_TRIGGER_DELAY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_VIDEO_STREAM">CAP_PROP_VIDEO_STREAM</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_VIDEO_TOTAL_CHANNELS">CAP_PROP_VIDEO_TOTAL_CHANNELS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_VIEWFINDER">CAP_PROP_VIEWFINDER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_WB_TEMPERATURE">CAP_PROP_WB_TEMPERATURE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_WHITE_BALANCE_BLUE_U">CAP_PROP_WHITE_BALANCE_BLUE_U</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_WHITE_BALANCE_RED_V">CAP_PROP_WHITE_BALANCE_RED_V</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_ACQ_BUFFER_SIZE">CAP_PROP_XI_ACQ_BUFFER_SIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_ACQ_BUFFER_SIZE_UNIT">CAP_PROP_XI_ACQ_BUFFER_SIZE_UNIT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_ACQ_FRAME_BURST_COUNT">CAP_PROP_XI_ACQ_FRAME_BURST_COUNT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_ACQ_TIMING_MODE">CAP_PROP_XI_ACQ_TIMING_MODE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_COMMIT">CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_COMMIT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_SIZE">CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_SIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_AE_MAX_LIMIT">CAP_PROP_XI_AE_MAX_LIMIT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_AEAG">CAP_PROP_XI_AEAG</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_AEAG_LEVEL">CAP_PROP_XI_AEAG_LEVEL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_AEAG_ROI_HEIGHT">CAP_PROP_XI_AEAG_ROI_HEIGHT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_AEAG_ROI_OFFSET_X">CAP_PROP_XI_AEAG_ROI_OFFSET_X</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_AEAG_ROI_OFFSET_Y">CAP_PROP_XI_AEAG_ROI_OFFSET_Y</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_AEAG_ROI_WIDTH">CAP_PROP_XI_AEAG_ROI_WIDTH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_AG_MAX_LIMIT">CAP_PROP_XI_AG_MAX_LIMIT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_APPLY_CMS">CAP_PROP_XI_APPLY_CMS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_AUTO_BANDWIDTH_CALCULATION">CAP_PROP_XI_AUTO_BANDWIDTH_CALCULATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_AUTO_WB">CAP_PROP_XI_AUTO_WB</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_AVAILABLE_BANDWIDTH">CAP_PROP_XI_AVAILABLE_BANDWIDTH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_BINNING_HORIZONTAL">CAP_PROP_XI_BINNING_HORIZONTAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_BINNING_PATTERN">CAP_PROP_XI_BINNING_PATTERN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_BINNING_SELECTOR">CAP_PROP_XI_BINNING_SELECTOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_BINNING_VERTICAL">CAP_PROP_XI_BINNING_VERTICAL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_BPC">CAP_PROP_XI_BPC</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_BUFFER_POLICY">CAP_PROP_XI_BUFFER_POLICY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_BUFFERS_QUEUE_SIZE">CAP_PROP_XI_BUFFERS_QUEUE_SIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_00">CAP_PROP_XI_CC_MATRIX_00</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_01">CAP_PROP_XI_CC_MATRIX_01</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_02">CAP_PROP_XI_CC_MATRIX_02</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_03">CAP_PROP_XI_CC_MATRIX_03</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_10">CAP_PROP_XI_CC_MATRIX_10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_11">CAP_PROP_XI_CC_MATRIX_11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_12">CAP_PROP_XI_CC_MATRIX_12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_13">CAP_PROP_XI_CC_MATRIX_13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_20">CAP_PROP_XI_CC_MATRIX_20</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_21">CAP_PROP_XI_CC_MATRIX_21</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_22">CAP_PROP_XI_CC_MATRIX_22</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_23">CAP_PROP_XI_CC_MATRIX_23</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_30">CAP_PROP_XI_CC_MATRIX_30</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_31">CAP_PROP_XI_CC_MATRIX_31</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_32">CAP_PROP_XI_CC_MATRIX_32</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_33">CAP_PROP_XI_CC_MATRIX_33</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_CHIP_TEMP">CAP_PROP_XI_CHIP_TEMP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_CMS">CAP_PROP_XI_CMS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_COLOR_FILTER_ARRAY">CAP_PROP_XI_COLOR_FILTER_ARRAY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_COLUMN_FPN_CORRECTION">CAP_PROP_XI_COLUMN_FPN_CORRECTION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_COOLING">CAP_PROP_XI_COOLING</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_COUNTER_SELECTOR">CAP_PROP_XI_COUNTER_SELECTOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_COUNTER_VALUE">CAP_PROP_XI_COUNTER_VALUE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_DATA_FORMAT">CAP_PROP_XI_DATA_FORMAT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_DEBOUNCE_EN">CAP_PROP_XI_DEBOUNCE_EN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_DEBOUNCE_POL">CAP_PROP_XI_DEBOUNCE_POL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_DEBOUNCE_T0">CAP_PROP_XI_DEBOUNCE_T0</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_DEBOUNCE_T1">CAP_PROP_XI_DEBOUNCE_T1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_DEBUG_LEVEL">CAP_PROP_XI_DEBUG_LEVEL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_DECIMATION_HORIZONTAL">CAP_PROP_XI_DECIMATION_HORIZONTAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_DECIMATION_PATTERN">CAP_PROP_XI_DECIMATION_PATTERN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_DECIMATION_SELECTOR">CAP_PROP_XI_DECIMATION_SELECTOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_DECIMATION_VERTICAL">CAP_PROP_XI_DECIMATION_VERTICAL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_DEFAULT_CC_MATRIX">CAP_PROP_XI_DEFAULT_CC_MATRIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_DEVICE_MODEL_ID">CAP_PROP_XI_DEVICE_MODEL_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_DEVICE_RESET">CAP_PROP_XI_DEVICE_RESET</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_DEVICE_SN">CAP_PROP_XI_DEVICE_SN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_DOWNSAMPLING">CAP_PROP_XI_DOWNSAMPLING</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_DOWNSAMPLING_TYPE">CAP_PROP_XI_DOWNSAMPLING_TYPE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_EXP_PRIORITY">CAP_PROP_XI_EXP_PRIORITY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_EXPOSURE">CAP_PROP_XI_EXPOSURE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_EXPOSURE_BURST_COUNT">CAP_PROP_XI_EXPOSURE_BURST_COUNT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_FFS_ACCESS_KEY">CAP_PROP_XI_FFS_ACCESS_KEY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_FFS_FILE_ID">CAP_PROP_XI_FFS_FILE_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_FFS_FILE_SIZE">CAP_PROP_XI_FFS_FILE_SIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_FRAMERATE">CAP_PROP_XI_FRAMERATE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_FREE_FFS_SIZE">CAP_PROP_XI_FREE_FFS_SIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_GAIN">CAP_PROP_XI_GAIN</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_GAIN_SELECTOR">CAP_PROP_XI_GAIN_SELECTOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_GAMMAC">CAP_PROP_XI_GAMMAC</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_GAMMAY">CAP_PROP_XI_GAMMAY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_GPI_LEVEL">CAP_PROP_XI_GPI_LEVEL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_GPI_MODE">CAP_PROP_XI_GPI_MODE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_GPI_SELECTOR">CAP_PROP_XI_GPI_SELECTOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_GPO_MODE">CAP_PROP_XI_GPO_MODE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_GPO_SELECTOR">CAP_PROP_XI_GPO_SELECTOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_HDR">CAP_PROP_XI_HDR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_HDR_KNEEPOINT_COUNT">CAP_PROP_XI_HDR_KNEEPOINT_COUNT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_HDR_T1">CAP_PROP_XI_HDR_T1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_HDR_T2">CAP_PROP_XI_HDR_T2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_HEIGHT">CAP_PROP_XI_HEIGHT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_HOUS_BACK_SIDE_TEMP">CAP_PROP_XI_HOUS_BACK_SIDE_TEMP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_HOUS_TEMP">CAP_PROP_XI_HOUS_TEMP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_HW_REVISION">CAP_PROP_XI_HW_REVISION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_IMAGE_BLACK_LEVEL">CAP_PROP_XI_IMAGE_BLACK_LEVEL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_IMAGE_DATA_BIT_DEPTH">CAP_PROP_XI_IMAGE_DATA_BIT_DEPTH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_IMAGE_DATA_FORMAT">CAP_PROP_XI_IMAGE_DATA_FORMAT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_IMAGE_DATA_FORMAT_RGB32_ALPHA">CAP_PROP_XI_IMAGE_DATA_FORMAT_RGB32_ALPHA</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_IMAGE_IS_COLOR">CAP_PROP_XI_IMAGE_IS_COLOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_IMAGE_PAYLOAD_SIZE">CAP_PROP_XI_IMAGE_PAYLOAD_SIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_IS_COOLED">CAP_PROP_XI_IS_COOLED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_IS_DEVICE_EXIST">CAP_PROP_XI_IS_DEVICE_EXIST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_KNEEPOINT1">CAP_PROP_XI_KNEEPOINT1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_KNEEPOINT2">CAP_PROP_XI_KNEEPOINT2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_LED_MODE">CAP_PROP_XI_LED_MODE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_LED_SELECTOR">CAP_PROP_XI_LED_SELECTOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_LENS_APERTURE_VALUE">CAP_PROP_XI_LENS_APERTURE_VALUE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_LENS_FEATURE">CAP_PROP_XI_LENS_FEATURE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_LENS_FEATURE_SELECTOR">CAP_PROP_XI_LENS_FEATURE_SELECTOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_LENS_FOCAL_LENGTH">CAP_PROP_XI_LENS_FOCAL_LENGTH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_LENS_FOCUS_DISTANCE">CAP_PROP_XI_LENS_FOCUS_DISTANCE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_LENS_FOCUS_MOVE">CAP_PROP_XI_LENS_FOCUS_MOVE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_LENS_FOCUS_MOVEMENT_VALUE">CAP_PROP_XI_LENS_FOCUS_MOVEMENT_VALUE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_LENS_MODE">CAP_PROP_XI_LENS_MODE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_LIMIT_BANDWIDTH">CAP_PROP_XI_LIMIT_BANDWIDTH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_LUT_EN">CAP_PROP_XI_LUT_EN</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_LUT_INDEX">CAP_PROP_XI_LUT_INDEX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_LUT_VALUE">CAP_PROP_XI_LUT_VALUE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_MANUAL_WB">CAP_PROP_XI_MANUAL_WB</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_OFFSET_X">CAP_PROP_XI_OFFSET_X</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_OFFSET_Y">CAP_PROP_XI_OFFSET_Y</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_OUTPUT_DATA_BIT_DEPTH">CAP_PROP_XI_OUTPUT_DATA_BIT_DEPTH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_OUTPUT_DATA_PACKING">CAP_PROP_XI_OUTPUT_DATA_PACKING</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_OUTPUT_DATA_PACKING_TYPE">CAP_PROP_XI_OUTPUT_DATA_PACKING_TYPE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_RECENT_FRAME">CAP_PROP_XI_RECENT_FRAME</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_REGION_MODE">CAP_PROP_XI_REGION_MODE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_REGION_SELECTOR">CAP_PROP_XI_REGION_SELECTOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_ROW_FPN_CORRECTION">CAP_PROP_XI_ROW_FPN_CORRECTION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_SENSOR_BOARD_TEMP">CAP_PROP_XI_SENSOR_BOARD_TEMP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_SENSOR_CLOCK_FREQ_HZ">CAP_PROP_XI_SENSOR_CLOCK_FREQ_HZ</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_SENSOR_CLOCK_FREQ_INDEX">CAP_PROP_XI_SENSOR_CLOCK_FREQ_INDEX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_SENSOR_DATA_BIT_DEPTH">CAP_PROP_XI_SENSOR_DATA_BIT_DEPTH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_SENSOR_FEATURE_SELECTOR">CAP_PROP_XI_SENSOR_FEATURE_SELECTOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_SENSOR_FEATURE_VALUE">CAP_PROP_XI_SENSOR_FEATURE_VALUE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_SENSOR_MODE">CAP_PROP_XI_SENSOR_MODE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_SENSOR_OUTPUT_CHANNEL_COUNT">CAP_PROP_XI_SENSOR_OUTPUT_CHANNEL_COUNT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_SENSOR_TAPS">CAP_PROP_XI_SENSOR_TAPS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_SHARPNESS">CAP_PROP_XI_SHARPNESS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_SHUTTER_TYPE">CAP_PROP_XI_SHUTTER_TYPE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_TARGET_TEMP">CAP_PROP_XI_TARGET_TEMP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_TEST_PATTERN">CAP_PROP_XI_TEST_PATTERN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_TEST_PATTERN_GENERATOR_SELECTOR">CAP_PROP_XI_TEST_PATTERN_GENERATOR_SELECTOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_TIMEOUT">CAP_PROP_XI_TIMEOUT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_TRANSPORT_PIXEL_FORMAT">CAP_PROP_XI_TRANSPORT_PIXEL_FORMAT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_TRG_DELAY">CAP_PROP_XI_TRG_DELAY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_TRG_SELECTOR">CAP_PROP_XI_TRG_SELECTOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_TRG_SOFTWARE">CAP_PROP_XI_TRG_SOFTWARE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_TRG_SOURCE">CAP_PROP_XI_TRG_SOURCE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_TS_RST_MODE">CAP_PROP_XI_TS_RST_MODE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_TS_RST_SOURCE">CAP_PROP_XI_TS_RST_SOURCE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_USED_FFS_SIZE">CAP_PROP_XI_USED_FFS_SIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_WB_KB">CAP_PROP_XI_WB_KB</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_WB_KG">CAP_PROP_XI_WB_KG</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_WB_KR">CAP_PROP_XI_WB_KR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_XI_WIDTH">CAP_PROP_XI_WIDTH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PROP_ZOOM">CAP_PROP_ZOOM</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PVAPI">CAP_PVAPI</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PVAPI_DECIMATION_2OUTOF16">CAP_PVAPI_DECIMATION_2OUTOF16</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PVAPI_DECIMATION_2OUTOF4">CAP_PVAPI_DECIMATION_2OUTOF4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PVAPI_DECIMATION_2OUTOF8">CAP_PVAPI_DECIMATION_2OUTOF8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PVAPI_DECIMATION_OFF">CAP_PVAPI_DECIMATION_OFF</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PVAPI_FSTRIGMODE_FIXEDRATE">CAP_PVAPI_FSTRIGMODE_FIXEDRATE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PVAPI_FSTRIGMODE_FREERUN">CAP_PVAPI_FSTRIGMODE_FREERUN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PVAPI_FSTRIGMODE_SOFTWARE">CAP_PVAPI_FSTRIGMODE_SOFTWARE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PVAPI_FSTRIGMODE_SYNCIN1">CAP_PVAPI_FSTRIGMODE_SYNCIN1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PVAPI_FSTRIGMODE_SYNCIN2">CAP_PVAPI_FSTRIGMODE_SYNCIN2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PVAPI_PIXELFORMAT_BAYER16">CAP_PVAPI_PIXELFORMAT_BAYER16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PVAPI_PIXELFORMAT_BAYER8">CAP_PVAPI_PIXELFORMAT_BAYER8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PVAPI_PIXELFORMAT_BGR24">CAP_PVAPI_PIXELFORMAT_BGR24</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PVAPI_PIXELFORMAT_BGRA32">CAP_PVAPI_PIXELFORMAT_BGRA32</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PVAPI_PIXELFORMAT_MONO16">CAP_PVAPI_PIXELFORMAT_MONO16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PVAPI_PIXELFORMAT_MONO8">CAP_PVAPI_PIXELFORMAT_MONO8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PVAPI_PIXELFORMAT_RGB24">CAP_PVAPI_PIXELFORMAT_RGB24</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_PVAPI_PIXELFORMAT_RGBA32">CAP_PVAPI_PIXELFORMAT_RGBA32</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_QT">CAP_QT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_REALSENSE">CAP_REALSENSE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_UEYE">CAP_UEYE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_UNICAP">CAP_UNICAP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_V4L">CAP_V4L</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_V4L2">CAP_V4L2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_VFW">CAP_VFW</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_WINRT">CAP_WINRT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_XIAPI">CAP_XIAPI</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#CAP_XINE">CAP_XINE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#VIDEO_ACCELERATION_ANY">VIDEO_ACCELERATION_ANY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#VIDEO_ACCELERATION_D3D11">VIDEO_ACCELERATION_D3D11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#VIDEO_ACCELERATION_MFX">VIDEO_ACCELERATION_MFX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#VIDEO_ACCELERATION_NONE">VIDEO_ACCELERATION_NONE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#VIDEO_ACCELERATION_VAAPI">VIDEO_ACCELERATION_VAAPI</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#VIDEOWRITER_PROP_DEPTH">VIDEOWRITER_PROP_DEPTH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#VIDEOWRITER_PROP_FRAMEBYTES">VIDEOWRITER_PROP_FRAMEBYTES</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#VIDEOWRITER_PROP_HW_ACCELERATION">VIDEOWRITER_PROP_HW_ACCELERATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#VIDEOWRITER_PROP_HW_ACCELERATION_USE_OPENCL">VIDEOWRITER_PROP_HW_ACCELERATION_USE_OPENCL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#VIDEOWRITER_PROP_HW_DEVICE">VIDEOWRITER_PROP_HW_DEVICE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#VIDEOWRITER_PROP_IS_COLOR">VIDEOWRITER_PROP_IS_COLOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#VIDEOWRITER_PROP_NSTRIPES">VIDEOWRITER_PROP_NSTRIPES</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#VIDEOWRITER_PROP_QUALITY">VIDEOWRITER_PROP_QUALITY</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#Videoio--">Videoio</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#getBackendName-int-">getBackendName</a></span>(int&nbsp;api)</code>
<div class="block">Returns backend API name or "UnknownVideoAPI(xxx)"</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#getCameraBackendPluginVersion-int-int:A-int:A-">getCameraBackendPluginVersion</a></span>(int&nbsp;api,
                             int[]&nbsp;version_ABI,
                             int[]&nbsp;version_API)</code>
<div class="block">Returns description and ABI/API version of videoio plugin's camera interface</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#getStreamBackendPluginVersion-int-int:A-int:A-">getStreamBackendPluginVersion</a></span>(int&nbsp;api,
                             int[]&nbsp;version_ABI,
                             int[]&nbsp;version_API)</code>
<div class="block">Returns description and ABI/API version of videoio plugin's stream capture interface</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#getWriterBackendPluginVersion-int-int:A-int:A-">getWriterBackendPluginVersion</a></span>(int&nbsp;api,
                             int[]&nbsp;version_ABI,
                             int[]&nbsp;version_API)</code>
<div class="block">Returns description and ABI/API version of videoio plugin's writer interface</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#hasBackend-int-">hasBackend</a></span>(int&nbsp;api)</code>
<div class="block">Returns true if backend is available</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/Videoio.html#isBackendBuiltIn-int-">isBackendBuiltIn</a></span>(int&nbsp;api)</code>
<div class="block">Returns true if backend is built in (false if backend is used as plugin)</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="CAP_ANDROID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_ANDROID</h4>
<pre>public static final&nbsp;int CAP_ANDROID</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_ANDROID">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_ANY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_ANY</h4>
<pre>public static final&nbsp;int CAP_ANY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_ANY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_ARAVIS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_ARAVIS</h4>
<pre>public static final&nbsp;int CAP_ARAVIS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_ARAVIS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_AVFOUNDATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_AVFOUNDATION</h4>
<pre>public static final&nbsp;int CAP_AVFOUNDATION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_AVFOUNDATION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_CMU1394">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_CMU1394</h4>
<pre>public static final&nbsp;int CAP_CMU1394</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_CMU1394">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_DC1394">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_DC1394</h4>
<pre>public static final&nbsp;int CAP_DC1394</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_DC1394">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_DSHOW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_DSHOW</h4>
<pre>public static final&nbsp;int CAP_DSHOW</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_DSHOW">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_FFMPEG">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_FFMPEG</h4>
<pre>public static final&nbsp;int CAP_FFMPEG</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_FFMPEG">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_FIREWARE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_FIREWARE</h4>
<pre>public static final&nbsp;int CAP_FIREWARE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_FIREWARE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_FIREWIRE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_FIREWIRE</h4>
<pre>public static final&nbsp;int CAP_FIREWIRE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_FIREWIRE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_GIGANETIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_GIGANETIX</h4>
<pre>public static final&nbsp;int CAP_GIGANETIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_GIGANETIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_GPHOTO2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_GPHOTO2</h4>
<pre>public static final&nbsp;int CAP_GPHOTO2</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_GPHOTO2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_GSTREAMER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_GSTREAMER</h4>
<pre>public static final&nbsp;int CAP_GSTREAMER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_GSTREAMER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_IEEE1394">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_IEEE1394</h4>
<pre>public static final&nbsp;int CAP_IEEE1394</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_IEEE1394">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_IMAGES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_IMAGES</h4>
<pre>public static final&nbsp;int CAP_IMAGES</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_IMAGES">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_INTEL_MFX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_INTEL_MFX</h4>
<pre>public static final&nbsp;int CAP_INTEL_MFX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTEL_MFX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_INTELPERC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_INTELPERC</h4>
<pre>public static final&nbsp;int CAP_INTELPERC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTELPERC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_INTELPERC_DEPTH_GENERATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_INTELPERC_DEPTH_GENERATOR</h4>
<pre>public static final&nbsp;int CAP_INTELPERC_DEPTH_GENERATOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTELPERC_DEPTH_GENERATOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_INTELPERC_DEPTH_MAP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_INTELPERC_DEPTH_MAP</h4>
<pre>public static final&nbsp;int CAP_INTELPERC_DEPTH_MAP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTELPERC_DEPTH_MAP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_INTELPERC_GENERATORS_MASK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_INTELPERC_GENERATORS_MASK</h4>
<pre>public static final&nbsp;int CAP_INTELPERC_GENERATORS_MASK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTELPERC_GENERATORS_MASK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_INTELPERC_IMAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_INTELPERC_IMAGE</h4>
<pre>public static final&nbsp;int CAP_INTELPERC_IMAGE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTELPERC_IMAGE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_INTELPERC_IMAGE_GENERATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_INTELPERC_IMAGE_GENERATOR</h4>
<pre>public static final&nbsp;int CAP_INTELPERC_IMAGE_GENERATOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTELPERC_IMAGE_GENERATOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_INTELPERC_IR_GENERATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_INTELPERC_IR_GENERATOR</h4>
<pre>public static final&nbsp;int CAP_INTELPERC_IR_GENERATOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTELPERC_IR_GENERATOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_INTELPERC_IR_MAP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_INTELPERC_IR_MAP</h4>
<pre>public static final&nbsp;int CAP_INTELPERC_IR_MAP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTELPERC_IR_MAP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_INTELPERC_UVDEPTH_MAP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_INTELPERC_UVDEPTH_MAP</h4>
<pre>public static final&nbsp;int CAP_INTELPERC_UVDEPTH_MAP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_INTELPERC_UVDEPTH_MAP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_MSMF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_MSMF</h4>
<pre>public static final&nbsp;int CAP_MSMF</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_MSMF">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OBSENSOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OBSENSOR</h4>
<pre>public static final&nbsp;int CAP_OBSENSOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OBSENSOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OBSENSOR_BGR_IMAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OBSENSOR_BGR_IMAGE</h4>
<pre>public static final&nbsp;int CAP_OBSENSOR_BGR_IMAGE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OBSENSOR_BGR_IMAGE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OBSENSOR_DEPTH_GENERATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OBSENSOR_DEPTH_GENERATOR</h4>
<pre>public static final&nbsp;int CAP_OBSENSOR_DEPTH_GENERATOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OBSENSOR_DEPTH_GENERATOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OBSENSOR_DEPTH_MAP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OBSENSOR_DEPTH_MAP</h4>
<pre>public static final&nbsp;int CAP_OBSENSOR_DEPTH_MAP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OBSENSOR_DEPTH_MAP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OBSENSOR_GENERATORS_MASK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OBSENSOR_GENERATORS_MASK</h4>
<pre>public static final&nbsp;int CAP_OBSENSOR_GENERATORS_MASK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OBSENSOR_GENERATORS_MASK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OBSENSOR_IMAGE_GENERATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OBSENSOR_IMAGE_GENERATOR</h4>
<pre>public static final&nbsp;int CAP_OBSENSOR_IMAGE_GENERATOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OBSENSOR_IMAGE_GENERATOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OBSENSOR_IR_GENERATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OBSENSOR_IR_GENERATOR</h4>
<pre>public static final&nbsp;int CAP_OBSENSOR_IR_GENERATOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OBSENSOR_IR_GENERATOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OBSENSOR_IR_IMAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OBSENSOR_IR_IMAGE</h4>
<pre>public static final&nbsp;int CAP_OBSENSOR_IR_IMAGE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OBSENSOR_IR_IMAGE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENCV_MJPEG">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENCV_MJPEG</h4>
<pre>public static final&nbsp;int CAP_OPENCV_MJPEG</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENCV_MJPEG">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI</h4>
<pre>public static final&nbsp;int CAP_OPENNI</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_ASUS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_ASUS</h4>
<pre>public static final&nbsp;int CAP_OPENNI_ASUS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_ASUS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_BGR_IMAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_BGR_IMAGE</h4>
<pre>public static final&nbsp;int CAP_OPENNI_BGR_IMAGE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_BGR_IMAGE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_DEPTH_GENERATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_DEPTH_GENERATOR</h4>
<pre>public static final&nbsp;int CAP_OPENNI_DEPTH_GENERATOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_GENERATOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_DEPTH_GENERATOR_BASELINE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_DEPTH_GENERATOR_BASELINE</h4>
<pre>public static final&nbsp;int CAP_OPENNI_DEPTH_GENERATOR_BASELINE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_GENERATOR_BASELINE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_DEPTH_GENERATOR_FOCAL_LENGTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_DEPTH_GENERATOR_FOCAL_LENGTH</h4>
<pre>public static final&nbsp;int CAP_OPENNI_DEPTH_GENERATOR_FOCAL_LENGTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_GENERATOR_FOCAL_LENGTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_DEPTH_GENERATOR_PRESENT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_DEPTH_GENERATOR_PRESENT</h4>
<pre>public static final&nbsp;int CAP_OPENNI_DEPTH_GENERATOR_PRESENT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_GENERATOR_PRESENT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION</h4>
<pre>public static final&nbsp;int CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION_ON">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION_ON</h4>
<pre>public static final&nbsp;int CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION_ON</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION_ON">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_DEPTH_MAP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_DEPTH_MAP</h4>
<pre>public static final&nbsp;int CAP_OPENNI_DEPTH_MAP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_MAP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_DISPARITY_MAP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_DISPARITY_MAP</h4>
<pre>public static final&nbsp;int CAP_OPENNI_DISPARITY_MAP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_DISPARITY_MAP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_DISPARITY_MAP_32F">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_DISPARITY_MAP_32F</h4>
<pre>public static final&nbsp;int CAP_OPENNI_DISPARITY_MAP_32F</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_DISPARITY_MAP_32F">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_GENERATORS_MASK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_GENERATORS_MASK</h4>
<pre>public static final&nbsp;int CAP_OPENNI_GENERATORS_MASK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_GENERATORS_MASK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_GRAY_IMAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_GRAY_IMAGE</h4>
<pre>public static final&nbsp;int CAP_OPENNI_GRAY_IMAGE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_GRAY_IMAGE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_IMAGE_GENERATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_IMAGE_GENERATOR</h4>
<pre>public static final&nbsp;int CAP_OPENNI_IMAGE_GENERATOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_IMAGE_GENERATOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_IMAGE_GENERATOR_OUTPUT_MODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_IMAGE_GENERATOR_OUTPUT_MODE</h4>
<pre>public static final&nbsp;int CAP_OPENNI_IMAGE_GENERATOR_OUTPUT_MODE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_IMAGE_GENERATOR_OUTPUT_MODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_IMAGE_GENERATOR_PRESENT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_IMAGE_GENERATOR_PRESENT</h4>
<pre>public static final&nbsp;int CAP_OPENNI_IMAGE_GENERATOR_PRESENT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_IMAGE_GENERATOR_PRESENT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_IR_GENERATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_IR_GENERATOR</h4>
<pre>public static final&nbsp;int CAP_OPENNI_IR_GENERATOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_IR_GENERATOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_IR_GENERATOR_PRESENT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_IR_GENERATOR_PRESENT</h4>
<pre>public static final&nbsp;int CAP_OPENNI_IR_GENERATOR_PRESENT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_IR_GENERATOR_PRESENT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_IR_IMAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_IR_IMAGE</h4>
<pre>public static final&nbsp;int CAP_OPENNI_IR_IMAGE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_IR_IMAGE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_POINT_CLOUD_MAP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_POINT_CLOUD_MAP</h4>
<pre>public static final&nbsp;int CAP_OPENNI_POINT_CLOUD_MAP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_POINT_CLOUD_MAP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_QVGA_30HZ">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_QVGA_30HZ</h4>
<pre>public static final&nbsp;int CAP_OPENNI_QVGA_30HZ</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_QVGA_30HZ">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_QVGA_60HZ">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_QVGA_60HZ</h4>
<pre>public static final&nbsp;int CAP_OPENNI_QVGA_60HZ</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_QVGA_60HZ">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_SXGA_15HZ">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_SXGA_15HZ</h4>
<pre>public static final&nbsp;int CAP_OPENNI_SXGA_15HZ</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_SXGA_15HZ">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_SXGA_30HZ">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_SXGA_30HZ</h4>
<pre>public static final&nbsp;int CAP_OPENNI_SXGA_30HZ</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_SXGA_30HZ">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_VALID_DEPTH_MASK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_VALID_DEPTH_MASK</h4>
<pre>public static final&nbsp;int CAP_OPENNI_VALID_DEPTH_MASK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_VALID_DEPTH_MASK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI_VGA_30HZ">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI_VGA_30HZ</h4>
<pre>public static final&nbsp;int CAP_OPENNI_VGA_30HZ</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI_VGA_30HZ">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI2</h4>
<pre>public static final&nbsp;int CAP_OPENNI2</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI2_ASTRA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI2_ASTRA</h4>
<pre>public static final&nbsp;int CAP_OPENNI2_ASTRA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI2_ASTRA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_OPENNI2_ASUS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_OPENNI2_ASUS</h4>
<pre>public static final&nbsp;int CAP_OPENNI2_ASUS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_OPENNI2_ASUS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_APERTURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_APERTURE</h4>
<pre>public static final&nbsp;int CAP_PROP_APERTURE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_APERTURE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_ARAVIS_AUTOTRIGGER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_ARAVIS_AUTOTRIGGER</h4>
<pre>public static final&nbsp;int CAP_PROP_ARAVIS_AUTOTRIGGER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_ARAVIS_AUTOTRIGGER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_AUDIO_BASE_INDEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_AUDIO_BASE_INDEX</h4>
<pre>public static final&nbsp;int CAP_PROP_AUDIO_BASE_INDEX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUDIO_BASE_INDEX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_AUDIO_DATA_DEPTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_AUDIO_DATA_DEPTH</h4>
<pre>public static final&nbsp;int CAP_PROP_AUDIO_DATA_DEPTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUDIO_DATA_DEPTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_AUDIO_POS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_AUDIO_POS</h4>
<pre>public static final&nbsp;int CAP_PROP_AUDIO_POS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUDIO_POS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_AUDIO_SAMPLES_PER_SECOND">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_AUDIO_SAMPLES_PER_SECOND</h4>
<pre>public static final&nbsp;int CAP_PROP_AUDIO_SAMPLES_PER_SECOND</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUDIO_SAMPLES_PER_SECOND">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_AUDIO_SHIFT_NSEC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_AUDIO_SHIFT_NSEC</h4>
<pre>public static final&nbsp;int CAP_PROP_AUDIO_SHIFT_NSEC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUDIO_SHIFT_NSEC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_AUDIO_STREAM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_AUDIO_STREAM</h4>
<pre>public static final&nbsp;int CAP_PROP_AUDIO_STREAM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUDIO_STREAM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_AUDIO_SYNCHRONIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_AUDIO_SYNCHRONIZE</h4>
<pre>public static final&nbsp;int CAP_PROP_AUDIO_SYNCHRONIZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUDIO_SYNCHRONIZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_AUDIO_TOTAL_CHANNELS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_AUDIO_TOTAL_CHANNELS</h4>
<pre>public static final&nbsp;int CAP_PROP_AUDIO_TOTAL_CHANNELS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUDIO_TOTAL_CHANNELS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_AUDIO_TOTAL_STREAMS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_AUDIO_TOTAL_STREAMS</h4>
<pre>public static final&nbsp;int CAP_PROP_AUDIO_TOTAL_STREAMS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUDIO_TOTAL_STREAMS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_AUTO_EXPOSURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_AUTO_EXPOSURE</h4>
<pre>public static final&nbsp;int CAP_PROP_AUTO_EXPOSURE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUTO_EXPOSURE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_AUTO_WB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_AUTO_WB</h4>
<pre>public static final&nbsp;int CAP_PROP_AUTO_WB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUTO_WB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_AUTOFOCUS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_AUTOFOCUS</h4>
<pre>public static final&nbsp;int CAP_PROP_AUTOFOCUS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_AUTOFOCUS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_BACKEND">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_BACKEND</h4>
<pre>public static final&nbsp;int CAP_PROP_BACKEND</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_BACKEND">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_BACKLIGHT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_BACKLIGHT</h4>
<pre>public static final&nbsp;int CAP_PROP_BACKLIGHT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_BACKLIGHT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_BITRATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_BITRATE</h4>
<pre>public static final&nbsp;int CAP_PROP_BITRATE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_BITRATE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_BRIGHTNESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_BRIGHTNESS</h4>
<pre>public static final&nbsp;int CAP_PROP_BRIGHTNESS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_BRIGHTNESS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_BUFFERSIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_BUFFERSIZE</h4>
<pre>public static final&nbsp;int CAP_PROP_BUFFERSIZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_BUFFERSIZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_CHANNEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_CHANNEL</h4>
<pre>public static final&nbsp;int CAP_PROP_CHANNEL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_CHANNEL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_CODEC_EXTRADATA_INDEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_CODEC_EXTRADATA_INDEX</h4>
<pre>public static final&nbsp;int CAP_PROP_CODEC_EXTRADATA_INDEX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_CODEC_EXTRADATA_INDEX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_CODEC_PIXEL_FORMAT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_CODEC_PIXEL_FORMAT</h4>
<pre>public static final&nbsp;int CAP_PROP_CODEC_PIXEL_FORMAT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_CODEC_PIXEL_FORMAT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_CONTRAST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_CONTRAST</h4>
<pre>public static final&nbsp;int CAP_PROP_CONTRAST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_CONTRAST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_CONVERT_RGB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_CONVERT_RGB</h4>
<pre>public static final&nbsp;int CAP_PROP_CONVERT_RGB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_CONVERT_RGB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_DC1394_MAX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_DC1394_MAX</h4>
<pre>public static final&nbsp;int CAP_PROP_DC1394_MAX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_DC1394_MAX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_DC1394_MODE_AUTO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_DC1394_MODE_AUTO</h4>
<pre>public static final&nbsp;int CAP_PROP_DC1394_MODE_AUTO</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_DC1394_MODE_AUTO">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_DC1394_MODE_MANUAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_DC1394_MODE_MANUAL</h4>
<pre>public static final&nbsp;int CAP_PROP_DC1394_MODE_MANUAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_DC1394_MODE_MANUAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_DC1394_MODE_ONE_PUSH_AUTO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_DC1394_MODE_ONE_PUSH_AUTO</h4>
<pre>public static final&nbsp;int CAP_PROP_DC1394_MODE_ONE_PUSH_AUTO</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_DC1394_MODE_ONE_PUSH_AUTO">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_DC1394_OFF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_DC1394_OFF</h4>
<pre>public static final&nbsp;int CAP_PROP_DC1394_OFF</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_DC1394_OFF">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_EXPOSURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_EXPOSURE</h4>
<pre>public static final&nbsp;int CAP_PROP_EXPOSURE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_EXPOSURE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_EXPOSUREPROGRAM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_EXPOSUREPROGRAM</h4>
<pre>public static final&nbsp;int CAP_PROP_EXPOSUREPROGRAM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_EXPOSUREPROGRAM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_FOCUS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_FOCUS</h4>
<pre>public static final&nbsp;int CAP_PROP_FOCUS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_FOCUS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_FORMAT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_FORMAT</h4>
<pre>public static final&nbsp;int CAP_PROP_FORMAT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_FORMAT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_FOURCC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_FOURCC</h4>
<pre>public static final&nbsp;int CAP_PROP_FOURCC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_FOURCC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_FPS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_FPS</h4>
<pre>public static final&nbsp;int CAP_PROP_FPS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_FPS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_FRAME_COUNT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_FRAME_COUNT</h4>
<pre>public static final&nbsp;int CAP_PROP_FRAME_COUNT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_FRAME_COUNT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_FRAME_HEIGHT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_FRAME_HEIGHT</h4>
<pre>public static final&nbsp;int CAP_PROP_FRAME_HEIGHT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_FRAME_HEIGHT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_FRAME_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_FRAME_TYPE</h4>
<pre>public static final&nbsp;int CAP_PROP_FRAME_TYPE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_FRAME_TYPE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_FRAME_WIDTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_FRAME_WIDTH</h4>
<pre>public static final&nbsp;int CAP_PROP_FRAME_WIDTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_FRAME_WIDTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_GAIN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_GAIN</h4>
<pre>public static final&nbsp;int CAP_PROP_GAIN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GAIN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_GAMMA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_GAMMA</h4>
<pre>public static final&nbsp;int CAP_PROP_GAMMA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GAMMA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_GIGA_FRAME_HEIGH_MAX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_GIGA_FRAME_HEIGH_MAX</h4>
<pre>public static final&nbsp;int CAP_PROP_GIGA_FRAME_HEIGH_MAX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GIGA_FRAME_HEIGH_MAX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_GIGA_FRAME_OFFSET_X">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_GIGA_FRAME_OFFSET_X</h4>
<pre>public static final&nbsp;int CAP_PROP_GIGA_FRAME_OFFSET_X</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GIGA_FRAME_OFFSET_X">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_GIGA_FRAME_OFFSET_Y">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_GIGA_FRAME_OFFSET_Y</h4>
<pre>public static final&nbsp;int CAP_PROP_GIGA_FRAME_OFFSET_Y</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GIGA_FRAME_OFFSET_Y">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_GIGA_FRAME_SENS_HEIGH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_GIGA_FRAME_SENS_HEIGH</h4>
<pre>public static final&nbsp;int CAP_PROP_GIGA_FRAME_SENS_HEIGH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GIGA_FRAME_SENS_HEIGH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_GIGA_FRAME_SENS_WIDTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_GIGA_FRAME_SENS_WIDTH</h4>
<pre>public static final&nbsp;int CAP_PROP_GIGA_FRAME_SENS_WIDTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GIGA_FRAME_SENS_WIDTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_GIGA_FRAME_WIDTH_MAX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_GIGA_FRAME_WIDTH_MAX</h4>
<pre>public static final&nbsp;int CAP_PROP_GIGA_FRAME_WIDTH_MAX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GIGA_FRAME_WIDTH_MAX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_GPHOTO2_COLLECT_MSGS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_GPHOTO2_COLLECT_MSGS</h4>
<pre>public static final&nbsp;int CAP_PROP_GPHOTO2_COLLECT_MSGS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GPHOTO2_COLLECT_MSGS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_GPHOTO2_FLUSH_MSGS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_GPHOTO2_FLUSH_MSGS</h4>
<pre>public static final&nbsp;int CAP_PROP_GPHOTO2_FLUSH_MSGS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GPHOTO2_FLUSH_MSGS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_GPHOTO2_PREVIEW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_GPHOTO2_PREVIEW</h4>
<pre>public static final&nbsp;int CAP_PROP_GPHOTO2_PREVIEW</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GPHOTO2_PREVIEW">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_GPHOTO2_RELOAD_CONFIG">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_GPHOTO2_RELOAD_CONFIG</h4>
<pre>public static final&nbsp;int CAP_PROP_GPHOTO2_RELOAD_CONFIG</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GPHOTO2_RELOAD_CONFIG">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_GPHOTO2_RELOAD_ON_CHANGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_GPHOTO2_RELOAD_ON_CHANGE</h4>
<pre>public static final&nbsp;int CAP_PROP_GPHOTO2_RELOAD_ON_CHANGE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GPHOTO2_RELOAD_ON_CHANGE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_GPHOTO2_WIDGET_ENUMERATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_GPHOTO2_WIDGET_ENUMERATE</h4>
<pre>public static final&nbsp;int CAP_PROP_GPHOTO2_WIDGET_ENUMERATE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GPHOTO2_WIDGET_ENUMERATE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_GSTREAMER_QUEUE_LENGTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_GSTREAMER_QUEUE_LENGTH</h4>
<pre>public static final&nbsp;int CAP_PROP_GSTREAMER_QUEUE_LENGTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GSTREAMER_QUEUE_LENGTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_GUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_GUID</h4>
<pre>public static final&nbsp;int CAP_PROP_GUID</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_GUID">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_HUE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_HUE</h4>
<pre>public static final&nbsp;int CAP_PROP_HUE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_HUE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_HW_ACCELERATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_HW_ACCELERATION</h4>
<pre>public static final&nbsp;int CAP_PROP_HW_ACCELERATION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_HW_ACCELERATION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_HW_ACCELERATION_USE_OPENCL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_HW_ACCELERATION_USE_OPENCL</h4>
<pre>public static final&nbsp;int CAP_PROP_HW_ACCELERATION_USE_OPENCL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_HW_ACCELERATION_USE_OPENCL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_HW_DEVICE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_HW_DEVICE</h4>
<pre>public static final&nbsp;int CAP_PROP_HW_DEVICE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_HW_DEVICE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_IMAGES_BASE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_IMAGES_BASE</h4>
<pre>public static final&nbsp;int CAP_PROP_IMAGES_BASE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_IMAGES_BASE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_IMAGES_LAST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_IMAGES_LAST</h4>
<pre>public static final&nbsp;int CAP_PROP_IMAGES_LAST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_IMAGES_LAST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_INTELPERC_DEPTH_CONFIDENCE_THRESHOLD">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_INTELPERC_DEPTH_CONFIDENCE_THRESHOLD</h4>
<pre>public static final&nbsp;int CAP_PROP_INTELPERC_DEPTH_CONFIDENCE_THRESHOLD</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_DEPTH_CONFIDENCE_THRESHOLD">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_HORZ">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_HORZ</h4>
<pre>public static final&nbsp;int CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_HORZ</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_HORZ">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_VERT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_VERT</h4>
<pre>public static final&nbsp;int CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_VERT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_VERT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_INTELPERC_DEPTH_LOW_CONFIDENCE_VALUE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_INTELPERC_DEPTH_LOW_CONFIDENCE_VALUE</h4>
<pre>public static final&nbsp;int CAP_PROP_INTELPERC_DEPTH_LOW_CONFIDENCE_VALUE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_DEPTH_LOW_CONFIDENCE_VALUE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_INTELPERC_DEPTH_SATURATION_VALUE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_INTELPERC_DEPTH_SATURATION_VALUE</h4>
<pre>public static final&nbsp;int CAP_PROP_INTELPERC_DEPTH_SATURATION_VALUE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_DEPTH_SATURATION_VALUE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_INTELPERC_PROFILE_COUNT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_INTELPERC_PROFILE_COUNT</h4>
<pre>public static final&nbsp;int CAP_PROP_INTELPERC_PROFILE_COUNT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_PROFILE_COUNT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_INTELPERC_PROFILE_IDX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_INTELPERC_PROFILE_IDX</h4>
<pre>public static final&nbsp;int CAP_PROP_INTELPERC_PROFILE_IDX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_PROFILE_IDX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_IOS_DEVICE_EXPOSURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_IOS_DEVICE_EXPOSURE</h4>
<pre>public static final&nbsp;int CAP_PROP_IOS_DEVICE_EXPOSURE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_IOS_DEVICE_EXPOSURE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_IOS_DEVICE_FLASH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_IOS_DEVICE_FLASH</h4>
<pre>public static final&nbsp;int CAP_PROP_IOS_DEVICE_FLASH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_IOS_DEVICE_FLASH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_IOS_DEVICE_FOCUS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_IOS_DEVICE_FOCUS</h4>
<pre>public static final&nbsp;int CAP_PROP_IOS_DEVICE_FOCUS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_IOS_DEVICE_FOCUS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_IOS_DEVICE_TORCH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_IOS_DEVICE_TORCH</h4>
<pre>public static final&nbsp;int CAP_PROP_IOS_DEVICE_TORCH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_IOS_DEVICE_TORCH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_IOS_DEVICE_WHITEBALANCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_IOS_DEVICE_WHITEBALANCE</h4>
<pre>public static final&nbsp;int CAP_PROP_IOS_DEVICE_WHITEBALANCE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_IOS_DEVICE_WHITEBALANCE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_IRIS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_IRIS</h4>
<pre>public static final&nbsp;int CAP_PROP_IRIS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_IRIS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_ISO_SPEED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_ISO_SPEED</h4>
<pre>public static final&nbsp;int CAP_PROP_ISO_SPEED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_ISO_SPEED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_LRF_HAS_KEY_FRAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_LRF_HAS_KEY_FRAME</h4>
<pre>public static final&nbsp;int CAP_PROP_LRF_HAS_KEY_FRAME</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_LRF_HAS_KEY_FRAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_MODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_MODE</h4>
<pre>public static final&nbsp;int CAP_PROP_MODE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_MODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_MONOCHROME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_MONOCHROME</h4>
<pre>public static final&nbsp;int CAP_PROP_MONOCHROME</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_MONOCHROME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_N_THREADS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_N_THREADS</h4>
<pre>public static final&nbsp;int CAP_PROP_N_THREADS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_N_THREADS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_OBSENSOR_INTRINSIC_CX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_OBSENSOR_INTRINSIC_CX</h4>
<pre>public static final&nbsp;int CAP_PROP_OBSENSOR_INTRINSIC_CX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OBSENSOR_INTRINSIC_CX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_OBSENSOR_INTRINSIC_CY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_OBSENSOR_INTRINSIC_CY</h4>
<pre>public static final&nbsp;int CAP_PROP_OBSENSOR_INTRINSIC_CY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OBSENSOR_INTRINSIC_CY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_OBSENSOR_INTRINSIC_FX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_OBSENSOR_INTRINSIC_FX</h4>
<pre>public static final&nbsp;int CAP_PROP_OBSENSOR_INTRINSIC_FX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OBSENSOR_INTRINSIC_FX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_OBSENSOR_INTRINSIC_FY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_OBSENSOR_INTRINSIC_FY</h4>
<pre>public static final&nbsp;int CAP_PROP_OBSENSOR_INTRINSIC_FY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OBSENSOR_INTRINSIC_FY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_OPEN_TIMEOUT_MSEC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_OPEN_TIMEOUT_MSEC</h4>
<pre>public static final&nbsp;int CAP_PROP_OPEN_TIMEOUT_MSEC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPEN_TIMEOUT_MSEC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_OPENNI_APPROX_FRAME_SYNC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_OPENNI_APPROX_FRAME_SYNC</h4>
<pre>public static final&nbsp;int CAP_PROP_OPENNI_APPROX_FRAME_SYNC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_APPROX_FRAME_SYNC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_OPENNI_BASELINE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_OPENNI_BASELINE</h4>
<pre>public static final&nbsp;int CAP_PROP_OPENNI_BASELINE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_BASELINE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_OPENNI_CIRCLE_BUFFER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_OPENNI_CIRCLE_BUFFER</h4>
<pre>public static final&nbsp;int CAP_PROP_OPENNI_CIRCLE_BUFFER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_CIRCLE_BUFFER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_OPENNI_FOCAL_LENGTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_OPENNI_FOCAL_LENGTH</h4>
<pre>public static final&nbsp;int CAP_PROP_OPENNI_FOCAL_LENGTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_FOCAL_LENGTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_OPENNI_FRAME_MAX_DEPTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_OPENNI_FRAME_MAX_DEPTH</h4>
<pre>public static final&nbsp;int CAP_PROP_OPENNI_FRAME_MAX_DEPTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_FRAME_MAX_DEPTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_OPENNI_GENERATOR_PRESENT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_OPENNI_GENERATOR_PRESENT</h4>
<pre>public static final&nbsp;int CAP_PROP_OPENNI_GENERATOR_PRESENT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_GENERATOR_PRESENT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_OPENNI_MAX_BUFFER_SIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_OPENNI_MAX_BUFFER_SIZE</h4>
<pre>public static final&nbsp;int CAP_PROP_OPENNI_MAX_BUFFER_SIZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_MAX_BUFFER_SIZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_OPENNI_MAX_TIME_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_OPENNI_MAX_TIME_DURATION</h4>
<pre>public static final&nbsp;int CAP_PROP_OPENNI_MAX_TIME_DURATION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_MAX_TIME_DURATION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_OPENNI_OUTPUT_MODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_OPENNI_OUTPUT_MODE</h4>
<pre>public static final&nbsp;int CAP_PROP_OPENNI_OUTPUT_MODE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_OUTPUT_MODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_OPENNI_REGISTRATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_OPENNI_REGISTRATION</h4>
<pre>public static final&nbsp;int CAP_PROP_OPENNI_REGISTRATION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_REGISTRATION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_OPENNI_REGISTRATION_ON">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_OPENNI_REGISTRATION_ON</h4>
<pre>public static final&nbsp;int CAP_PROP_OPENNI_REGISTRATION_ON</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI_REGISTRATION_ON">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_OPENNI2_MIRROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_OPENNI2_MIRROR</h4>
<pre>public static final&nbsp;int CAP_PROP_OPENNI2_MIRROR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI2_MIRROR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_OPENNI2_SYNC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_OPENNI2_SYNC</h4>
<pre>public static final&nbsp;int CAP_PROP_OPENNI2_SYNC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_OPENNI2_SYNC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_ORIENTATION_AUTO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_ORIENTATION_AUTO</h4>
<pre>public static final&nbsp;int CAP_PROP_ORIENTATION_AUTO</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_ORIENTATION_AUTO">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_ORIENTATION_META">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_ORIENTATION_META</h4>
<pre>public static final&nbsp;int CAP_PROP_ORIENTATION_META</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_ORIENTATION_META">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_PAN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_PAN</h4>
<pre>public static final&nbsp;int CAP_PROP_PAN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_PAN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_POS_AVI_RATIO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_POS_AVI_RATIO</h4>
<pre>public static final&nbsp;int CAP_PROP_POS_AVI_RATIO</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_POS_AVI_RATIO">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_POS_FRAMES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_POS_FRAMES</h4>
<pre>public static final&nbsp;int CAP_PROP_POS_FRAMES</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_POS_FRAMES">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_POS_MSEC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_POS_MSEC</h4>
<pre>public static final&nbsp;int CAP_PROP_POS_MSEC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_POS_MSEC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_PVAPI_BINNINGX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_PVAPI_BINNINGX</h4>
<pre>public static final&nbsp;int CAP_PROP_PVAPI_BINNINGX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_PVAPI_BINNINGX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_PVAPI_BINNINGY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_PVAPI_BINNINGY</h4>
<pre>public static final&nbsp;int CAP_PROP_PVAPI_BINNINGY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_PVAPI_BINNINGY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_PVAPI_DECIMATIONHORIZONTAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_PVAPI_DECIMATIONHORIZONTAL</h4>
<pre>public static final&nbsp;int CAP_PROP_PVAPI_DECIMATIONHORIZONTAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_PVAPI_DECIMATIONHORIZONTAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_PVAPI_DECIMATIONVERTICAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_PVAPI_DECIMATIONVERTICAL</h4>
<pre>public static final&nbsp;int CAP_PROP_PVAPI_DECIMATIONVERTICAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_PVAPI_DECIMATIONVERTICAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_PVAPI_FRAMESTARTTRIGGERMODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_PVAPI_FRAMESTARTTRIGGERMODE</h4>
<pre>public static final&nbsp;int CAP_PROP_PVAPI_FRAMESTARTTRIGGERMODE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_PVAPI_FRAMESTARTTRIGGERMODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_PVAPI_MULTICASTIP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_PVAPI_MULTICASTIP</h4>
<pre>public static final&nbsp;int CAP_PROP_PVAPI_MULTICASTIP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_PVAPI_MULTICASTIP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_PVAPI_PIXELFORMAT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_PVAPI_PIXELFORMAT</h4>
<pre>public static final&nbsp;int CAP_PROP_PVAPI_PIXELFORMAT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_PVAPI_PIXELFORMAT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_READ_TIMEOUT_MSEC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_READ_TIMEOUT_MSEC</h4>
<pre>public static final&nbsp;int CAP_PROP_READ_TIMEOUT_MSEC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_READ_TIMEOUT_MSEC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_RECTIFICATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_RECTIFICATION</h4>
<pre>public static final&nbsp;int CAP_PROP_RECTIFICATION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_RECTIFICATION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_ROLL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_ROLL</h4>
<pre>public static final&nbsp;int CAP_PROP_ROLL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_ROLL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_SAR_DEN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_SAR_DEN</h4>
<pre>public static final&nbsp;int CAP_PROP_SAR_DEN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_SAR_DEN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_SAR_NUM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_SAR_NUM</h4>
<pre>public static final&nbsp;int CAP_PROP_SAR_NUM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_SAR_NUM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_SATURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_SATURATION</h4>
<pre>public static final&nbsp;int CAP_PROP_SATURATION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_SATURATION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_SETTINGS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_SETTINGS</h4>
<pre>public static final&nbsp;int CAP_PROP_SETTINGS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_SETTINGS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_SHARPNESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_SHARPNESS</h4>
<pre>public static final&nbsp;int CAP_PROP_SHARPNESS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_SHARPNESS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_SPEED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_SPEED</h4>
<pre>public static final&nbsp;int CAP_PROP_SPEED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_SPEED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_STREAM_OPEN_TIME_USEC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_STREAM_OPEN_TIME_USEC</h4>
<pre>public static final&nbsp;int CAP_PROP_STREAM_OPEN_TIME_USEC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_STREAM_OPEN_TIME_USEC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_TEMPERATURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_TEMPERATURE</h4>
<pre>public static final&nbsp;int CAP_PROP_TEMPERATURE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_TEMPERATURE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_TILT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_TILT</h4>
<pre>public static final&nbsp;int CAP_PROP_TILT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_TILT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_TRIGGER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_TRIGGER</h4>
<pre>public static final&nbsp;int CAP_PROP_TRIGGER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_TRIGGER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_TRIGGER_DELAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_TRIGGER_DELAY</h4>
<pre>public static final&nbsp;int CAP_PROP_TRIGGER_DELAY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_TRIGGER_DELAY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_VIDEO_STREAM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_VIDEO_STREAM</h4>
<pre>public static final&nbsp;int CAP_PROP_VIDEO_STREAM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_VIDEO_STREAM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_VIDEO_TOTAL_CHANNELS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_VIDEO_TOTAL_CHANNELS</h4>
<pre>public static final&nbsp;int CAP_PROP_VIDEO_TOTAL_CHANNELS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_VIDEO_TOTAL_CHANNELS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_VIEWFINDER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_VIEWFINDER</h4>
<pre>public static final&nbsp;int CAP_PROP_VIEWFINDER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_VIEWFINDER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_WB_TEMPERATURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_WB_TEMPERATURE</h4>
<pre>public static final&nbsp;int CAP_PROP_WB_TEMPERATURE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_WB_TEMPERATURE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_WHITE_BALANCE_BLUE_U">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_WHITE_BALANCE_BLUE_U</h4>
<pre>public static final&nbsp;int CAP_PROP_WHITE_BALANCE_BLUE_U</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_WHITE_BALANCE_BLUE_U">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_WHITE_BALANCE_RED_V">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_WHITE_BALANCE_RED_V</h4>
<pre>public static final&nbsp;int CAP_PROP_WHITE_BALANCE_RED_V</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_WHITE_BALANCE_RED_V">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_ACQ_BUFFER_SIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_ACQ_BUFFER_SIZE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_ACQ_BUFFER_SIZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_ACQ_BUFFER_SIZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_ACQ_BUFFER_SIZE_UNIT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_ACQ_BUFFER_SIZE_UNIT</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_ACQ_BUFFER_SIZE_UNIT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_ACQ_BUFFER_SIZE_UNIT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_ACQ_FRAME_BURST_COUNT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_ACQ_FRAME_BURST_COUNT</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_ACQ_FRAME_BURST_COUNT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_ACQ_FRAME_BURST_COUNT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_ACQ_TIMING_MODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_ACQ_TIMING_MODE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_ACQ_TIMING_MODE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_ACQ_TIMING_MODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_COMMIT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_COMMIT</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_COMMIT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_COMMIT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_SIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_SIZE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_SIZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_SIZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_AE_MAX_LIMIT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_AE_MAX_LIMIT</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_AE_MAX_LIMIT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AE_MAX_LIMIT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_AEAG">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_AEAG</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_AEAG</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AEAG">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_AEAG_LEVEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_AEAG_LEVEL</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_AEAG_LEVEL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AEAG_LEVEL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_AEAG_ROI_HEIGHT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_AEAG_ROI_HEIGHT</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_AEAG_ROI_HEIGHT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AEAG_ROI_HEIGHT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_AEAG_ROI_OFFSET_X">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_AEAG_ROI_OFFSET_X</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_AEAG_ROI_OFFSET_X</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AEAG_ROI_OFFSET_X">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_AEAG_ROI_OFFSET_Y">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_AEAG_ROI_OFFSET_Y</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_AEAG_ROI_OFFSET_Y</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AEAG_ROI_OFFSET_Y">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_AEAG_ROI_WIDTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_AEAG_ROI_WIDTH</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_AEAG_ROI_WIDTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AEAG_ROI_WIDTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_AG_MAX_LIMIT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_AG_MAX_LIMIT</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_AG_MAX_LIMIT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AG_MAX_LIMIT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_APPLY_CMS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_APPLY_CMS</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_APPLY_CMS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_APPLY_CMS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_AUTO_BANDWIDTH_CALCULATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_AUTO_BANDWIDTH_CALCULATION</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_AUTO_BANDWIDTH_CALCULATION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AUTO_BANDWIDTH_CALCULATION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_AUTO_WB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_AUTO_WB</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_AUTO_WB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AUTO_WB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_AVAILABLE_BANDWIDTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_AVAILABLE_BANDWIDTH</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_AVAILABLE_BANDWIDTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_AVAILABLE_BANDWIDTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_BINNING_HORIZONTAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_BINNING_HORIZONTAL</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_BINNING_HORIZONTAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_BINNING_HORIZONTAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_BINNING_PATTERN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_BINNING_PATTERN</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_BINNING_PATTERN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_BINNING_PATTERN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_BINNING_SELECTOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_BINNING_SELECTOR</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_BINNING_SELECTOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_BINNING_SELECTOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_BINNING_VERTICAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_BINNING_VERTICAL</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_BINNING_VERTICAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_BINNING_VERTICAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_BPC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_BPC</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_BPC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_BPC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_BUFFER_POLICY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_BUFFER_POLICY</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_BUFFER_POLICY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_BUFFER_POLICY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_BUFFERS_QUEUE_SIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_BUFFERS_QUEUE_SIZE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_BUFFERS_QUEUE_SIZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_BUFFERS_QUEUE_SIZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_CC_MATRIX_00">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_CC_MATRIX_00</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_CC_MATRIX_00</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_00">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_CC_MATRIX_01">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_CC_MATRIX_01</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_CC_MATRIX_01</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_01">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_CC_MATRIX_02">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_CC_MATRIX_02</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_CC_MATRIX_02</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_02">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_CC_MATRIX_03">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_CC_MATRIX_03</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_CC_MATRIX_03</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_03">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_CC_MATRIX_10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_CC_MATRIX_10</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_CC_MATRIX_10</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_10">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_CC_MATRIX_11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_CC_MATRIX_11</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_CC_MATRIX_11</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_11">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_CC_MATRIX_12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_CC_MATRIX_12</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_CC_MATRIX_12</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_12">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_CC_MATRIX_13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_CC_MATRIX_13</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_CC_MATRIX_13</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_13">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_CC_MATRIX_20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_CC_MATRIX_20</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_CC_MATRIX_20</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_20">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_CC_MATRIX_21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_CC_MATRIX_21</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_CC_MATRIX_21</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_21">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_CC_MATRIX_22">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_CC_MATRIX_22</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_CC_MATRIX_22</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_22">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_CC_MATRIX_23">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_CC_MATRIX_23</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_CC_MATRIX_23</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_23">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_CC_MATRIX_30">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_CC_MATRIX_30</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_CC_MATRIX_30</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_30">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_CC_MATRIX_31">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_CC_MATRIX_31</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_CC_MATRIX_31</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_31">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_CC_MATRIX_32">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_CC_MATRIX_32</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_CC_MATRIX_32</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_32">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_CC_MATRIX_33">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_CC_MATRIX_33</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_CC_MATRIX_33</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_33">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_CHIP_TEMP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_CHIP_TEMP</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_CHIP_TEMP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CHIP_TEMP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_CMS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_CMS</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_CMS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_CMS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_COLOR_FILTER_ARRAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_COLOR_FILTER_ARRAY</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_COLOR_FILTER_ARRAY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_COLOR_FILTER_ARRAY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_COLUMN_FPN_CORRECTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_COLUMN_FPN_CORRECTION</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_COLUMN_FPN_CORRECTION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_COLUMN_FPN_CORRECTION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_COOLING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_COOLING</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_COOLING</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_COOLING">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_COUNTER_SELECTOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_COUNTER_SELECTOR</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_COUNTER_SELECTOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_COUNTER_SELECTOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_COUNTER_VALUE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_COUNTER_VALUE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_COUNTER_VALUE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_COUNTER_VALUE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_DATA_FORMAT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_DATA_FORMAT</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_DATA_FORMAT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DATA_FORMAT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_DEBOUNCE_EN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_DEBOUNCE_EN</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_DEBOUNCE_EN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DEBOUNCE_EN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_DEBOUNCE_POL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_DEBOUNCE_POL</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_DEBOUNCE_POL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DEBOUNCE_POL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_DEBOUNCE_T0">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_DEBOUNCE_T0</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_DEBOUNCE_T0</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DEBOUNCE_T0">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_DEBOUNCE_T1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_DEBOUNCE_T1</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_DEBOUNCE_T1</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DEBOUNCE_T1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_DEBUG_LEVEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_DEBUG_LEVEL</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_DEBUG_LEVEL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DEBUG_LEVEL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_DECIMATION_HORIZONTAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_DECIMATION_HORIZONTAL</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_DECIMATION_HORIZONTAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DECIMATION_HORIZONTAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_DECIMATION_PATTERN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_DECIMATION_PATTERN</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_DECIMATION_PATTERN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DECIMATION_PATTERN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_DECIMATION_SELECTOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_DECIMATION_SELECTOR</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_DECIMATION_SELECTOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DECIMATION_SELECTOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_DECIMATION_VERTICAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_DECIMATION_VERTICAL</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_DECIMATION_VERTICAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DECIMATION_VERTICAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_DEFAULT_CC_MATRIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_DEFAULT_CC_MATRIX</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_DEFAULT_CC_MATRIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DEFAULT_CC_MATRIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_DEVICE_MODEL_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_DEVICE_MODEL_ID</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_DEVICE_MODEL_ID</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DEVICE_MODEL_ID">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_DEVICE_RESET">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_DEVICE_RESET</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_DEVICE_RESET</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DEVICE_RESET">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_DEVICE_SN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_DEVICE_SN</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_DEVICE_SN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DEVICE_SN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_DOWNSAMPLING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_DOWNSAMPLING</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_DOWNSAMPLING</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DOWNSAMPLING">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_DOWNSAMPLING_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_DOWNSAMPLING_TYPE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_DOWNSAMPLING_TYPE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_DOWNSAMPLING_TYPE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_EXP_PRIORITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_EXP_PRIORITY</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_EXP_PRIORITY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_EXP_PRIORITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_EXPOSURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_EXPOSURE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_EXPOSURE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_EXPOSURE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_EXPOSURE_BURST_COUNT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_EXPOSURE_BURST_COUNT</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_EXPOSURE_BURST_COUNT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_EXPOSURE_BURST_COUNT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_FFS_ACCESS_KEY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_FFS_ACCESS_KEY</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_FFS_ACCESS_KEY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_FFS_ACCESS_KEY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_FFS_FILE_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_FFS_FILE_ID</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_FFS_FILE_ID</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_FFS_FILE_ID">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_FFS_FILE_SIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_FFS_FILE_SIZE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_FFS_FILE_SIZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_FFS_FILE_SIZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_FRAMERATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_FRAMERATE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_FRAMERATE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_FRAMERATE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_FREE_FFS_SIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_FREE_FFS_SIZE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_FREE_FFS_SIZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_FREE_FFS_SIZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_GAIN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_GAIN</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_GAIN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_GAIN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_GAIN_SELECTOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_GAIN_SELECTOR</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_GAIN_SELECTOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_GAIN_SELECTOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_GAMMAC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_GAMMAC</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_GAMMAC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_GAMMAC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_GAMMAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_GAMMAY</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_GAMMAY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_GAMMAY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_GPI_LEVEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_GPI_LEVEL</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_GPI_LEVEL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_GPI_LEVEL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_GPI_MODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_GPI_MODE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_GPI_MODE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_GPI_MODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_GPI_SELECTOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_GPI_SELECTOR</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_GPI_SELECTOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_GPI_SELECTOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_GPO_MODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_GPO_MODE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_GPO_MODE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_GPO_MODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_GPO_SELECTOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_GPO_SELECTOR</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_GPO_SELECTOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_GPO_SELECTOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_HDR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_HDR</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_HDR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_HDR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_HDR_KNEEPOINT_COUNT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_HDR_KNEEPOINT_COUNT</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_HDR_KNEEPOINT_COUNT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_HDR_KNEEPOINT_COUNT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_HDR_T1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_HDR_T1</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_HDR_T1</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_HDR_T1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_HDR_T2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_HDR_T2</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_HDR_T2</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_HDR_T2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_HEIGHT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_HEIGHT</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_HEIGHT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_HEIGHT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_HOUS_BACK_SIDE_TEMP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_HOUS_BACK_SIDE_TEMP</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_HOUS_BACK_SIDE_TEMP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_HOUS_BACK_SIDE_TEMP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_HOUS_TEMP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_HOUS_TEMP</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_HOUS_TEMP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_HOUS_TEMP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_HW_REVISION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_HW_REVISION</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_HW_REVISION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_HW_REVISION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_IMAGE_BLACK_LEVEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_IMAGE_BLACK_LEVEL</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_IMAGE_BLACK_LEVEL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_IMAGE_BLACK_LEVEL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_IMAGE_DATA_BIT_DEPTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_IMAGE_DATA_BIT_DEPTH</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_IMAGE_DATA_BIT_DEPTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_IMAGE_DATA_BIT_DEPTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_IMAGE_DATA_FORMAT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_IMAGE_DATA_FORMAT</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_IMAGE_DATA_FORMAT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_IMAGE_DATA_FORMAT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_IMAGE_DATA_FORMAT_RGB32_ALPHA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_IMAGE_DATA_FORMAT_RGB32_ALPHA</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_IMAGE_DATA_FORMAT_RGB32_ALPHA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_IMAGE_DATA_FORMAT_RGB32_ALPHA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_IMAGE_IS_COLOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_IMAGE_IS_COLOR</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_IMAGE_IS_COLOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_IMAGE_IS_COLOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_IMAGE_PAYLOAD_SIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_IMAGE_PAYLOAD_SIZE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_IMAGE_PAYLOAD_SIZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_IMAGE_PAYLOAD_SIZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_IS_COOLED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_IS_COOLED</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_IS_COOLED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_IS_COOLED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_IS_DEVICE_EXIST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_IS_DEVICE_EXIST</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_IS_DEVICE_EXIST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_IS_DEVICE_EXIST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_KNEEPOINT1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_KNEEPOINT1</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_KNEEPOINT1</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_KNEEPOINT1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_KNEEPOINT2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_KNEEPOINT2</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_KNEEPOINT2</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_KNEEPOINT2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_LED_MODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_LED_MODE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_LED_MODE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LED_MODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_LED_SELECTOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_LED_SELECTOR</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_LED_SELECTOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LED_SELECTOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_LENS_APERTURE_VALUE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_LENS_APERTURE_VALUE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_LENS_APERTURE_VALUE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_APERTURE_VALUE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_LENS_FEATURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_LENS_FEATURE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_LENS_FEATURE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_FEATURE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_LENS_FEATURE_SELECTOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_LENS_FEATURE_SELECTOR</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_LENS_FEATURE_SELECTOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_FEATURE_SELECTOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_LENS_FOCAL_LENGTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_LENS_FOCAL_LENGTH</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_LENS_FOCAL_LENGTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_FOCAL_LENGTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_LENS_FOCUS_DISTANCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_LENS_FOCUS_DISTANCE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_LENS_FOCUS_DISTANCE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_FOCUS_DISTANCE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_LENS_FOCUS_MOVE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_LENS_FOCUS_MOVE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_LENS_FOCUS_MOVE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_FOCUS_MOVE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_LENS_FOCUS_MOVEMENT_VALUE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_LENS_FOCUS_MOVEMENT_VALUE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_LENS_FOCUS_MOVEMENT_VALUE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_FOCUS_MOVEMENT_VALUE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_LENS_MODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_LENS_MODE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_LENS_MODE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_MODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_LIMIT_BANDWIDTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_LIMIT_BANDWIDTH</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_LIMIT_BANDWIDTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LIMIT_BANDWIDTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_LUT_EN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_LUT_EN</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_LUT_EN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LUT_EN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_LUT_INDEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_LUT_INDEX</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_LUT_INDEX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LUT_INDEX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_LUT_VALUE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_LUT_VALUE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_LUT_VALUE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_LUT_VALUE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_MANUAL_WB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_MANUAL_WB</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_MANUAL_WB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_MANUAL_WB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_OFFSET_X">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_OFFSET_X</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_OFFSET_X</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_OFFSET_X">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_OFFSET_Y">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_OFFSET_Y</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_OFFSET_Y</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_OFFSET_Y">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_OUTPUT_DATA_BIT_DEPTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_OUTPUT_DATA_BIT_DEPTH</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_OUTPUT_DATA_BIT_DEPTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_OUTPUT_DATA_BIT_DEPTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_OUTPUT_DATA_PACKING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_OUTPUT_DATA_PACKING</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_OUTPUT_DATA_PACKING</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_OUTPUT_DATA_PACKING">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_OUTPUT_DATA_PACKING_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_OUTPUT_DATA_PACKING_TYPE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_OUTPUT_DATA_PACKING_TYPE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_OUTPUT_DATA_PACKING_TYPE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_RECENT_FRAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_RECENT_FRAME</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_RECENT_FRAME</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_RECENT_FRAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_REGION_MODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_REGION_MODE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_REGION_MODE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_REGION_MODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_REGION_SELECTOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_REGION_SELECTOR</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_REGION_SELECTOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_REGION_SELECTOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_ROW_FPN_CORRECTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_ROW_FPN_CORRECTION</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_ROW_FPN_CORRECTION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_ROW_FPN_CORRECTION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_SENSOR_BOARD_TEMP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_SENSOR_BOARD_TEMP</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_SENSOR_BOARD_TEMP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_BOARD_TEMP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_SENSOR_CLOCK_FREQ_HZ">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_SENSOR_CLOCK_FREQ_HZ</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_SENSOR_CLOCK_FREQ_HZ</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_CLOCK_FREQ_HZ">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_SENSOR_CLOCK_FREQ_INDEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_SENSOR_CLOCK_FREQ_INDEX</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_SENSOR_CLOCK_FREQ_INDEX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_CLOCK_FREQ_INDEX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_SENSOR_DATA_BIT_DEPTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_SENSOR_DATA_BIT_DEPTH</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_SENSOR_DATA_BIT_DEPTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_DATA_BIT_DEPTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_SENSOR_FEATURE_SELECTOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_SENSOR_FEATURE_SELECTOR</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_SENSOR_FEATURE_SELECTOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_FEATURE_SELECTOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_SENSOR_FEATURE_VALUE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_SENSOR_FEATURE_VALUE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_SENSOR_FEATURE_VALUE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_FEATURE_VALUE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_SENSOR_MODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_SENSOR_MODE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_SENSOR_MODE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_MODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_SENSOR_OUTPUT_CHANNEL_COUNT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_SENSOR_OUTPUT_CHANNEL_COUNT</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_SENSOR_OUTPUT_CHANNEL_COUNT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_OUTPUT_CHANNEL_COUNT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_SENSOR_TAPS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_SENSOR_TAPS</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_SENSOR_TAPS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_TAPS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_SHARPNESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_SHARPNESS</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_SHARPNESS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SHARPNESS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_SHUTTER_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_SHUTTER_TYPE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_SHUTTER_TYPE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_SHUTTER_TYPE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_TARGET_TEMP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_TARGET_TEMP</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_TARGET_TEMP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TARGET_TEMP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_TEST_PATTERN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_TEST_PATTERN</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_TEST_PATTERN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TEST_PATTERN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_TEST_PATTERN_GENERATOR_SELECTOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_TEST_PATTERN_GENERATOR_SELECTOR</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_TEST_PATTERN_GENERATOR_SELECTOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TEST_PATTERN_GENERATOR_SELECTOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_TIMEOUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_TIMEOUT</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_TIMEOUT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TIMEOUT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_TRANSPORT_PIXEL_FORMAT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_TRANSPORT_PIXEL_FORMAT</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_TRANSPORT_PIXEL_FORMAT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TRANSPORT_PIXEL_FORMAT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_TRG_DELAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_TRG_DELAY</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_TRG_DELAY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TRG_DELAY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_TRG_SELECTOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_TRG_SELECTOR</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_TRG_SELECTOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TRG_SELECTOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_TRG_SOFTWARE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_TRG_SOFTWARE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_TRG_SOFTWARE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TRG_SOFTWARE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_TRG_SOURCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_TRG_SOURCE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_TRG_SOURCE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TRG_SOURCE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_TS_RST_MODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_TS_RST_MODE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_TS_RST_MODE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TS_RST_MODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_TS_RST_SOURCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_TS_RST_SOURCE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_TS_RST_SOURCE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_TS_RST_SOURCE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_USED_FFS_SIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_USED_FFS_SIZE</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_USED_FFS_SIZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_USED_FFS_SIZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_WB_KB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_WB_KB</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_WB_KB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_WB_KB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_WB_KG">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_WB_KG</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_WB_KG</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_WB_KG">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_WB_KR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_WB_KR</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_WB_KR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_WB_KR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_XI_WIDTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_XI_WIDTH</h4>
<pre>public static final&nbsp;int CAP_PROP_XI_WIDTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_XI_WIDTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PROP_ZOOM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PROP_ZOOM</h4>
<pre>public static final&nbsp;int CAP_PROP_ZOOM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PROP_ZOOM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PVAPI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PVAPI</h4>
<pre>public static final&nbsp;int CAP_PVAPI</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PVAPI_DECIMATION_2OUTOF16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PVAPI_DECIMATION_2OUTOF16</h4>
<pre>public static final&nbsp;int CAP_PVAPI_DECIMATION_2OUTOF16</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_DECIMATION_2OUTOF16">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PVAPI_DECIMATION_2OUTOF4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PVAPI_DECIMATION_2OUTOF4</h4>
<pre>public static final&nbsp;int CAP_PVAPI_DECIMATION_2OUTOF4</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_DECIMATION_2OUTOF4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PVAPI_DECIMATION_2OUTOF8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PVAPI_DECIMATION_2OUTOF8</h4>
<pre>public static final&nbsp;int CAP_PVAPI_DECIMATION_2OUTOF8</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_DECIMATION_2OUTOF8">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PVAPI_DECIMATION_OFF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PVAPI_DECIMATION_OFF</h4>
<pre>public static final&nbsp;int CAP_PVAPI_DECIMATION_OFF</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_DECIMATION_OFF">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PVAPI_FSTRIGMODE_FIXEDRATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PVAPI_FSTRIGMODE_FIXEDRATE</h4>
<pre>public static final&nbsp;int CAP_PVAPI_FSTRIGMODE_FIXEDRATE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_FSTRIGMODE_FIXEDRATE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PVAPI_FSTRIGMODE_FREERUN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PVAPI_FSTRIGMODE_FREERUN</h4>
<pre>public static final&nbsp;int CAP_PVAPI_FSTRIGMODE_FREERUN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_FSTRIGMODE_FREERUN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PVAPI_FSTRIGMODE_SOFTWARE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PVAPI_FSTRIGMODE_SOFTWARE</h4>
<pre>public static final&nbsp;int CAP_PVAPI_FSTRIGMODE_SOFTWARE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_FSTRIGMODE_SOFTWARE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PVAPI_FSTRIGMODE_SYNCIN1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PVAPI_FSTRIGMODE_SYNCIN1</h4>
<pre>public static final&nbsp;int CAP_PVAPI_FSTRIGMODE_SYNCIN1</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_FSTRIGMODE_SYNCIN1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PVAPI_FSTRIGMODE_SYNCIN2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PVAPI_FSTRIGMODE_SYNCIN2</h4>
<pre>public static final&nbsp;int CAP_PVAPI_FSTRIGMODE_SYNCIN2</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_FSTRIGMODE_SYNCIN2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PVAPI_PIXELFORMAT_BAYER16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PVAPI_PIXELFORMAT_BAYER16</h4>
<pre>public static final&nbsp;int CAP_PVAPI_PIXELFORMAT_BAYER16</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_BAYER16">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PVAPI_PIXELFORMAT_BAYER8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PVAPI_PIXELFORMAT_BAYER8</h4>
<pre>public static final&nbsp;int CAP_PVAPI_PIXELFORMAT_BAYER8</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_BAYER8">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PVAPI_PIXELFORMAT_BGR24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PVAPI_PIXELFORMAT_BGR24</h4>
<pre>public static final&nbsp;int CAP_PVAPI_PIXELFORMAT_BGR24</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_BGR24">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PVAPI_PIXELFORMAT_BGRA32">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PVAPI_PIXELFORMAT_BGRA32</h4>
<pre>public static final&nbsp;int CAP_PVAPI_PIXELFORMAT_BGRA32</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_BGRA32">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PVAPI_PIXELFORMAT_MONO16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PVAPI_PIXELFORMAT_MONO16</h4>
<pre>public static final&nbsp;int CAP_PVAPI_PIXELFORMAT_MONO16</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_MONO16">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PVAPI_PIXELFORMAT_MONO8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PVAPI_PIXELFORMAT_MONO8</h4>
<pre>public static final&nbsp;int CAP_PVAPI_PIXELFORMAT_MONO8</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_MONO8">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PVAPI_PIXELFORMAT_RGB24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PVAPI_PIXELFORMAT_RGB24</h4>
<pre>public static final&nbsp;int CAP_PVAPI_PIXELFORMAT_RGB24</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_RGB24">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_PVAPI_PIXELFORMAT_RGBA32">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_PVAPI_PIXELFORMAT_RGBA32</h4>
<pre>public static final&nbsp;int CAP_PVAPI_PIXELFORMAT_RGBA32</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_RGBA32">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_QT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_QT</h4>
<pre>public static final&nbsp;int CAP_QT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_QT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_REALSENSE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_REALSENSE</h4>
<pre>public static final&nbsp;int CAP_REALSENSE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_REALSENSE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_UEYE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_UEYE</h4>
<pre>public static final&nbsp;int CAP_UEYE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_UEYE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_UNICAP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_UNICAP</h4>
<pre>public static final&nbsp;int CAP_UNICAP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_UNICAP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_V4L">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_V4L</h4>
<pre>public static final&nbsp;int CAP_V4L</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_V4L">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_V4L2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_V4L2</h4>
<pre>public static final&nbsp;int CAP_V4L2</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_V4L2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_VFW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_VFW</h4>
<pre>public static final&nbsp;int CAP_VFW</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_VFW">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_WINRT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_WINRT</h4>
<pre>public static final&nbsp;int CAP_WINRT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_WINRT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_XIAPI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_XIAPI</h4>
<pre>public static final&nbsp;int CAP_XIAPI</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_XIAPI">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CAP_XINE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CAP_XINE</h4>
<pre>public static final&nbsp;int CAP_XINE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.CAP_XINE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VIDEO_ACCELERATION_ANY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VIDEO_ACCELERATION_ANY</h4>
<pre>public static final&nbsp;int VIDEO_ACCELERATION_ANY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEO_ACCELERATION_ANY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VIDEO_ACCELERATION_D3D11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VIDEO_ACCELERATION_D3D11</h4>
<pre>public static final&nbsp;int VIDEO_ACCELERATION_D3D11</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEO_ACCELERATION_D3D11">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VIDEO_ACCELERATION_MFX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VIDEO_ACCELERATION_MFX</h4>
<pre>public static final&nbsp;int VIDEO_ACCELERATION_MFX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEO_ACCELERATION_MFX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VIDEO_ACCELERATION_NONE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VIDEO_ACCELERATION_NONE</h4>
<pre>public static final&nbsp;int VIDEO_ACCELERATION_NONE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEO_ACCELERATION_NONE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VIDEO_ACCELERATION_VAAPI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VIDEO_ACCELERATION_VAAPI</h4>
<pre>public static final&nbsp;int VIDEO_ACCELERATION_VAAPI</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEO_ACCELERATION_VAAPI">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VIDEOWRITER_PROP_DEPTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VIDEOWRITER_PROP_DEPTH</h4>
<pre>public static final&nbsp;int VIDEOWRITER_PROP_DEPTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_DEPTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VIDEOWRITER_PROP_FRAMEBYTES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VIDEOWRITER_PROP_FRAMEBYTES</h4>
<pre>public static final&nbsp;int VIDEOWRITER_PROP_FRAMEBYTES</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_FRAMEBYTES">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VIDEOWRITER_PROP_HW_ACCELERATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VIDEOWRITER_PROP_HW_ACCELERATION</h4>
<pre>public static final&nbsp;int VIDEOWRITER_PROP_HW_ACCELERATION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_HW_ACCELERATION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VIDEOWRITER_PROP_HW_ACCELERATION_USE_OPENCL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VIDEOWRITER_PROP_HW_ACCELERATION_USE_OPENCL</h4>
<pre>public static final&nbsp;int VIDEOWRITER_PROP_HW_ACCELERATION_USE_OPENCL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_HW_ACCELERATION_USE_OPENCL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VIDEOWRITER_PROP_HW_DEVICE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VIDEOWRITER_PROP_HW_DEVICE</h4>
<pre>public static final&nbsp;int VIDEOWRITER_PROP_HW_DEVICE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_HW_DEVICE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VIDEOWRITER_PROP_IS_COLOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VIDEOWRITER_PROP_IS_COLOR</h4>
<pre>public static final&nbsp;int VIDEOWRITER_PROP_IS_COLOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_IS_COLOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VIDEOWRITER_PROP_NSTRIPES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VIDEOWRITER_PROP_NSTRIPES</h4>
<pre>public static final&nbsp;int VIDEOWRITER_PROP_NSTRIPES</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_NSTRIPES">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VIDEOWRITER_PROP_QUALITY">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>VIDEOWRITER_PROP_QUALITY</h4>
<pre>public static final&nbsp;int VIDEOWRITER_PROP_QUALITY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.videoio.Videoio.VIDEOWRITER_PROP_QUALITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Videoio--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Videoio</h4>
<pre>public&nbsp;Videoio()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getBackendName-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBackendName</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getBackendName(int&nbsp;api)</pre>
<div class="block">Returns backend API name or "UnknownVideoAPI(xxx)"</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>api</code> - backend ID (#VideoCaptureAPIs)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getCameraBackendPluginVersion-int-int:A-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCameraBackendPluginVersion</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getCameraBackendPluginVersion(int&nbsp;api,
                                                             int[]&nbsp;version_ABI,
                                                             int[]&nbsp;version_API)</pre>
<div class="block">Returns description and ABI/API version of videoio plugin's camera interface</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>api</code> - automatically generated</dd>
<dd><code>version_ABI</code> - automatically generated</dd>
<dd><code>version_API</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getStreamBackendPluginVersion-int-int:A-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStreamBackendPluginVersion</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getStreamBackendPluginVersion(int&nbsp;api,
                                                             int[]&nbsp;version_ABI,
                                                             int[]&nbsp;version_API)</pre>
<div class="block">Returns description and ABI/API version of videoio plugin's stream capture interface</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>api</code> - automatically generated</dd>
<dd><code>version_ABI</code> - automatically generated</dd>
<dd><code>version_API</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getWriterBackendPluginVersion-int-int:A-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWriterBackendPluginVersion</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getWriterBackendPluginVersion(int&nbsp;api,
                                                             int[]&nbsp;version_ABI,
                                                             int[]&nbsp;version_API)</pre>
<div class="block">Returns description and ABI/API version of videoio plugin's writer interface</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>api</code> - automatically generated</dd>
<dd><code>version_ABI</code> - automatically generated</dd>
<dd><code>version_API</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="hasBackend-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hasBackend</h4>
<pre>public static&nbsp;boolean&nbsp;hasBackend(int&nbsp;api)</pre>
<div class="block">Returns true if backend is available</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>api</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="isBackendBuiltIn-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isBackendBuiltIn</h4>
<pre>public static&nbsp;boolean&nbsp;isBackendBuiltIn(int&nbsp;api)</pre>
<div class="block">Returns true if backend is built in (false if backend is used as plugin)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>api</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/videoio/VideoCapture.html" title="class in org.opencv.videoio"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/videoio/VideoWriter.html" title="class in org.opencv.videoio"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/videoio/Videoio.html" target="_top">Frames</a></li>
<li><a href="Videoio.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
