<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:24 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>VideoCapture (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="VideoCapture (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/opencv/videoio/Videoio.html" title="class in org.opencv.videoio"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/videoio/VideoCapture.html" target="_top">Frames</a></li>
<li><a href="VideoCapture.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.videoio</div>
<h2 title="Class VideoCapture" class="title">Class VideoCapture</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.videoio.VideoCapture</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">VideoCapture</span>
extends java.lang.Object</pre>
<div class="block">Class for video capturing from video files, image sequences or cameras.

 The class provides C++ API for capturing video from cameras or for reading video files and image sequences.

 Here is how the class can be used:
 INCLUDE: samples/cpp/videocapture_basic.cpp

 <b>Note:</b> In REF: videoio_c "C API" the black-box structure <code>CvCapture</code> is used instead of %VideoCapture.
 <b>Note:</b>
 <ul>
   <li>
    (C++) A basic sample on using the %VideoCapture interface can be found at
     <code>OPENCV_SOURCE_CODE/samples/cpp/videocapture_starter.cpp</code>
   </li>
   <li>
    (Python) A basic sample on using the %VideoCapture interface can be found at
     <code>OPENCV_SOURCE_CODE/samples/python/video.py</code>
   </li>
   <li>
    (Python) A multi threaded video processing sample can be found at
     <code>OPENCV_SOURCE_CODE/samples/python/video_threaded.py</code>
   </li>
   <li>
    (Python) %VideoCapture sample showcasing some features of the Video4Linux2 backend
     <code>OPENCV_SOURCE_CODE/samples/python/video_v4l2.py</code>
   </li>
 </ul></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#VideoCapture--">VideoCapture</a></span>()</code>
<div class="block">Default constructor
     <b>Note:</b> In REF: videoio_c "C API", when you finished working with video, release CvCapture structure with
     cvReleaseCapture(), or use Ptr&lt;CvCapture&gt; that calls cvReleaseCapture() automatically in the
     destructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#VideoCapture-int-">VideoCapture</a></span>(int&nbsp;index)</code>
<div class="block">Opens a camera for video capturing</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#VideoCapture-int-int-">VideoCapture</a></span>(int&nbsp;index,
            int&nbsp;apiPreference)</code>
<div class="block">Opens a camera for video capturing</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#VideoCapture-int-int-org.opencv.core.MatOfInt-">VideoCapture</a></span>(int&nbsp;index,
            int&nbsp;apiPreference,
            <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code>
<div class="block">Opens a camera for video capturing with API Preference and parameters

     The <code>params</code> parameter allows to specify extra parameters encoded as pairs <code>(paramId_1, paramValue_1, paramId_2, paramValue_2, ...)</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#VideoCapture-java.lang.String-">VideoCapture</a></span>(java.lang.String&nbsp;filename)</code>
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing with API Preference</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#VideoCapture-java.lang.String-int-">VideoCapture</a></span>(java.lang.String&nbsp;filename,
            int&nbsp;apiPreference)</code>
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing with API Preference</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#VideoCapture-java.lang.String-int-org.opencv.core.MatOfInt-">VideoCapture</a></span>(java.lang.String&nbsp;filename,
            int&nbsp;apiPreference,
            <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code>
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing with API Preference and parameters

     The <code>params</code> parameter allows to specify extra parameters encoded as pairs <code>(paramId_1, paramValue_1, paramId_2, paramValue_2, ...)</code>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/videoio/VideoCapture.html" title="class in org.opencv.videoio">VideoCapture</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#get-int-">get</a></span>(int&nbsp;propId)</code>
<div class="block">Returns the specified VideoCapture property</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#getBackendName--">getBackendName</a></span>()</code>
<div class="block">Returns used backend API name

      <b>Note:</b> Stream should be opened.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#getExceptionMode--">getExceptionMode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#getNativeObjAddr--">getNativeObjAddr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#grab--">grab</a></span>()</code>
<div class="block">Grabs the next frame from video file or capturing device.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#isOpened--">isOpened</a></span>()</code>
<div class="block">Returns true if video capturing has been initialized already.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#open-int-">open</a></span>(int&nbsp;index)</code>
<div class="block">Opens a camera for video capturing

     

     Parameters are same as the constructor VideoCapture(int index, int apiPreference = CAP_ANY)</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#open-int-int-">open</a></span>(int&nbsp;index,
    int&nbsp;apiPreference)</code>
<div class="block">Opens a camera for video capturing

     

     Parameters are same as the constructor VideoCapture(int index, int apiPreference = CAP_ANY)</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#open-int-int-org.opencv.core.MatOfInt-">open</a></span>(int&nbsp;index,
    int&nbsp;apiPreference,
    <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code>
<div class="block">Opens a camera for video capturing with API Preference and parameters

     

     The <code>params</code> parameter allows to specify extra parameters encoded as pairs <code>(paramId_1, paramValue_1, paramId_2, paramValue_2, ...)</code>.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#open-java.lang.String-">open</a></span>(java.lang.String&nbsp;filename)</code>
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#open-java.lang.String-int-">open</a></span>(java.lang.String&nbsp;filename,
    int&nbsp;apiPreference)</code>
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#open-java.lang.String-int-org.opencv.core.MatOfInt-">open</a></span>(java.lang.String&nbsp;filename,
    int&nbsp;apiPreference,
    <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code>
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing with API Preference and parameters

     

     The <code>params</code> parameter allows to specify extra parameters encoded as pairs <code>(paramId_1, paramValue_1, paramId_2, paramValue_2, ...)</code>.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#read-org.opencv.core.Mat-">read</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</code>
<div class="block">Grabs, decodes and returns the next video frame.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#release--">release</a></span>()</code>
<div class="block">Closes video file or capturing device.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#retrieve-org.opencv.core.Mat-">retrieve</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</code>
<div class="block">Decodes and returns the grabbed video frame.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#retrieve-org.opencv.core.Mat-int-">retrieve</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
        int&nbsp;flag)</code>
<div class="block">Decodes and returns the grabbed video frame.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#set-int-double-">set</a></span>(int&nbsp;propId,
   double&nbsp;value)</code>
<div class="block">Sets a property in the VideoCapture.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoCapture.html#setExceptionMode-boolean-">setExceptionMode</a></span>(boolean&nbsp;enable)</code>
<div class="block">Switches exceptions mode

 methods raise exceptions if not successful instead of returning an error code</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="VideoCapture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VideoCapture</h4>
<pre>public&nbsp;VideoCapture()</pre>
<div class="block">Default constructor
     <b>Note:</b> In REF: videoio_c "C API", when you finished working with video, release CvCapture structure with
     cvReleaseCapture(), or use Ptr&lt;CvCapture&gt; that calls cvReleaseCapture() automatically in the
     destructor.</div>
</li>
</ul>
<a name="VideoCapture-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VideoCapture</h4>
<pre>public&nbsp;VideoCapture(int&nbsp;index)</pre>
<div class="block">Opens a camera for video capturing</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - id of the video capturing device to open. To open default camera using default backend just pass 0.
     (to backward compatibility usage of camera_id + domain_offset (CAP_*) is valid when apiPreference is CAP_ANY)
     implementation if multiple are available: e.g. cv::CAP_DSHOW or cv::CAP_MSMF or cv::CAP_V4L.

     SEE: cv::VideoCaptureAPIs</dd>
</dl>
</li>
</ul>
<a name="VideoCapture-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VideoCapture</h4>
<pre>public&nbsp;VideoCapture(int&nbsp;index,
                    int&nbsp;apiPreference)</pre>
<div class="block">Opens a camera for video capturing</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - id of the video capturing device to open. To open default camera using default backend just pass 0.
     (to backward compatibility usage of camera_id + domain_offset (CAP_*) is valid when apiPreference is CAP_ANY)</dd>
<dd><code>apiPreference</code> - preferred Capture API backends to use. Can be used to enforce a specific reader
     implementation if multiple are available: e.g. cv::CAP_DSHOW or cv::CAP_MSMF or cv::CAP_V4L.

     SEE: cv::VideoCaptureAPIs</dd>
</dl>
</li>
</ul>
<a name="VideoCapture-int-int-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VideoCapture</h4>
<pre>public&nbsp;VideoCapture(int&nbsp;index,
                    int&nbsp;apiPreference,
                    <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</pre>
<div class="block">Opens a camera for video capturing with API Preference and parameters

     The <code>params</code> parameter allows to specify extra parameters encoded as pairs <code>(paramId_1, paramValue_1, paramId_2, paramValue_2, ...)</code>.
     See cv::VideoCaptureProperties</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - automatically generated</dd>
<dd><code>apiPreference</code> - automatically generated</dd>
<dd><code>params</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="VideoCapture-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VideoCapture</h4>
<pre>public&nbsp;VideoCapture(java.lang.String&nbsp;filename)</pre>
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing with API Preference</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - it can be:
 <ul>
   <li>
      name of video file (eg. <code>video.avi</code>)
   </li>
   <li>
      or image sequence (eg. <code>img_%02d.jpg</code>, which will read samples like <code>img_00.jpg, img_01.jpg, img_02.jpg, ...</code>)
   </li>
   <li>
      or URL of video stream (eg. <code>protocol://host:port/script_name?script_params|auth</code>)
   </li>
   <li>
      or GStreamer pipeline string in gst-launch tool format in case if GStreamer is used as backend
       Note that each video stream or IP camera feed has its own URL scheme. Please refer to the
       documentation of source stream to know the right URL.
   </li>
 </ul>
     implementation if multiple are available: e.g. cv::CAP_FFMPEG or cv::CAP_IMAGES or cv::CAP_DSHOW.

     SEE: cv::VideoCaptureAPIs</dd>
</dl>
</li>
</ul>
<a name="VideoCapture-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VideoCapture</h4>
<pre>public&nbsp;VideoCapture(java.lang.String&nbsp;filename,
                    int&nbsp;apiPreference)</pre>
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing with API Preference</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - it can be:
 <ul>
   <li>
      name of video file (eg. <code>video.avi</code>)
   </li>
   <li>
      or image sequence (eg. <code>img_%02d.jpg</code>, which will read samples like <code>img_00.jpg, img_01.jpg, img_02.jpg, ...</code>)
   </li>
   <li>
      or URL of video stream (eg. <code>protocol://host:port/script_name?script_params|auth</code>)
   </li>
   <li>
      or GStreamer pipeline string in gst-launch tool format in case if GStreamer is used as backend
       Note that each video stream or IP camera feed has its own URL scheme. Please refer to the
       documentation of source stream to know the right URL.
   </li>
 </ul></dd>
<dd><code>apiPreference</code> - preferred Capture API backends to use. Can be used to enforce a specific reader
     implementation if multiple are available: e.g. cv::CAP_FFMPEG or cv::CAP_IMAGES or cv::CAP_DSHOW.

     SEE: cv::VideoCaptureAPIs</dd>
</dl>
</li>
</ul>
<a name="VideoCapture-java.lang.String-int-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>VideoCapture</h4>
<pre>public&nbsp;VideoCapture(java.lang.String&nbsp;filename,
                    int&nbsp;apiPreference,
                    <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</pre>
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing with API Preference and parameters

     The <code>params</code> parameter allows to specify extra parameters encoded as pairs <code>(paramId_1, paramValue_1, paramId_2, paramValue_2, ...)</code>.
     See cv::VideoCaptureProperties</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - automatically generated</dd>
<dd><code>apiPreference</code> - automatically generated</dd>
<dd><code>params</code> - automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/videoio/VideoCapture.html" title="class in org.opencv.videoio">VideoCapture</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="get-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre>public&nbsp;double&nbsp;get(int&nbsp;propId)</pre>
<div class="block">Returns the specified VideoCapture property</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>propId</code> - Property identifier from cv::VideoCaptureProperties (eg. cv::CAP_PROP_POS_MSEC, cv::CAP_PROP_POS_FRAMES, ...)
     or one from REF: videoio_flags_others</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Value for the specified property. Value 0 is returned when querying a property that is
     not supported by the backend used by the VideoCapture instance.

     <b>Note:</b> Reading / writing properties involves many layers. Some unexpected result might happens
     along this chain.
     <code>
     VideoCapture -&gt; API Backend -&gt; Operating System -&gt; Device Driver -&gt; Device Hardware
     </code>
     The returned value might be different from what really used by the device or it could be encoded
     using device dependent rules (eg. steps or percentage). Effective behaviour depends from device
     driver and API Backend</dd>
</dl>
</li>
</ul>
<a name="getBackendName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBackendName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getBackendName()</pre>
<div class="block">Returns used backend API name

      <b>Note:</b> Stream should be opened.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getExceptionMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExceptionMode</h4>
<pre>public&nbsp;boolean&nbsp;getExceptionMode()</pre>
</li>
</ul>
<a name="getNativeObjAddr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNativeObjAddr</h4>
<pre>public&nbsp;long&nbsp;getNativeObjAddr()</pre>
</li>
</ul>
<a name="grab--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>grab</h4>
<pre>public&nbsp;boolean&nbsp;grab()</pre>
<div class="block">Grabs the next frame from video file or capturing device.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> (non-zero) in the case of success.

     The method/function grabs the next frame from video file or camera and returns true (non-zero) in
     the case of success.

     The primary use of the function is in multi-camera environments, especially when the cameras do not
     have hardware synchronization. That is, you call VideoCapture::grab() for each camera and after that
     call the slower method VideoCapture::retrieve() to decode and get frame from each camera. This way
     the overhead on demosaicing or motion jpeg decompression etc. is eliminated and the retrieved frames
     from different cameras will be closer in time.

     Also, when a connected camera is multi-head (for example, a stereo camera or a Kinect device), the
     correct way of retrieving data from it is to call VideoCapture::grab() first and then call
     VideoCapture::retrieve() one or more times with different values of the channel parameter.

     REF: tutorial_kinect_openni</dd>
</dl>
</li>
</ul>
<a name="isOpened--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isOpened</h4>
<pre>public&nbsp;boolean&nbsp;isOpened()</pre>
<div class="block">Returns true if video capturing has been initialized already.

     If the previous call to VideoCapture constructor or VideoCapture::open() succeeded, the method returns
     true.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="open-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open(int&nbsp;index)</pre>
<div class="block">Opens a camera for video capturing

     

     Parameters are same as the constructor VideoCapture(int index, int apiPreference = CAP_ANY)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the camera has been successfully opened.

     The method first calls VideoCapture::release to close the already opened file or camera.</dd>
</dl>
</li>
</ul>
<a name="open-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open(int&nbsp;index,
                    int&nbsp;apiPreference)</pre>
<div class="block">Opens a camera for video capturing

     

     Parameters are same as the constructor VideoCapture(int index, int apiPreference = CAP_ANY)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - automatically generated</dd>
<dd><code>apiPreference</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the camera has been successfully opened.

     The method first calls VideoCapture::release to close the already opened file or camera.</dd>
</dl>
</li>
</ul>
<a name="open-int-int-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open(int&nbsp;index,
                    int&nbsp;apiPreference,
                    <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</pre>
<div class="block">Opens a camera for video capturing with API Preference and parameters

     

     The <code>params</code> parameter allows to specify extra parameters encoded as pairs <code>(paramId_1, paramValue_1, paramId_2, paramValue_2, ...)</code>.
     See cv::VideoCaptureProperties</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - automatically generated</dd>
<dd><code>apiPreference</code> - automatically generated</dd>
<dd><code>params</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the camera has been successfully opened.

     The method first calls VideoCapture::release to close the already opened file or camera.</dd>
</dl>
</li>
</ul>
<a name="open-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open(java.lang.String&nbsp;filename)</pre>
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing.

     

     Parameters are same as the constructor VideoCapture(const String&amp; filename, int apiPreference = CAP_ANY)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the file has been successfully opened

     The method first calls VideoCapture::release to close the already opened file or camera.</dd>
</dl>
</li>
</ul>
<a name="open-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open(java.lang.String&nbsp;filename,
                    int&nbsp;apiPreference)</pre>
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing.

     

     Parameters are same as the constructor VideoCapture(const String&amp; filename, int apiPreference = CAP_ANY)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - automatically generated</dd>
<dd><code>apiPreference</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the file has been successfully opened

     The method first calls VideoCapture::release to close the already opened file or camera.</dd>
</dl>
</li>
</ul>
<a name="open-java.lang.String-int-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open(java.lang.String&nbsp;filename,
                    int&nbsp;apiPreference,
                    <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</pre>
<div class="block">Opens a video file or a capturing device or an IP video stream for video capturing with API Preference and parameters

     

     The <code>params</code> parameter allows to specify extra parameters encoded as pairs <code>(paramId_1, paramValue_1, paramId_2, paramValue_2, ...)</code>.
     See cv::VideoCaptureProperties</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - automatically generated</dd>
<dd><code>apiPreference</code> - automatically generated</dd>
<dd><code>params</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the file has been successfully opened

     The method first calls VideoCapture::release to close the already opened file or camera.</dd>
</dl>
</li>
</ul>
<a name="read-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre>public&nbsp;boolean&nbsp;read(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</pre>
<div class="block">Grabs, decodes and returns the next video frame.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>false</code> if no frames has been grabbed

     The method/function combines VideoCapture::grab() and VideoCapture::retrieve() in one call. This is the
     most convenient method for reading video files or capturing data from decode and returns the just
     grabbed frame. If no frames has been grabbed (camera has been disconnected, or there are no more
     frames in video file), the method returns false and the function returns empty image (with %cv::Mat, test it with Mat::empty()).

     <b>Note:</b> In REF: videoio_c "C API", functions cvRetrieveFrame() and cv.RetrieveFrame() return image stored inside the video
     capturing structure. It is not allowed to modify or release the image! You can copy the frame using
     cvCloneImage and then do whatever you want with the copy.</dd>
</dl>
</li>
</ul>
<a name="release--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>release</h4>
<pre>public&nbsp;void&nbsp;release()</pre>
<div class="block">Closes video file or capturing device.

     The method is automatically called by subsequent VideoCapture::open and by VideoCapture
     destructor.

     The C function also deallocates memory and clears \*capture pointer.</div>
</li>
</ul>
<a name="retrieve-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>retrieve</h4>
<pre>public&nbsp;boolean&nbsp;retrieve(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</pre>
<div class="block">Decodes and returns the grabbed video frame.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>false</code> if no frames has been grabbed

     The method decodes and returns the just grabbed frame. If no frames has been grabbed
     (camera has been disconnected, or there are no more frames in video file), the method returns false
     and the function returns an empty image (with %cv::Mat, test it with Mat::empty()).

     SEE: read()

     <b>Note:</b> In REF: videoio_c "C API", functions cvRetrieveFrame() and cv.RetrieveFrame() return image stored inside the video
     capturing structure. It is not allowed to modify or release the image! You can copy the frame using
     cvCloneImage and then do whatever you want with the copy.</dd>
</dl>
</li>
</ul>
<a name="retrieve-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>retrieve</h4>
<pre>public&nbsp;boolean&nbsp;retrieve(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                        int&nbsp;flag)</pre>
<div class="block">Decodes and returns the grabbed video frame.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - it could be a frame index or a driver specific flag</dd>
<dd><code>image</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>false</code> if no frames has been grabbed

     The method decodes and returns the just grabbed frame. If no frames has been grabbed
     (camera has been disconnected, or there are no more frames in video file), the method returns false
     and the function returns an empty image (with %cv::Mat, test it with Mat::empty()).

     SEE: read()

     <b>Note:</b> In REF: videoio_c "C API", functions cvRetrieveFrame() and cv.RetrieveFrame() return image stored inside the video
     capturing structure. It is not allowed to modify or release the image! You can copy the frame using
     cvCloneImage and then do whatever you want with the copy.</dd>
</dl>
</li>
</ul>
<a name="set-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set</h4>
<pre>public&nbsp;boolean&nbsp;set(int&nbsp;propId,
                   double&nbsp;value)</pre>
<div class="block">Sets a property in the VideoCapture.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>propId</code> - Property identifier from cv::VideoCaptureProperties (eg. cv::CAP_PROP_POS_MSEC, cv::CAP_PROP_POS_FRAMES, ...)
     or one from REF: videoio_flags_others</dd>
<dd><code>value</code> - Value of the property.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the property is supported by backend used by the VideoCapture instance.
     <b>Note:</b> Even if it returns <code>true</code> this doesn't ensure that the property
     value has been accepted by the capture device. See note in VideoCapture::get()</dd>
</dl>
</li>
</ul>
<a name="setExceptionMode-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setExceptionMode</h4>
<pre>public&nbsp;void&nbsp;setExceptionMode(boolean&nbsp;enable)</pre>
<div class="block">Switches exceptions mode

 methods raise exceptions if not successful instead of returning an error code</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>enable</code> - automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/opencv/videoio/Videoio.html" title="class in org.opencv.videoio"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/videoio/VideoCapture.html" target="_top">Frames</a></li>
<li><a href="VideoCapture.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
