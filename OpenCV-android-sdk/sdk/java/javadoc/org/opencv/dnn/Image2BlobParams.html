<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:22 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Image2BlobParams (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Image2BlobParams (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/dnn/Dnn.html" title="class in org.opencv.dnn"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/dnn/KeypointsModel.html" title="class in org.opencv.dnn"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/dnn/Image2BlobParams.html" target="_top">Frames</a></li>
<li><a href="Image2BlobParams.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.dnn</div>
<h2 title="Class Image2BlobParams" class="title">Class Image2BlobParams</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.dnn.Image2BlobParams</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Image2BlobParams</span>
extends java.lang.Object</pre>
<div class="block">Processing params of image to blob.

 It includes all possible image processing operations and corresponding parameters.

 SEE: blobFromImageWithParams

 <b>Note:</b>
 The order and usage of <code>scalefactor</code> and <code>mean</code> are (input - mean) * scalefactor.
 The order and usage of <code>scalefactor</code>, <code>size</code>, <code>mean</code>, <code>swapRB</code>, and <code>ddepth</code> are consistent
 with the function of REF: blobFromImage.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Image2BlobParams.html#Image2BlobParams--">Image2BlobParams</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Image2BlobParams.html#Image2BlobParams-org.opencv.core.Scalar-">Image2BlobParams</a></span>(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Image2BlobParams.html#Image2BlobParams-org.opencv.core.Scalar-org.opencv.core.Size-">Image2BlobParams</a></span>(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor,
                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Image2BlobParams.html#Image2BlobParams-org.opencv.core.Scalar-org.opencv.core.Size-org.opencv.core.Scalar-">Image2BlobParams</a></span>(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor,
                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Image2BlobParams.html#Image2BlobParams-org.opencv.core.Scalar-org.opencv.core.Size-org.opencv.core.Scalar-boolean-">Image2BlobParams</a></span>(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor,
                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
                boolean&nbsp;swapRB)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Image2BlobParams.html#Image2BlobParams-org.opencv.core.Scalar-org.opencv.core.Size-org.opencv.core.Scalar-boolean-int-">Image2BlobParams</a></span>(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor,
                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
                boolean&nbsp;swapRB,
                int&nbsp;ddepth)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Image2BlobParams.html" title="class in org.opencv.dnn">Image2BlobParams</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Image2BlobParams.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Image2BlobParams.html#get_ddepth--">get_ddepth</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Image2BlobParams.html#get_mean--">get_mean</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Image2BlobParams.html#get_scalefactor--">get_scalefactor</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Image2BlobParams.html#get_size--">get_size</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Image2BlobParams.html#get_swapRB--">get_swapRB</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Image2BlobParams.html#getNativeObjAddr--">getNativeObjAddr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Image2BlobParams.html#set_ddepth-int-">set_ddepth</a></span>(int&nbsp;ddepth)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Image2BlobParams.html#set_mean-org.opencv.core.Scalar-">set_mean</a></span>(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Image2BlobParams.html#set_scalefactor-org.opencv.core.Scalar-">set_scalefactor</a></span>(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Image2BlobParams.html#set_size-org.opencv.core.Size-">set_size</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Image2BlobParams.html#set_swapRB-boolean-">set_swapRB</a></span>(boolean&nbsp;swapRB)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Image2BlobParams--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Image2BlobParams</h4>
<pre>public&nbsp;Image2BlobParams()</pre>
</li>
</ul>
<a name="Image2BlobParams-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Image2BlobParams</h4>
<pre>public&nbsp;Image2BlobParams(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor)</pre>
</li>
</ul>
<a name="Image2BlobParams-org.opencv.core.Scalar-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Image2BlobParams</h4>
<pre>public&nbsp;Image2BlobParams(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor,
                        <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</pre>
</li>
</ul>
<a name="Image2BlobParams-org.opencv.core.Scalar-org.opencv.core.Size-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Image2BlobParams</h4>
<pre>public&nbsp;Image2BlobParams(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor,
                        <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                        <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</pre>
</li>
</ul>
<a name="Image2BlobParams-org.opencv.core.Scalar-org.opencv.core.Size-org.opencv.core.Scalar-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Image2BlobParams</h4>
<pre>public&nbsp;Image2BlobParams(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor,
                        <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                        <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
                        boolean&nbsp;swapRB)</pre>
</li>
</ul>
<a name="Image2BlobParams-org.opencv.core.Scalar-org.opencv.core.Size-org.opencv.core.Scalar-boolean-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Image2BlobParams</h4>
<pre>public&nbsp;Image2BlobParams(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor,
                        <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                        <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
                        boolean&nbsp;swapRB,
                        int&nbsp;ddepth)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Image2BlobParams.html" title="class in org.opencv.dnn">Image2BlobParams</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="get_ddepth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_ddepth</h4>
<pre>public&nbsp;int&nbsp;get_ddepth()</pre>
</li>
</ul>
<a name="get_mean--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_mean</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;get_mean()</pre>
</li>
</ul>
<a name="get_scalefactor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_scalefactor</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;get_scalefactor()</pre>
</li>
</ul>
<a name="get_size--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_size</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;get_size()</pre>
</li>
</ul>
<a name="get_swapRB--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_swapRB</h4>
<pre>public&nbsp;boolean&nbsp;get_swapRB()</pre>
</li>
</ul>
<a name="getNativeObjAddr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNativeObjAddr</h4>
<pre>public&nbsp;long&nbsp;getNativeObjAddr()</pre>
</li>
</ul>
<a name="set_ddepth-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_ddepth</h4>
<pre>public&nbsp;void&nbsp;set_ddepth(int&nbsp;ddepth)</pre>
</li>
</ul>
<a name="set_mean-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_mean</h4>
<pre>public&nbsp;void&nbsp;set_mean(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</pre>
</li>
</ul>
<a name="set_scalefactor-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_scalefactor</h4>
<pre>public&nbsp;void&nbsp;set_scalefactor(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scalefactor)</pre>
</li>
</ul>
<a name="set_size-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_size</h4>
<pre>public&nbsp;void&nbsp;set_size(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</pre>
</li>
</ul>
<a name="set_swapRB-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>set_swapRB</h4>
<pre>public&nbsp;void&nbsp;set_swapRB(boolean&nbsp;swapRB)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/dnn/Dnn.html" title="class in org.opencv.dnn"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/dnn/KeypointsModel.html" title="class in org.opencv.dnn"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/dnn/Image2BlobParams.html" target="_top">Frames</a></li>
<li><a href="Image2BlobParams.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
