<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:22 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Net (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Net (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":9,"i37":9,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/dnn/SegmentationModel.html" title="class in org.opencv.dnn"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/dnn/Net.html" target="_top">Frames</a></li>
<li><a href="Net.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.dnn</div>
<h2 title="Class Net" class="title">Class Net</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.dnn.Net</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Net</span>
extends java.lang.Object</pre>
<div class="block">This class allows to create and manipulate comprehensive artificial neural networks.

 Neural network is presented as directed acyclic graph (DAG), where vertices are Layer instances,
 and edges specify relationships between layers inputs and outputs.

 Each network layer has unique integer id and unique string name inside its network.
 LayerId can store either layer name or layer id.

 This class supports reference counting of its instances, i. e. copies point to the same instance.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#Net--">Net</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#connect-java.lang.String-java.lang.String-">connect</a></span>(java.lang.String&nbsp;outPin,
       java.lang.String&nbsp;inpPin)</code>
<div class="block">Connects output of the first layer to input of the second layer.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#dump--">dump</a></span>()</code>
<div class="block">Dump net to String</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#dumpToFile-java.lang.String-">dumpToFile</a></span>(java.lang.String&nbsp;path)</code>
<div class="block">Dump net structure, hyperparameters, backend, target and fusion to dot file</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#empty--">empty</a></span>()</code>
<div class="block">Returns true if there are no layers in the network.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#enableFusion-boolean-">enableFusion</a></span>(boolean&nbsp;fusion)</code>
<div class="block">Enables or disables layer fusion in the network.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#enableWinograd-boolean-">enableWinograd</a></span>(boolean&nbsp;useWinograd)</code>
<div class="block">Enables or disables the Winograd compute branch.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#forward--">forward</a></span>()</code>
<div class="block">Runs forward pass to compute output of layer with name <code>outputName</code>.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#forward-java.util.List-">forward</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;outputBlobs)</code>
<div class="block">Runs forward pass to compute output of layer with name <code>outputName</code>.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#forward-java.util.List-java.util.List-">forward</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;outputBlobs,
       java.util.List&lt;java.lang.String&gt;&nbsp;outBlobNames)</code>
<div class="block">Runs forward pass to compute outputs of layers listed in <code>outBlobNames</code>.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#forward-java.util.List-java.lang.String-">forward</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;outputBlobs,
       java.lang.String&nbsp;outputName)</code>
<div class="block">Runs forward pass to compute output of layer with name <code>outputName</code>.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#forward-java.lang.String-">forward</a></span>(java.lang.String&nbsp;outputName)</code>
<div class="block">Runs forward pass to compute output of layer with name <code>outputName</code>.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getFLOPS-int-java.util.List-">getFLOPS</a></span>(int&nbsp;layerId,
        java.util.List&lt;<a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&gt;&nbsp;netInputShapes)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getFLOPS-int-org.opencv.core.MatOfInt-">getFLOPS</a></span>(int&nbsp;layerId,
        <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;netInputShape)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getFLOPS-java.util.List-">getFLOPS</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&gt;&nbsp;netInputShapes)</code>
<div class="block">Computes FLOP for whole loaded model with specified input shapes.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getFLOPS-org.opencv.core.MatOfInt-">getFLOPS</a></span>(<a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;netInputShape)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getInputDetails-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-">getInputDetails</a></span>(<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scales,
               <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;zeropoints)</code>
<div class="block">Returns input scale and zeropoint for a quantized Net.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/dnn/Layer.html" title="class in org.opencv.dnn">Layer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getLayer-int-">getLayer</a></span>(int&nbsp;layerId)</code>
<div class="block">Returns pointer to layer with specified id or name which the network use.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getLayerId-java.lang.String-">getLayerId</a></span>(java.lang.String&nbsp;layer)</code>
<div class="block">Converts string name of the layer to the integer identifier.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getLayerNames--">getLayerNames</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getLayersCount-java.lang.String-">getLayersCount</a></span>(java.lang.String&nbsp;layerType)</code>
<div class="block">Returns count of layers of specified type.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getLayerTypes-java.util.List-">getLayerTypes</a></span>(java.util.List&lt;java.lang.String&gt;&nbsp;layersTypes)</code>
<div class="block">Returns list of types for layer used in model.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getMemoryConsumption-int-java.util.List-long:A-long:A-">getMemoryConsumption</a></span>(int&nbsp;layerId,
                    java.util.List&lt;<a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&gt;&nbsp;netInputShapes,
                    long[]&nbsp;weights,
                    long[]&nbsp;blobs)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getMemoryConsumption-int-org.opencv.core.MatOfInt-long:A-long:A-">getMemoryConsumption</a></span>(int&nbsp;layerId,
                    <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;netInputShape,
                    long[]&nbsp;weights,
                    long[]&nbsp;blobs)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getMemoryConsumption-org.opencv.core.MatOfInt-long:A-long:A-">getMemoryConsumption</a></span>(<a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;netInputShape,
                    long[]&nbsp;weights,
                    long[]&nbsp;blobs)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getNativeObjAddr--">getNativeObjAddr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getOutputDetails-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-">getOutputDetails</a></span>(<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scales,
                <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;zeropoints)</code>
<div class="block">Returns output scale and zeropoint for a quantized Net.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getParam-int-">getParam</a></span>(int&nbsp;layer)</code>
<div class="block">Returns parameter blob of the layer.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getParam-int-int-">getParam</a></span>(int&nbsp;layer,
        int&nbsp;numParam)</code>
<div class="block">Returns parameter blob of the layer.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getParam-java.lang.String-">getParam</a></span>(java.lang.String&nbsp;layerName)</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getParam-java.lang.String-int-">getParam</a></span>(java.lang.String&nbsp;layerName,
        int&nbsp;numParam)</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getPerfProfile-org.opencv.core.MatOfDouble-">getPerfProfile</a></span>(<a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;timings)</code>
<div class="block">Returns overall time for inference and timings (in ticks) for layers.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getUnconnectedOutLayers--">getUnconnectedOutLayers</a></span>()</code>
<div class="block">Returns indexes of layers with unconnected outputs.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#getUnconnectedOutLayersNames--">getUnconnectedOutLayersNames</a></span>()</code>
<div class="block">Returns names of layers with unconnected outputs.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#quantize-java.util.List-int-int-">quantize</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;calibData,
        int&nbsp;inputsDtype,
        int&nbsp;outputsDtype)</code>
<div class="block">Returns a quantized Net from a floating-point Net.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#quantize-java.util.List-int-int-boolean-">quantize</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;calibData,
        int&nbsp;inputsDtype,
        int&nbsp;outputsDtype,
        boolean&nbsp;perChannel)</code>
<div class="block">Returns a quantized Net from a floating-point Net.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#readFromModelOptimizer-org.opencv.core.MatOfByte-org.opencv.core.MatOfByte-">readFromModelOptimizer</a></span>(<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModelConfig,
                      <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferWeights)</code>
<div class="block">Create a network from Intel's Model Optimizer in-memory buffers with intermediate representation (IR).</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#readFromModelOptimizer-java.lang.String-java.lang.String-">readFromModelOptimizer</a></span>(java.lang.String&nbsp;xml,
                      java.lang.String&nbsp;bin)</code>
<div class="block">Create a network from Intel's Model Optimizer intermediate representation (IR).</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#setHalideScheduler-java.lang.String-">setHalideScheduler</a></span>(java.lang.String&nbsp;scheduler)</code>
<div class="block">Compile Halide layers.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#setInput-org.opencv.core.Mat-">setInput</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob)</code>
<div class="block">Sets the new input value for the network</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#setInput-org.opencv.core.Mat-java.lang.String-">setInput</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob,
        java.lang.String&nbsp;name)</code>
<div class="block">Sets the new input value for the network</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#setInput-org.opencv.core.Mat-java.lang.String-double-">setInput</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob,
        java.lang.String&nbsp;name,
        double&nbsp;scalefactor)</code>
<div class="block">Sets the new input value for the network</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#setInput-org.opencv.core.Mat-java.lang.String-double-org.opencv.core.Scalar-">setInput</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob,
        java.lang.String&nbsp;name,
        double&nbsp;scalefactor,
        <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</code>
<div class="block">Sets the new input value for the network</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#setInputShape-java.lang.String-org.opencv.core.MatOfInt-">setInputShape</a></span>(java.lang.String&nbsp;inputName,
             <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;shape)</code>
<div class="block">Specify shape of network input.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#setInputsNames-java.util.List-">setInputsNames</a></span>(java.util.List&lt;java.lang.String&gt;&nbsp;inputBlobNames)</code>
<div class="block">Sets outputs names of the network input pseudo layer.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#setParam-int-int-org.opencv.core.Mat-">setParam</a></span>(int&nbsp;layer,
        int&nbsp;numParam,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob)</code>
<div class="block">Sets the new value for the learned param of the layer.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#setParam-java.lang.String-int-org.opencv.core.Mat-">setParam</a></span>(java.lang.String&nbsp;layerName,
        int&nbsp;numParam,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob)</code>&nbsp;</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#setPreferableBackend-int-">setPreferableBackend</a></span>(int&nbsp;backendId)</code>
<div class="block">Ask network to use specific computation backend where it supported.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Net.html#setPreferableTarget-int-">setPreferableTarget</a></span>(int&nbsp;targetId)</code>
<div class="block">Ask network to make computations on specific target device.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Net--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Net</h4>
<pre>public&nbsp;Net()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="connect-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>connect</h4>
<pre>public&nbsp;void&nbsp;connect(java.lang.String&nbsp;outPin,
                    java.lang.String&nbsp;inpPin)</pre>
<div class="block">Connects output of the first layer to input of the second layer.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>outPin</code> - descriptor of the first layer output.</dd>
<dd><code>inpPin</code> - descriptor of the second layer input.

 Descriptors have the following template &lt;DFN&gt;&amp;lt;layer_name&amp;gt;[.input_number]&lt;/DFN&gt;:
 - the first part of the template &lt;DFN&gt;layer_name&lt;/DFN&gt; is string name of the added layer.
 If this part is empty then the network input pseudo layer will be used;
 - the second optional part of the template &lt;DFN&gt;input_number&lt;/DFN&gt;
 is either number of the layer input, either label one.
 If this part is omitted then the first layer input will be used.

 SEE: setNetInputs(), Layer::inputNameToIndex(), Layer::outputNameToIndex()</dd>
</dl>
</li>
</ul>
<a name="dump--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dump</h4>
<pre>public&nbsp;java.lang.String&nbsp;dump()</pre>
<div class="block">Dump net to String</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>String with structure, hyperparameters, backend, target and fusion
 Call method after setInput(). To see correct backend, target and fusion run after forward().</dd>
</dl>
</li>
</ul>
<a name="dumpToFile-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dumpToFile</h4>
<pre>public&nbsp;void&nbsp;dumpToFile(java.lang.String&nbsp;path)</pre>
<div class="block">Dump net structure, hyperparameters, backend, target and fusion to dot file</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - path to output file with .dot extension
 SEE: dump()</dd>
</dl>
</li>
</ul>
<a name="empty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>empty</h4>
<pre>public&nbsp;boolean&nbsp;empty()</pre>
<div class="block">Returns true if there are no layers in the network.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="enableFusion-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableFusion</h4>
<pre>public&nbsp;void&nbsp;enableFusion(boolean&nbsp;fusion)</pre>
<div class="block">Enables or disables layer fusion in the network.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fusion</code> - true to enable the fusion, false to disable. The fusion is enabled by default.</dd>
</dl>
</li>
</ul>
<a name="enableWinograd-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableWinograd</h4>
<pre>public&nbsp;void&nbsp;enableWinograd(boolean&nbsp;useWinograd)</pre>
<div class="block">Enables or disables the Winograd compute branch. The Winograd compute branch can speed up
 3x3 Convolution at a small loss of accuracy.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>useWinograd</code> - true to enable the Winograd compute branch. The default is true.</dd>
</dl>
</li>
</ul>
<a name="forward--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>forward</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;forward()</pre>
<div class="block">Runs forward pass to compute output of layer with name <code>outputName</code>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>blob for first output of specified layer.
 By default runs forward pass for the whole network.</dd>
</dl>
</li>
</ul>
<a name="forward-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>forward</h4>
<pre>public&nbsp;void&nbsp;forward(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;outputBlobs)</pre>
<div class="block">Runs forward pass to compute output of layer with name <code>outputName</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>outputBlobs</code> - contains all output blobs for specified layer.
 If <code>outputName</code> is empty, runs forward pass for the whole network.</dd>
</dl>
</li>
</ul>
<a name="forward-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>forward</h4>
<pre>public&nbsp;void&nbsp;forward(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;outputBlobs,
                    java.util.List&lt;java.lang.String&gt;&nbsp;outBlobNames)</pre>
<div class="block">Runs forward pass to compute outputs of layers listed in <code>outBlobNames</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>outputBlobs</code> - contains blobs for first outputs of specified layers.</dd>
<dd><code>outBlobNames</code> - names for layers which outputs are needed to get</dd>
</dl>
</li>
</ul>
<a name="forward-java.util.List-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>forward</h4>
<pre>public&nbsp;void&nbsp;forward(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;outputBlobs,
                    java.lang.String&nbsp;outputName)</pre>
<div class="block">Runs forward pass to compute output of layer with name <code>outputName</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>outputBlobs</code> - contains all output blobs for specified layer.</dd>
<dd><code>outputName</code> - name for layer which output is needed to get
 If <code>outputName</code> is empty, runs forward pass for the whole network.</dd>
</dl>
</li>
</ul>
<a name="forward-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>forward</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;forward(java.lang.String&nbsp;outputName)</pre>
<div class="block">Runs forward pass to compute output of layer with name <code>outputName</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>outputName</code> - name for layer which output is needed to get</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>blob for first output of specified layer.
 By default runs forward pass for the whole network.</dd>
</dl>
</li>
</ul>
<a name="getFLOPS-int-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFLOPS</h4>
<pre>public&nbsp;long&nbsp;getFLOPS(int&nbsp;layerId,
                     java.util.List&lt;<a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&gt;&nbsp;netInputShapes)</pre>
</li>
</ul>
<a name="getFLOPS-int-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFLOPS</h4>
<pre>public&nbsp;long&nbsp;getFLOPS(int&nbsp;layerId,
                     <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;netInputShape)</pre>
</li>
</ul>
<a name="getFLOPS-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFLOPS</h4>
<pre>public&nbsp;long&nbsp;getFLOPS(java.util.List&lt;<a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&gt;&nbsp;netInputShapes)</pre>
<div class="block">Computes FLOP for whole loaded model with specified input shapes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>netInputShapes</code> - vector of shapes for all net inputs.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>computed FLOP.</dd>
</dl>
</li>
</ul>
<a name="getFLOPS-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFLOPS</h4>
<pre>public&nbsp;long&nbsp;getFLOPS(<a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;netInputShape)</pre>
</li>
</ul>
<a name="getInputDetails-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInputDetails</h4>
<pre>public&nbsp;void&nbsp;getInputDetails(<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scales,
                            <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;zeropoints)</pre>
<div class="block">Returns input scale and zeropoint for a quantized Net.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>scales</code> - output parameter for returning input scales.</dd>
<dd><code>zeropoints</code> - output parameter for returning input zeropoints.</dd>
</dl>
</li>
</ul>
<a name="getLayer-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLayer</h4>
<pre>public&nbsp;<a href="../../../org/opencv/dnn/Layer.html" title="class in org.opencv.dnn">Layer</a>&nbsp;getLayer(int&nbsp;layerId)</pre>
<div class="block">Returns pointer to layer with specified id or name which the network use.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>layerId</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getLayerId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLayerId</h4>
<pre>public&nbsp;int&nbsp;getLayerId(java.lang.String&nbsp;layer)</pre>
<div class="block">Converts string name of the layer to the integer identifier.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>layer</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>id of the layer, or -1 if the layer wasn't found.</dd>
</dl>
</li>
</ul>
<a name="getLayerNames--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLayerNames</h4>
<pre>public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getLayerNames()</pre>
</li>
</ul>
<a name="getLayersCount-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLayersCount</h4>
<pre>public&nbsp;int&nbsp;getLayersCount(java.lang.String&nbsp;layerType)</pre>
<div class="block">Returns count of layers of specified type.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>layerType</code> - type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>count of layers</dd>
</dl>
</li>
</ul>
<a name="getLayerTypes-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLayerTypes</h4>
<pre>public&nbsp;void&nbsp;getLayerTypes(java.util.List&lt;java.lang.String&gt;&nbsp;layersTypes)</pre>
<div class="block">Returns list of types for layer used in model.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>layersTypes</code> - output parameter for returning types.</dd>
</dl>
</li>
</ul>
<a name="getMemoryConsumption-int-java.util.List-long:A-long:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMemoryConsumption</h4>
<pre>public&nbsp;void&nbsp;getMemoryConsumption(int&nbsp;layerId,
                                 java.util.List&lt;<a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&gt;&nbsp;netInputShapes,
                                 long[]&nbsp;weights,
                                 long[]&nbsp;blobs)</pre>
</li>
</ul>
<a name="getMemoryConsumption-int-org.opencv.core.MatOfInt-long:A-long:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMemoryConsumption</h4>
<pre>public&nbsp;void&nbsp;getMemoryConsumption(int&nbsp;layerId,
                                 <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;netInputShape,
                                 long[]&nbsp;weights,
                                 long[]&nbsp;blobs)</pre>
</li>
</ul>
<a name="getMemoryConsumption-org.opencv.core.MatOfInt-long:A-long:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMemoryConsumption</h4>
<pre>public&nbsp;void&nbsp;getMemoryConsumption(<a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;netInputShape,
                                 long[]&nbsp;weights,
                                 long[]&nbsp;blobs)</pre>
</li>
</ul>
<a name="getNativeObjAddr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNativeObjAddr</h4>
<pre>public&nbsp;long&nbsp;getNativeObjAddr()</pre>
</li>
</ul>
<a name="getOutputDetails-org.opencv.core.MatOfFloat-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutputDetails</h4>
<pre>public&nbsp;void&nbsp;getOutputDetails(<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;scales,
                             <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;zeropoints)</pre>
<div class="block">Returns output scale and zeropoint for a quantized Net.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>scales</code> - output parameter for returning output scales.</dd>
<dd><code>zeropoints</code> - output parameter for returning output zeropoints.</dd>
</dl>
</li>
</ul>
<a name="getParam-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParam</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getParam(int&nbsp;layer)</pre>
<div class="block">Returns parameter blob of the layer.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>layer</code> - name or id of the layer.
 SEE: Layer::blobs</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getParam-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParam</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getParam(int&nbsp;layer,
                    int&nbsp;numParam)</pre>
<div class="block">Returns parameter blob of the layer.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>layer</code> - name or id of the layer.</dd>
<dd><code>numParam</code> - index of the layer parameter in the Layer::blobs array.
 SEE: Layer::blobs</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getParam-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParam</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getParam(java.lang.String&nbsp;layerName)</pre>
</li>
</ul>
<a name="getParam-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParam</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getParam(java.lang.String&nbsp;layerName,
                    int&nbsp;numParam)</pre>
</li>
</ul>
<a name="getPerfProfile-org.opencv.core.MatOfDouble-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPerfProfile</h4>
<pre>public&nbsp;long&nbsp;getPerfProfile(<a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;timings)</pre>
<div class="block">Returns overall time for inference and timings (in ticks) for layers.

 Indexes in returned vector correspond to layers ids. Some layers can be fused with others,
 in this case zero ticks count will be return for that skipped layers. Supported by DNN_BACKEND_OPENCV on DNN_TARGET_CPU only.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>timings</code> - vector for tick timings for all layers.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>overall ticks for model inference.</dd>
</dl>
</li>
</ul>
<a name="getUnconnectedOutLayers--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnconnectedOutLayers</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;getUnconnectedOutLayers()</pre>
<div class="block">Returns indexes of layers with unconnected outputs.

 FIXIT: Rework API to registerOutput() approach, deprecate this call</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getUnconnectedOutLayersNames--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnconnectedOutLayersNames</h4>
<pre>public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getUnconnectedOutLayersNames()</pre>
<div class="block">Returns names of layers with unconnected outputs.

 FIXIT: Rework API to registerOutput() approach, deprecate this call</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="quantize-java.util.List-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>quantize</h4>
<pre>public&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;quantize(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;calibData,
                    int&nbsp;inputsDtype,
                    int&nbsp;outputsDtype)</pre>
<div class="block">Returns a quantized Net from a floating-point Net.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>calibData</code> - Calibration data to compute the quantization parameters.</dd>
<dd><code>inputsDtype</code> - Datatype of quantized net's inputs. Can be CV_32F or CV_8S.</dd>
<dd><code>outputsDtype</code> - Datatype of quantized net's outputs. Can be CV_32F or CV_8S.
 in per-channel way (channel-wise). Set it false to quantize model in per-tensor way (or tensor-wise).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="quantize-java.util.List-int-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>quantize</h4>
<pre>public&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;quantize(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;calibData,
                    int&nbsp;inputsDtype,
                    int&nbsp;outputsDtype,
                    boolean&nbsp;perChannel)</pre>
<div class="block">Returns a quantized Net from a floating-point Net.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>calibData</code> - Calibration data to compute the quantization parameters.</dd>
<dd><code>inputsDtype</code> - Datatype of quantized net's inputs. Can be CV_32F or CV_8S.</dd>
<dd><code>outputsDtype</code> - Datatype of quantized net's outputs. Can be CV_32F or CV_8S.</dd>
<dd><code>perChannel</code> - Quantization granularity of quantized Net. The default is true, that means quantize model
 in per-channel way (channel-wise). Set it false to quantize model in per-tensor way (or tensor-wise).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="readFromModelOptimizer-org.opencv.core.MatOfByte-org.opencv.core.MatOfByte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readFromModelOptimizer</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readFromModelOptimizer(<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModelConfig,
                                         <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferWeights)</pre>
<div class="block">Create a network from Intel's Model Optimizer in-memory buffers with intermediate representation (IR).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bufferModelConfig</code> - buffer with model's configuration.</dd>
<dd><code>bufferWeights</code> - buffer with model's trained weights.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Net object.</dd>
</dl>
</li>
</ul>
<a name="readFromModelOptimizer-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readFromModelOptimizer</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;readFromModelOptimizer(java.lang.String&nbsp;xml,
                                         java.lang.String&nbsp;bin)</pre>
<div class="block">Create a network from Intel's Model Optimizer intermediate representation (IR).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>xml</code> - XML configuration file with network's topology.</dd>
<dd><code>bin</code> - Binary file with trained weights.
 Networks imported from Intel's Model Optimizer are launched in Intel's Inference Engine
 backend.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setHalideScheduler-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHalideScheduler</h4>
<pre>public&nbsp;void&nbsp;setHalideScheduler(java.lang.String&nbsp;scheduler)</pre>
<div class="block">Compile Halide layers.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>scheduler</code> - Path to YAML file with scheduling directives.
 SEE: setPreferableBackend

 Schedule layers that support Halide backend. Then compile them for
 specific target. For layers that not represented in scheduling file
 or if no manual scheduling used at all, automatic scheduling will be applied.</dd>
</dl>
</li>
</ul>
<a name="setInput-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInput</h4>
<pre>public&nbsp;void&nbsp;setInput(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob)</pre>
<div class="block">Sets the new input value for the network</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>blob</code> - A new blob. Should have CV_32F or CV_8U depth.
 SEE: connect(String, String) to know format of the descriptor.

 If scale or mean values are specified, a final input blob is computed
 as:
 \(input(n,c,h,w) = scalefactor \times (blob(n,c,h,w) - mean_c)\)</dd>
</dl>
</li>
</ul>
<a name="setInput-org.opencv.core.Mat-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInput</h4>
<pre>public&nbsp;void&nbsp;setInput(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob,
                     java.lang.String&nbsp;name)</pre>
<div class="block">Sets the new input value for the network</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>blob</code> - A new blob. Should have CV_32F or CV_8U depth.</dd>
<dd><code>name</code> - A name of input layer.
 SEE: connect(String, String) to know format of the descriptor.

 If scale or mean values are specified, a final input blob is computed
 as:
 \(input(n,c,h,w) = scalefactor \times (blob(n,c,h,w) - mean_c)\)</dd>
</dl>
</li>
</ul>
<a name="setInput-org.opencv.core.Mat-java.lang.String-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInput</h4>
<pre>public&nbsp;void&nbsp;setInput(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob,
                     java.lang.String&nbsp;name,
                     double&nbsp;scalefactor)</pre>
<div class="block">Sets the new input value for the network</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>blob</code> - A new blob. Should have CV_32F or CV_8U depth.</dd>
<dd><code>name</code> - A name of input layer.</dd>
<dd><code>scalefactor</code> - An optional normalization scale.
 SEE: connect(String, String) to know format of the descriptor.

 If scale or mean values are specified, a final input blob is computed
 as:
 \(input(n,c,h,w) = scalefactor \times (blob(n,c,h,w) - mean_c)\)</dd>
</dl>
</li>
</ul>
<a name="setInput-org.opencv.core.Mat-java.lang.String-double-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInput</h4>
<pre>public&nbsp;void&nbsp;setInput(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob,
                     java.lang.String&nbsp;name,
                     double&nbsp;scalefactor,
                     <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</pre>
<div class="block">Sets the new input value for the network</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>blob</code> - A new blob. Should have CV_32F or CV_8U depth.</dd>
<dd><code>name</code> - A name of input layer.</dd>
<dd><code>scalefactor</code> - An optional normalization scale.</dd>
<dd><code>mean</code> - An optional mean subtraction values.
 SEE: connect(String, String) to know format of the descriptor.

 If scale or mean values are specified, a final input blob is computed
 as:
 \(input(n,c,h,w) = scalefactor \times (blob(n,c,h,w) - mean_c)\)</dd>
</dl>
</li>
</ul>
<a name="setInputShape-java.lang.String-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInputShape</h4>
<pre>public&nbsp;void&nbsp;setInputShape(java.lang.String&nbsp;inputName,
                          <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;shape)</pre>
<div class="block">Specify shape of network input.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inputName</code> - automatically generated</dd>
<dd><code>shape</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setInputsNames-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInputsNames</h4>
<pre>public&nbsp;void&nbsp;setInputsNames(java.util.List&lt;java.lang.String&gt;&nbsp;inputBlobNames)</pre>
<div class="block">Sets outputs names of the network input pseudo layer.

 Each net always has special own the network input pseudo layer with id=0.
 This layer stores the user blobs only and don't make any computations.
 In fact, this layer provides the only way to pass user data into the network.
 As any other layer, this layer can label its outputs and this function provides an easy way to do this.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inputBlobNames</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setParam-int-int-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParam</h4>
<pre>public&nbsp;void&nbsp;setParam(int&nbsp;layer,
                     int&nbsp;numParam,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob)</pre>
<div class="block">Sets the new value for the learned param of the layer.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>layer</code> - name or id of the layer.</dd>
<dd><code>numParam</code> - index of the layer parameter in the Layer::blobs array.</dd>
<dd><code>blob</code> - the new value.
 SEE: Layer::blobs
 <b>Note:</b> If shape of the new blob differs from the previous shape,
 then the following forward pass may fail.</dd>
</dl>
</li>
</ul>
<a name="setParam-java.lang.String-int-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParam</h4>
<pre>public&nbsp;void&nbsp;setParam(java.lang.String&nbsp;layerName,
                     int&nbsp;numParam,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blob)</pre>
</li>
</ul>
<a name="setPreferableBackend-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPreferableBackend</h4>
<pre>public&nbsp;void&nbsp;setPreferableBackend(int&nbsp;backendId)</pre>
<div class="block">Ask network to use specific computation backend where it supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>backendId</code> - backend identifier.
 SEE: Backend

 If OpenCV is compiled with Intel's Inference Engine library, DNN_BACKEND_DEFAULT
 means DNN_BACKEND_INFERENCE_ENGINE. Otherwise it equals to DNN_BACKEND_OPENCV.</dd>
</dl>
</li>
</ul>
<a name="setPreferableTarget-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setPreferableTarget</h4>
<pre>public&nbsp;void&nbsp;setPreferableTarget(int&nbsp;targetId)</pre>
<div class="block">Ask network to make computations on specific target device.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>targetId</code> - target identifier.
 SEE: Target

 List of supported combinations backend / target:
 |                        | DNN_BACKEND_OPENCV | DNN_BACKEND_INFERENCE_ENGINE | DNN_BACKEND_HALIDE |  DNN_BACKEND_CUDA |
 |------------------------|--------------------|------------------------------|--------------------|-------------------|
 | DNN_TARGET_CPU         |                  + |                            + |                  + |                   |
 | DNN_TARGET_OPENCL      |                  + |                            + |                  + |                   |
 | DNN_TARGET_OPENCL_FP16 |                  + |                            + |                    |                   |
 | DNN_TARGET_MYRIAD      |                    |                            + |                    |                   |
 | DNN_TARGET_FPGA        |                    |                            + |                    |                   |
 | DNN_TARGET_CUDA        |                    |                              |                    |                 + |
 | DNN_TARGET_CUDA_FP16   |                    |                              |                    |                 + |
 | DNN_TARGET_HDDL        |                    |                            + |                    |                   |</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/dnn/SegmentationModel.html" title="class in org.opencv.dnn"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/dnn/Net.html" target="_top">Frames</a></li>
<li><a href="Net.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
