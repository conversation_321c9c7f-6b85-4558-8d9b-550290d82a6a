<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:22 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Model (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Model (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/dnn/Layer.html" title="class in org.opencv.dnn"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/dnn/Model.html" target="_top">Frames</a></li>
<li><a href="Model.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.dnn</div>
<h2 title="Class Model" class="title">Class Model</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.dnn.Model</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../org/opencv/dnn/ClassificationModel.html" title="class in org.opencv.dnn">ClassificationModel</a>, <a href="../../../org/opencv/dnn/DetectionModel.html" title="class in org.opencv.dnn">DetectionModel</a>, <a href="../../../org/opencv/dnn/KeypointsModel.html" title="class in org.opencv.dnn">KeypointsModel</a>, <a href="../../../org/opencv/dnn/SegmentationModel.html" title="class in org.opencv.dnn">SegmentationModel</a>, <a href="../../../org/opencv/dnn/TextDetectionModel.html" title="class in org.opencv.dnn">TextDetectionModel</a>, <a href="../../../org/opencv/dnn/TextRecognitionModel.html" title="class in org.opencv.dnn">TextRecognitionModel</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Model</span>
extends java.lang.Object</pre>
<div class="block">This class is presented high-level API for neural networks.

 Model allows to set params for preprocessing input image.
 Model creates net from file with trained weights and config,
 sets preprocessing input and runs forward pass.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#Model-org.opencv.dnn.Net-">Model</a></span>(<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;network)</code>
<div class="block">Create model from deep learning network.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#Model-java.lang.String-">Model</a></span>(java.lang.String&nbsp;model)</code>
<div class="block">Create model from deep learning network represented in one of the supported formats.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#Model-java.lang.String-java.lang.String-">Model</a></span>(java.lang.String&nbsp;model,
     java.lang.String&nbsp;config)</code>
<div class="block">Create model from deep learning network represented in one of the supported formats.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#getNativeObjAddr--">getNativeObjAddr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#predict-org.opencv.core.Mat-java.util.List-">predict</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;frame,
       java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;outs)</code>
<div class="block">Given the <code>input</code> frame, create input blob, run net and return the output <code>blobs</code>.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#setInputCrop-boolean-">setInputCrop</a></span>(boolean&nbsp;crop)</code>
<div class="block">Set flag crop for frame.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#setInputMean-org.opencv.core.Scalar-">setInputMean</a></span>(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</code>
<div class="block">Set mean value for frame.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#setInputParams--">setInputParams</a></span>()</code>
<div class="block">Set preprocessing parameters for frame.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#setInputParams-double-">setInputParams</a></span>(double&nbsp;scale)</code>
<div class="block">Set preprocessing parameters for frame.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#setInputParams-double-org.opencv.core.Size-">setInputParams</a></span>(double&nbsp;scale,
              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</code>
<div class="block">Set preprocessing parameters for frame.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#setInputParams-double-org.opencv.core.Size-org.opencv.core.Scalar-">setInputParams</a></span>(double&nbsp;scale,
              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
              <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</code>
<div class="block">Set preprocessing parameters for frame.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#setInputParams-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-">setInputParams</a></span>(double&nbsp;scale,
              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
              <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
              boolean&nbsp;swapRB)</code>
<div class="block">Set preprocessing parameters for frame.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#setInputParams-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-boolean-">setInputParams</a></span>(double&nbsp;scale,
              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
              <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
              boolean&nbsp;swapRB,
              boolean&nbsp;crop)</code>
<div class="block">Set preprocessing parameters for frame.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#setInputScale-org.opencv.core.Scalar-">setInputScale</a></span>(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scale)</code>
<div class="block">Set scalefactor value for frame.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#setInputSize-int-int-">setInputSize</a></span>(int&nbsp;width,
            int&nbsp;height)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#setInputSize-org.opencv.core.Size-">setInputSize</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</code>
<div class="block">Set input size for frame.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#setInputSwapRB-boolean-">setInputSwapRB</a></span>(boolean&nbsp;swapRB)</code>
<div class="block">Set flag swapRB for frame.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#setPreferableBackend-int-">setPreferableBackend</a></span>(int&nbsp;backendId)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/Model.html#setPreferableTarget-int-">setPreferableTarget</a></span>(int&nbsp;targetId)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Model-org.opencv.dnn.Net-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model</h4>
<pre>public&nbsp;Model(<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;network)</pre>
<div class="block">Create model from deep learning network.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>network</code> - Net object.</dd>
</dl>
</li>
</ul>
<a name="Model-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model</h4>
<pre>public&nbsp;Model(java.lang.String&nbsp;model)</pre>
<div class="block">Create model from deep learning network represented in one of the supported formats.
 An order of <code>model</code> and <code>config</code> arguments does not matter.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - Binary file contains trained weights.</dd>
</dl>
</li>
</ul>
<a name="Model-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Model</h4>
<pre>public&nbsp;Model(java.lang.String&nbsp;model,
             java.lang.String&nbsp;config)</pre>
<div class="block">Create model from deep learning network represented in one of the supported formats.
 An order of <code>model</code> and <code>config</code> arguments does not matter.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - Binary file contains trained weights.</dd>
<dd><code>config</code> - Text file contains network configuration.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="getNativeObjAddr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNativeObjAddr</h4>
<pre>public&nbsp;long&nbsp;getNativeObjAddr()</pre>
</li>
</ul>
<a name="predict-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>predict</h4>
<pre>public&nbsp;void&nbsp;predict(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;frame,
                    java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;outs)</pre>
<div class="block">Given the <code>input</code> frame, create input blob, run net and return the output <code>blobs</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>outs</code> - Allocated output blobs, which will store results of the computation.</dd>
<dd><code>frame</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setInputCrop-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInputCrop</h4>
<pre>public&nbsp;<a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a>&nbsp;setInputCrop(boolean&nbsp;crop)</pre>
<div class="block">Set flag crop for frame.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>crop</code> - Flag which indicates whether image will be cropped after resize or not.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setInputMean-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInputMean</h4>
<pre>public&nbsp;<a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a>&nbsp;setInputMean(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</pre>
<div class="block">Set mean value for frame.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mean</code> - Scalar with mean values which are subtracted from channels.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setInputParams--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInputParams</h4>
<pre>public&nbsp;void&nbsp;setInputParams()</pre>
<div class="block">Set preprocessing parameters for frame.
 blob(n, c, y, x) = scale * resize( frame(y, x, c) ) - mean(c) )</div>
</li>
</ul>
<a name="setInputParams-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInputParams</h4>
<pre>public&nbsp;void&nbsp;setInputParams(double&nbsp;scale)</pre>
<div class="block">Set preprocessing parameters for frame.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>scale</code> - Multiplier for frame values.
 blob(n, c, y, x) = scale * resize( frame(y, x, c) ) - mean(c) )</dd>
</dl>
</li>
</ul>
<a name="setInputParams-double-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInputParams</h4>
<pre>public&nbsp;void&nbsp;setInputParams(double&nbsp;scale,
                           <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</pre>
<div class="block">Set preprocessing parameters for frame.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>size</code> - New input size.</dd>
<dd><code>scale</code> - Multiplier for frame values.
 blob(n, c, y, x) = scale * resize( frame(y, x, c) ) - mean(c) )</dd>
</dl>
</li>
</ul>
<a name="setInputParams-double-org.opencv.core.Size-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInputParams</h4>
<pre>public&nbsp;void&nbsp;setInputParams(double&nbsp;scale,
                           <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean)</pre>
<div class="block">Set preprocessing parameters for frame.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>size</code> - New input size.</dd>
<dd><code>mean</code> - Scalar with mean values which are subtracted from channels.</dd>
<dd><code>scale</code> - Multiplier for frame values.
 blob(n, c, y, x) = scale * resize( frame(y, x, c) ) - mean(c) )</dd>
</dl>
</li>
</ul>
<a name="setInputParams-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInputParams</h4>
<pre>public&nbsp;void&nbsp;setInputParams(double&nbsp;scale,
                           <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
                           boolean&nbsp;swapRB)</pre>
<div class="block">Set preprocessing parameters for frame.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>size</code> - New input size.</dd>
<dd><code>mean</code> - Scalar with mean values which are subtracted from channels.</dd>
<dd><code>scale</code> - Multiplier for frame values.</dd>
<dd><code>swapRB</code> - Flag which indicates that swap first and last channels.
 blob(n, c, y, x) = scale * resize( frame(y, x, c) ) - mean(c) )</dd>
</dl>
</li>
</ul>
<a name="setInputParams-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInputParams</h4>
<pre>public&nbsp;void&nbsp;setInputParams(double&nbsp;scale,
                           <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean,
                           boolean&nbsp;swapRB,
                           boolean&nbsp;crop)</pre>
<div class="block">Set preprocessing parameters for frame.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>size</code> - New input size.</dd>
<dd><code>mean</code> - Scalar with mean values which are subtracted from channels.</dd>
<dd><code>scale</code> - Multiplier for frame values.</dd>
<dd><code>swapRB</code> - Flag which indicates that swap first and last channels.</dd>
<dd><code>crop</code> - Flag which indicates whether image will be cropped after resize or not.
 blob(n, c, y, x) = scale * resize( frame(y, x, c) ) - mean(c) )</dd>
</dl>
</li>
</ul>
<a name="setInputScale-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInputScale</h4>
<pre>public&nbsp;<a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a>&nbsp;setInputScale(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;scale)</pre>
<div class="block">Set scalefactor value for frame.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>scale</code> - Multiplier for frame values.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setInputSize-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInputSize</h4>
<pre>public&nbsp;<a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a>&nbsp;setInputSize(int&nbsp;width,
                          int&nbsp;height)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>width</code> - New input width.</dd>
<dd><code>height</code> - New input height.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setInputSize-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInputSize</h4>
<pre>public&nbsp;<a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a>&nbsp;setInputSize(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size)</pre>
<div class="block">Set input size for frame.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>size</code> - New input size.
 <b>Note:</b> If shape of the new blob less than 0, then frame size not change.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setInputSwapRB-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInputSwapRB</h4>
<pre>public&nbsp;<a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a>&nbsp;setInputSwapRB(boolean&nbsp;swapRB)</pre>
<div class="block">Set flag swapRB for frame.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>swapRB</code> - Flag which indicates that swap first and last channels.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setPreferableBackend-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPreferableBackend</h4>
<pre>public&nbsp;<a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a>&nbsp;setPreferableBackend(int&nbsp;backendId)</pre>
</li>
</ul>
<a name="setPreferableTarget-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setPreferableTarget</h4>
<pre>public&nbsp;<a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a>&nbsp;setPreferableTarget(int&nbsp;targetId)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/dnn/Layer.html" title="class in org.opencv.dnn"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/dnn/Model.html" target="_top">Frames</a></li>
<li><a href="Model.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
