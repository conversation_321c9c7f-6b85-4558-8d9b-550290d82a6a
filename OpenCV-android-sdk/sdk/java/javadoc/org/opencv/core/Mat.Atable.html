<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:24 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Mat.Atable (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Mat.Atable (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/core/Mat.Tuple2.html" title="class in org.opencv.core"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/core/Mat.Atable.html" target="_top">Frames</a></li>
<li><a href="Mat.Atable.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.core</div>
<h2 title="Interface Mat.Atable" class="title">Interface Mat.Atable&lt;T&gt;</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></dd>
</dl>
<hr>
<br>
<pre>public static interface <span class="typeNameLabel">Mat.Atable&lt;T&gt;</span></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.Atable.html" title="type parameter in Mat.Atable">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.Atable.html#getV--">getV</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.Tuple2.html" title="class in org.opencv.core">Mat.Tuple2</a>&lt;<a href="../../../org/opencv/core/Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.Atable.html#getV2c--">getV2c</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.Tuple3.html" title="class in org.opencv.core">Mat.Tuple3</a>&lt;<a href="../../../org/opencv/core/Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.Atable.html#getV3c--">getV3c</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.Tuple4.html" title="class in org.opencv.core">Mat.Tuple4</a>&lt;<a href="../../../org/opencv/core/Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.Atable.html#getV4c--">getV4c</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.Atable.html#setV-T-">setV</a></span>(<a href="../../../org/opencv/core/Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&nbsp;v)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.Atable.html#setV2c-org.opencv.core.Mat.Tuple2-">setV2c</a></span>(<a href="../../../org/opencv/core/Mat.Tuple2.html" title="class in org.opencv.core">Mat.Tuple2</a>&lt;<a href="../../../org/opencv/core/Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;&nbsp;v)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.Atable.html#setV3c-org.opencv.core.Mat.Tuple3-">setV3c</a></span>(<a href="../../../org/opencv/core/Mat.Tuple3.html" title="class in org.opencv.core">Mat.Tuple3</a>&lt;<a href="../../../org/opencv/core/Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;&nbsp;v)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.Atable.html#setV4c-org.opencv.core.Mat.Tuple4-">setV4c</a></span>(<a href="../../../org/opencv/core/Mat.Tuple4.html" title="class in org.opencv.core">Mat.Tuple4</a>&lt;<a href="../../../org/opencv/core/Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;&nbsp;v)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getV--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getV</h4>
<pre><a href="../../../org/opencv/core/Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&nbsp;getV()</pre>
</li>
</ul>
<a name="getV2c--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getV2c</h4>
<pre><a href="../../../org/opencv/core/Mat.Tuple2.html" title="class in org.opencv.core">Mat.Tuple2</a>&lt;<a href="../../../org/opencv/core/Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;&nbsp;getV2c()</pre>
</li>
</ul>
<a name="getV3c--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getV3c</h4>
<pre><a href="../../../org/opencv/core/Mat.Tuple3.html" title="class in org.opencv.core">Mat.Tuple3</a>&lt;<a href="../../../org/opencv/core/Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;&nbsp;getV3c()</pre>
</li>
</ul>
<a name="getV4c--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getV4c</h4>
<pre><a href="../../../org/opencv/core/Mat.Tuple4.html" title="class in org.opencv.core">Mat.Tuple4</a>&lt;<a href="../../../org/opencv/core/Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;&nbsp;getV4c()</pre>
</li>
</ul>
<a name="setV-java.lang.Object-">
<!--   -->
</a><a name="setV-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setV</h4>
<pre>void&nbsp;setV(<a href="../../../org/opencv/core/Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&nbsp;v)</pre>
</li>
</ul>
<a name="setV2c-org.opencv.core.Mat.Tuple2-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setV2c</h4>
<pre>void&nbsp;setV2c(<a href="../../../org/opencv/core/Mat.Tuple2.html" title="class in org.opencv.core">Mat.Tuple2</a>&lt;<a href="../../../org/opencv/core/Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;&nbsp;v)</pre>
</li>
</ul>
<a name="setV3c-org.opencv.core.Mat.Tuple3-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setV3c</h4>
<pre>void&nbsp;setV3c(<a href="../../../org/opencv/core/Mat.Tuple3.html" title="class in org.opencv.core">Mat.Tuple3</a>&lt;<a href="../../../org/opencv/core/Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;&nbsp;v)</pre>
</li>
</ul>
<a name="setV4c-org.opencv.core.Mat.Tuple4-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setV4c</h4>
<pre>void&nbsp;setV4c(<a href="../../../org/opencv/core/Mat.Tuple4.html" title="class in org.opencv.core">Mat.Tuple4</a>&lt;<a href="../../../org/opencv/core/Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;&nbsp;v)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/core/Mat.Tuple2.html" title="class in org.opencv.core"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/core/Mat.Atable.html" target="_top">Frames</a></li>
<li><a href="Mat.Atable.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
