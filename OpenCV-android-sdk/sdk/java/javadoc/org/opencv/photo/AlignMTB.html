<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:24 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AlignMTB (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AlignMTB (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/photo/AlignExposures.html" title="class in org.opencv.photo"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/photo/CalibrateCRF.html" title="class in org.opencv.photo"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/photo/AlignMTB.html" target="_top">Frames</a></li>
<li><a href="AlignMTB.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.photo</div>
<h2 title="Class AlignMTB" class="title">Class AlignMTB</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/photo/AlignExposures.html" title="class in org.opencv.photo">org.opencv.photo.AlignExposures</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.photo.AlignMTB</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">AlignMTB</span>
extends <a href="../../../org/opencv/photo/AlignExposures.html" title="class in org.opencv.photo">AlignExposures</a></pre>
<div class="block">This algorithm converts images to median threshold bitmaps (1 for pixels brighter than median
 luminance and 0 otherwise) and than aligns the resulting bitmaps using bit operations.

 It is invariant to exposure, so exposure values and camera response are not necessary.

 In this implementation new image regions are filled with zeros.

 For more information see CITE: GW03 .</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/AlignMTB.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/AlignMTB.html#calculateShift-org.opencv.core.Mat-org.opencv.core.Mat-">calculateShift</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img0,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1)</code>
<div class="block">Calculates shift between two images, i.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/AlignMTB.html#computeBitmaps-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">computeBitmaps</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tb,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eb)</code>
<div class="block">Computes median threshold and exclude bitmaps of given image.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/AlignMTB.html#getCut--">getCut</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/AlignMTB.html#getExcludeRange--">getExcludeRange</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/AlignMTB.html#getMaxBits--">getMaxBits</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/AlignMTB.html#process-java.util.List-java.util.List-">process</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;src,
       java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;dst)</code>
<div class="block">Short version of process, that doesn't take extra arguments.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/AlignMTB.html#process-java.util.List-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-">process</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;src,
       java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;dst,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;times,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;response)</code>
<div class="block">Aligns images</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/AlignMTB.html#setCut-boolean-">setCut</a></span>(boolean&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/AlignMTB.html#setExcludeRange-int-">setExcludeRange</a></span>(int&nbsp;exclude_range)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/AlignMTB.html#setMaxBits-int-">setMaxBits</a></span>(int&nbsp;max_bits)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/AlignMTB.html#shiftMat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Point-">shiftMat</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
        <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;shift)</code>
<div class="block">Helper function, that shift Mat filling new regions with zeros.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#empty--">empty</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="calculateShift-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateShift</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;calculateShift(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img0,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img1)</pre>
<div class="block">Calculates shift between two images, i. e. how to shift the second image to correspond it with the
     first.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img0</code> - first image</dd>
<dd><code>img1</code> - second image</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="computeBitmaps-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>computeBitmaps</h4>
<pre>public&nbsp;void&nbsp;computeBitmaps(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tb,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eb)</pre>
<div class="block">Computes median threshold and exclude bitmaps of given image.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - input image</dd>
<dd><code>tb</code> - median threshold bitmap</dd>
<dd><code>eb</code> - exclude bitmap</dd>
</dl>
</li>
</ul>
<a name="getCut--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCut</h4>
<pre>public&nbsp;boolean&nbsp;getCut()</pre>
</li>
</ul>
<a name="getExcludeRange--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExcludeRange</h4>
<pre>public&nbsp;int&nbsp;getExcludeRange()</pre>
</li>
</ul>
<a name="getMaxBits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxBits</h4>
<pre>public&nbsp;int&nbsp;getMaxBits()</pre>
</li>
</ul>
<a name="process-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>process</h4>
<pre>public&nbsp;void&nbsp;process(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;src,
                    java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;dst)</pre>
<div class="block">Short version of process, that doesn't take extra arguments.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - vector of input images</dd>
<dd><code>dst</code> - vector of aligned images</dd>
</dl>
</li>
</ul>
<a name="process-java.util.List-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>process</h4>
<pre>public&nbsp;void&nbsp;process(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;src,
                    java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;dst,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;times,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;response)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../org/opencv/photo/AlignExposures.html#process-java.util.List-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-">AlignExposures</a></code></span></div>
<div class="block">Aligns images</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/opencv/photo/AlignExposures.html#process-java.util.List-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-">process</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/opencv/photo/AlignExposures.html" title="class in org.opencv.photo">AlignExposures</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - vector of input images</dd>
<dd><code>dst</code> - vector of aligned images</dd>
<dd><code>times</code> - vector of exposure time values for each image</dd>
<dd><code>response</code> - 256x1 matrix with inverse camera response function for each pixel value, it should
     have the same number of channels as images.</dd>
</dl>
</li>
</ul>
<a name="setCut-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCut</h4>
<pre>public&nbsp;void&nbsp;setCut(boolean&nbsp;value)</pre>
</li>
</ul>
<a name="setExcludeRange-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExcludeRange</h4>
<pre>public&nbsp;void&nbsp;setExcludeRange(int&nbsp;exclude_range)</pre>
</li>
</ul>
<a name="setMaxBits-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxBits</h4>
<pre>public&nbsp;void&nbsp;setMaxBits(int&nbsp;max_bits)</pre>
</li>
</ul>
<a name="shiftMat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Point-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>shiftMat</h4>
<pre>public&nbsp;void&nbsp;shiftMat(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                     <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;shift)</pre>
<div class="block">Helper function, that shift Mat filling new regions with zeros.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - input image</dd>
<dd><code>dst</code> - result image</dd>
<dd><code>shift</code> - shift value</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/photo/AlignExposures.html" title="class in org.opencv.photo"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/photo/CalibrateCRF.html" title="class in org.opencv.photo"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/photo/AlignMTB.html" target="_top">Frames</a></li>
<li><a href="AlignMTB.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
