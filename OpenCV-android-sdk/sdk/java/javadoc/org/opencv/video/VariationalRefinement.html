<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:24 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>VariationalRefinement (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="VariationalRefinement (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":9,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/video/TrackerNano_Params.html" title="class in org.opencv.video"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/video/Video.html" title="class in org.opencv.video"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/video/VariationalRefinement.html" target="_top">Frames</a></li>
<li><a href="VariationalRefinement.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.video</div>
<h2 title="Class VariationalRefinement" class="title">Class VariationalRefinement</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/video/DenseOpticalFlow.html" title="class in org.opencv.video">org.opencv.video.DenseOpticalFlow</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.video.VariationalRefinement</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">VariationalRefinement</span>
extends <a href="../../../org/opencv/video/DenseOpticalFlow.html" title="class in org.opencv.video">DenseOpticalFlow</a></pre>
<div class="block">Variational optical flow refinement

 This class implements variational refinement of the input flow field, i.e.
 it uses input flow to initialize the minimization of the following functional:
 \(E(U) = \int_{\Omega} \delta \Psi(E_I) + \gamma \Psi(E_G) + \alpha \Psi(E_S) \),
 where \(E_I,E_G,E_S\) are color constancy, gradient constancy and smoothness terms
 respectively. \(\Psi(s^2)=\sqrt{s^2+\epsilon^2}\) is a robust penalizer to limit the
 influence of outliers. A complete formulation and a description of the minimization
 procedure can be found in CITE: Brox2004</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/VariationalRefinement.html" title="class in org.opencv.video">VariationalRefinement</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/VariationalRefinement.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/VariationalRefinement.html#calcUV-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">calcUV</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;I0,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;I1,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;flow_u,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;flow_v)</code>
<div class="block">REF: calc function overload to handle separate horizontal (u) and vertical (v) flow components
 (to avoid extra splits/merges)</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/VariationalRefinement.html" title="class in org.opencv.video">VariationalRefinement</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/VariationalRefinement.html#create--">create</a></span>()</code>
<div class="block">Creates an instance of VariationalRefinement</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/VariationalRefinement.html#getAlpha--">getAlpha</a></span>()</code>
<div class="block">Weight of the smoothness term
 SEE: setAlpha</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/VariationalRefinement.html#getDelta--">getDelta</a></span>()</code>
<div class="block">Weight of the color constancy term
 SEE: setDelta</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/VariationalRefinement.html#getFixedPointIterations--">getFixedPointIterations</a></span>()</code>
<div class="block">Number of outer (fixed-point) iterations in the minimization procedure.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/VariationalRefinement.html#getGamma--">getGamma</a></span>()</code>
<div class="block">Weight of the gradient constancy term
 SEE: setGamma</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/VariationalRefinement.html#getOmega--">getOmega</a></span>()</code>
<div class="block">Relaxation factor in SOR
 SEE: setOmega</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/VariationalRefinement.html#getSorIterations--">getSorIterations</a></span>()</code>
<div class="block">Number of inner successive over-relaxation (SOR) iterations
         in the minimization procedure to solve the respective linear system.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/VariationalRefinement.html#setAlpha-float-">setAlpha</a></span>(float&nbsp;val)</code>
<div class="block">getAlpha SEE: getAlpha</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/VariationalRefinement.html#setDelta-float-">setDelta</a></span>(float&nbsp;val)</code>
<div class="block">getDelta SEE: getDelta</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/VariationalRefinement.html#setFixedPointIterations-int-">setFixedPointIterations</a></span>(int&nbsp;val)</code>
<div class="block">getFixedPointIterations SEE: getFixedPointIterations</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/VariationalRefinement.html#setGamma-float-">setGamma</a></span>(float&nbsp;val)</code>
<div class="block">getGamma SEE: getGamma</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/VariationalRefinement.html#setOmega-float-">setOmega</a></span>(float&nbsp;val)</code>
<div class="block">getOmega SEE: getOmega</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/VariationalRefinement.html#setSorIterations-int-">setSorIterations</a></span>(int&nbsp;val)</code>
<div class="block">getSorIterations SEE: getSorIterations</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.video.DenseOpticalFlow">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.video.<a href="../../../org/opencv/video/DenseOpticalFlow.html" title="class in org.opencv.video">DenseOpticalFlow</a></h3>
<code><a href="../../../org/opencv/video/DenseOpticalFlow.html#calc-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">calc</a>, <a href="../../../org/opencv/video/DenseOpticalFlow.html#collectGarbage--">collectGarbage</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#empty--">empty</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/VariationalRefinement.html" title="class in org.opencv.video">VariationalRefinement</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="calcUV-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calcUV</h4>
<pre>public&nbsp;void&nbsp;calcUV(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;I0,
                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;I1,
                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;flow_u,
                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;flow_v)</pre>
<div class="block">REF: calc function overload to handle separate horizontal (u) and vertical (v) flow components
 (to avoid extra splits/merges)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>I0</code> - automatically generated</dd>
<dd><code>I1</code> - automatically generated</dd>
<dd><code>flow_u</code> - automatically generated</dd>
<dd><code>flow_v</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/VariationalRefinement.html" title="class in org.opencv.video">VariationalRefinement</a>&nbsp;create()</pre>
<div class="block">Creates an instance of VariationalRefinement</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getAlpha--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAlpha</h4>
<pre>public&nbsp;float&nbsp;getAlpha()</pre>
<div class="block">Weight of the smoothness term
 SEE: setAlpha</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getDelta--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDelta</h4>
<pre>public&nbsp;float&nbsp;getDelta()</pre>
<div class="block">Weight of the color constancy term
 SEE: setDelta</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getFixedPointIterations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFixedPointIterations</h4>
<pre>public&nbsp;int&nbsp;getFixedPointIterations()</pre>
<div class="block">Number of outer (fixed-point) iterations in the minimization procedure.
 SEE: setFixedPointIterations</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getGamma--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGamma</h4>
<pre>public&nbsp;float&nbsp;getGamma()</pre>
<div class="block">Weight of the gradient constancy term
 SEE: setGamma</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getOmega--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOmega</h4>
<pre>public&nbsp;float&nbsp;getOmega()</pre>
<div class="block">Relaxation factor in SOR
 SEE: setOmega</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getSorIterations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSorIterations</h4>
<pre>public&nbsp;int&nbsp;getSorIterations()</pre>
<div class="block">Number of inner successive over-relaxation (SOR) iterations
         in the minimization procedure to solve the respective linear system.
 SEE: setSorIterations</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setAlpha-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAlpha</h4>
<pre>public&nbsp;void&nbsp;setAlpha(float&nbsp;val)</pre>
<div class="block">getAlpha SEE: getAlpha</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setDelta-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDelta</h4>
<pre>public&nbsp;void&nbsp;setDelta(float&nbsp;val)</pre>
<div class="block">getDelta SEE: getDelta</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setFixedPointIterations-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFixedPointIterations</h4>
<pre>public&nbsp;void&nbsp;setFixedPointIterations(int&nbsp;val)</pre>
<div class="block">getFixedPointIterations SEE: getFixedPointIterations</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setGamma-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGamma</h4>
<pre>public&nbsp;void&nbsp;setGamma(float&nbsp;val)</pre>
<div class="block">getGamma SEE: getGamma</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setOmega-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOmega</h4>
<pre>public&nbsp;void&nbsp;setOmega(float&nbsp;val)</pre>
<div class="block">getOmega SEE: getOmega</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setSorIterations-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setSorIterations</h4>
<pre>public&nbsp;void&nbsp;setSorIterations(int&nbsp;val)</pre>
<div class="block">getSorIterations SEE: getSorIterations</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/video/TrackerNano_Params.html" title="class in org.opencv.video"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/video/Video.html" title="class in org.opencv.video"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/video/VariationalRefinement.html" target="_top">Frames</a></li>
<li><a href="VariationalRefinement.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
