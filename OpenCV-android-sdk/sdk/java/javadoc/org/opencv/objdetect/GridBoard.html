<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:23 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>GridBoard (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="GridBoard (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/GraphicalCodeDetector.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/HOGDescriptor.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/GridBoard.html" target="_top">Frames</a></li>
<li><a href="GridBoard.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.objdetect</div>
<h2 title="Class GridBoard" class="title">Class GridBoard</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/objdetect/Board.html" title="class in org.opencv.objdetect">org.opencv.objdetect.Board</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.objdetect.GridBoard</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">GridBoard</span>
extends <a href="../../../org/opencv/objdetect/Board.html" title="class in org.opencv.objdetect">Board</a></pre>
<div class="block">Planar board with grid arrangement of markers

 More common type of board. All markers are placed in the same plane in a grid arrangement.
 The board image can be drawn using generateImage() method.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/GridBoard.html#GridBoard-org.opencv.core.Size-float-float-org.opencv.objdetect.Dictionary-">GridBoard</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
         float&nbsp;markerLength,
         float&nbsp;markerSeparation,
         <a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary)</code>
<div class="block">GridBoard constructor</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/GridBoard.html#GridBoard-org.opencv.core.Size-float-float-org.opencv.objdetect.Dictionary-org.opencv.core.Mat-">GridBoard</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
         float&nbsp;markerLength,
         float&nbsp;markerSeparation,
         <a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids)</code>
<div class="block">GridBoard constructor</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/GridBoard.html" title="class in org.opencv.objdetect">GridBoard</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/GridBoard.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/GridBoard.html#getGridSize--">getGridSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/GridBoard.html#getMarkerLength--">getMarkerLength</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/GridBoard.html#getMarkerSeparation--">getMarkerSeparation</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.objdetect.Board">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.objdetect.<a href="../../../org/opencv/objdetect/Board.html" title="class in org.opencv.objdetect">Board</a></h3>
<code><a href="../../../org/opencv/objdetect/Board.html#generateImage-org.opencv.core.Size-org.opencv.core.Mat-">generateImage</a>, <a href="../../../org/opencv/objdetect/Board.html#generateImage-org.opencv.core.Size-org.opencv.core.Mat-int-">generateImage</a>, <a href="../../../org/opencv/objdetect/Board.html#generateImage-org.opencv.core.Size-org.opencv.core.Mat-int-int-">generateImage</a>, <a href="../../../org/opencv/objdetect/Board.html#getDictionary--">getDictionary</a>, <a href="../../../org/opencv/objdetect/Board.html#getIds--">getIds</a>, <a href="../../../org/opencv/objdetect/Board.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/objdetect/Board.html#getObjPoints--">getObjPoints</a>, <a href="../../../org/opencv/objdetect/Board.html#getRightBottomCorner--">getRightBottomCorner</a>, <a href="../../../org/opencv/objdetect/Board.html#matchImagePoints-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">matchImagePoints</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="GridBoard-org.opencv.core.Size-float-float-org.opencv.objdetect.Dictionary-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GridBoard</h4>
<pre>public&nbsp;GridBoard(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                 float&nbsp;markerLength,
                 float&nbsp;markerSeparation,
                 <a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary)</pre>
<div class="block">GridBoard constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>size</code> - number of markers in x and y directions</dd>
<dd><code>markerLength</code> - marker side length (normally in meters)</dd>
<dd><code>markerSeparation</code> - separation between two markers (same unit as markerLength)</dd>
<dd><code>dictionary</code> - dictionary of markers indicating the type of markers</dd>
</dl>
</li>
</ul>
<a name="GridBoard-org.opencv.core.Size-float-float-org.opencv.objdetect.Dictionary-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>GridBoard</h4>
<pre>public&nbsp;GridBoard(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                 float&nbsp;markerLength,
                 float&nbsp;markerSeparation,
                 <a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids)</pre>
<div class="block">GridBoard constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>size</code> - number of markers in x and y directions</dd>
<dd><code>markerLength</code> - marker side length (normally in meters)</dd>
<dd><code>markerSeparation</code> - separation between two markers (same unit as markerLength)</dd>
<dd><code>dictionary</code> - dictionary of markers indicating the type of markers</dd>
<dd><code>ids</code> - set of marker ids in dictionary to use on board.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/GridBoard.html" title="class in org.opencv.objdetect">GridBoard</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="getGridSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGridSize</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;getGridSize()</pre>
</li>
</ul>
<a name="getMarkerLength--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMarkerLength</h4>
<pre>public&nbsp;float&nbsp;getMarkerLength()</pre>
</li>
</ul>
<a name="getMarkerSeparation--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getMarkerSeparation</h4>
<pre>public&nbsp;float&nbsp;getMarkerSeparation()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/GraphicalCodeDetector.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/HOGDescriptor.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/GridBoard.html" target="_top">Frames</a></li>
<li><a href="GridBoard.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
