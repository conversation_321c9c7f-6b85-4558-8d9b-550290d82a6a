<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:23 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>CharucoDetector (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CharucoDetector (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/CharucoBoard.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/CharucoParameters.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/CharucoDetector.html" target="_top">Frames</a></li>
<li><a href="CharucoDetector.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.objdetect</div>
<h2 title="Class CharucoDetector" class="title">Class CharucoDetector</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.objdetect.CharucoDetector</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">CharucoDetector</span>
extends <a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CharucoDetector.html#CharucoDetector-org.opencv.objdetect.CharucoBoard-">CharucoDetector</a></span>(<a href="../../../org/opencv/objdetect/CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board)</code>
<div class="block">Basic CharucoDetector constructor</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CharucoDetector.html#CharucoDetector-org.opencv.objdetect.CharucoBoard-org.opencv.objdetect.CharucoParameters-">CharucoDetector</a></span>(<a href="../../../org/opencv/objdetect/CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board,
               <a href="../../../org/opencv/objdetect/CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a>&nbsp;charucoParams)</code>
<div class="block">Basic CharucoDetector constructor</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CharucoDetector.html#CharucoDetector-org.opencv.objdetect.CharucoBoard-org.opencv.objdetect.CharucoParameters-org.opencv.objdetect.DetectorParameters-">CharucoDetector</a></span>(<a href="../../../org/opencv/objdetect/CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board,
               <a href="../../../org/opencv/objdetect/CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a>&nbsp;charucoParams,
               <a href="../../../org/opencv/objdetect/DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParams)</code>
<div class="block">Basic CharucoDetector constructor</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CharucoDetector.html#CharucoDetector-org.opencv.objdetect.CharucoBoard-org.opencv.objdetect.CharucoParameters-org.opencv.objdetect.DetectorParameters-org.opencv.objdetect.RefineParameters-">CharucoDetector</a></span>(<a href="../../../org/opencv/objdetect/CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board,
               <a href="../../../org/opencv/objdetect/CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a>&nbsp;charucoParams,
               <a href="../../../org/opencv/objdetect/DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParams,
               <a href="../../../org/opencv/objdetect/RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a>&nbsp;refineParams)</code>
<div class="block">Basic CharucoDetector constructor</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/CharucoDetector.html" title="class in org.opencv.objdetect">CharucoDetector</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CharucoDetector.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CharucoDetector.html#detectBoard-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">detectBoard</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds)</code>
<div class="block">detect aruco markers and interpolate position of ChArUco board corners</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CharucoDetector.html#detectBoard-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-">detectBoard</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds,
           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;markerCorners)</code>
<div class="block">detect aruco markers and interpolate position of ChArUco board corners</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CharucoDetector.html#detectBoard-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-">detectBoard</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds,
           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;markerCorners,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;markerIds)</code>
<div class="block">detect aruco markers and interpolate position of ChArUco board corners</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CharucoDetector.html#detectDiamonds-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-">detectDiamonds</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
              java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds)</code>
<div class="block">Detect ChArUco Diamond markers</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CharucoDetector.html#detectDiamonds-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-java.util.List-">detectDiamonds</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
              java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds,
              java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;markerCorners)</code>
<div class="block">Detect ChArUco Diamond markers</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CharucoDetector.html#detectDiamonds-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-">detectDiamonds</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
              java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds,
              java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;markerCorners,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;markerIds)</code>
<div class="block">Detect ChArUco Diamond markers</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/objdetect/CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CharucoDetector.html#getBoard--">getBoard</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/objdetect/CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CharucoDetector.html#getCharucoParameters--">getCharucoParameters</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/objdetect/DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CharucoDetector.html#getDetectorParameters--">getDetectorParameters</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/objdetect/RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CharucoDetector.html#getRefineParameters--">getRefineParameters</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CharucoDetector.html#setBoard-org.opencv.objdetect.CharucoBoard-">setBoard</a></span>(<a href="../../../org/opencv/objdetect/CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CharucoDetector.html#setCharucoParameters-org.opencv.objdetect.CharucoParameters-">setCharucoParameters</a></span>(<a href="../../../org/opencv/objdetect/CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a>&nbsp;charucoParameters)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CharucoDetector.html#setDetectorParameters-org.opencv.objdetect.DetectorParameters-">setDetectorParameters</a></span>(<a href="../../../org/opencv/objdetect/DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParameters)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CharucoDetector.html#setRefineParameters-org.opencv.objdetect.RefineParameters-">setRefineParameters</a></span>(<a href="../../../org/opencv/objdetect/RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a>&nbsp;refineParameters)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#empty--">empty</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="CharucoDetector-org.opencv.objdetect.CharucoBoard-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CharucoDetector</h4>
<pre>public&nbsp;CharucoDetector(<a href="../../../org/opencv/objdetect/CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board)</pre>
<div class="block">Basic CharucoDetector constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>board</code> - ChAruco board</dd>
</dl>
</li>
</ul>
<a name="CharucoDetector-org.opencv.objdetect.CharucoBoard-org.opencv.objdetect.CharucoParameters-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CharucoDetector</h4>
<pre>public&nbsp;CharucoDetector(<a href="../../../org/opencv/objdetect/CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board,
                       <a href="../../../org/opencv/objdetect/CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a>&nbsp;charucoParams)</pre>
<div class="block">Basic CharucoDetector constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>board</code> - ChAruco board</dd>
<dd><code>charucoParams</code> - charuco detection parameters</dd>
</dl>
</li>
</ul>
<a name="CharucoDetector-org.opencv.objdetect.CharucoBoard-org.opencv.objdetect.CharucoParameters-org.opencv.objdetect.DetectorParameters-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CharucoDetector</h4>
<pre>public&nbsp;CharucoDetector(<a href="../../../org/opencv/objdetect/CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board,
                       <a href="../../../org/opencv/objdetect/CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a>&nbsp;charucoParams,
                       <a href="../../../org/opencv/objdetect/DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParams)</pre>
<div class="block">Basic CharucoDetector constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>board</code> - ChAruco board</dd>
<dd><code>charucoParams</code> - charuco detection parameters</dd>
<dd><code>detectorParams</code> - marker detection parameters</dd>
</dl>
</li>
</ul>
<a name="CharucoDetector-org.opencv.objdetect.CharucoBoard-org.opencv.objdetect.CharucoParameters-org.opencv.objdetect.DetectorParameters-org.opencv.objdetect.RefineParameters-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CharucoDetector</h4>
<pre>public&nbsp;CharucoDetector(<a href="../../../org/opencv/objdetect/CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board,
                       <a href="../../../org/opencv/objdetect/CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a>&nbsp;charucoParams,
                       <a href="../../../org/opencv/objdetect/DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParams,
                       <a href="../../../org/opencv/objdetect/RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a>&nbsp;refineParams)</pre>
<div class="block">Basic CharucoDetector constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>board</code> - ChAruco board</dd>
<dd><code>charucoParams</code> - charuco detection parameters</dd>
<dd><code>detectorParams</code> - marker detection parameters</dd>
<dd><code>refineParams</code> - marker refine detection parameters</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/CharucoDetector.html" title="class in org.opencv.objdetect">CharucoDetector</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="detectBoard-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectBoard</h4>
<pre>public&nbsp;void&nbsp;detectBoard(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds)</pre>
<div class="block">detect aruco markers and interpolate position of ChArUco board corners</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image necesary for corner refinement. Note that markers are not detected and
 should be sent in corners and ids parameters.</dd>
<dd><code>charucoCorners</code> - interpolated chessboard corners.</dd>
<dd><code>charucoIds</code> - interpolated chessboard corners identifiers.
 corners are provided, (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers, the
 dimensions of this array should be Nx4. The order of the corners should be clockwise.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.

 This function receives the detected markers and returns the 2D position of the chessboard corners
 from a ChArUco board using the detected Aruco markers.

 If markerCorners and markerCorners are empty, the detectMarkers() will run and detect aruco markers and ids.

 If camera parameters are provided, the process is based in an approximated pose estimation, else it is based on local homography.
 Only visible corners are returned. For each corner, its corresponding identifier is also returned in charucoIds.
 SEE: findChessboardCorners</dd>
</dl>
</li>
</ul>
<a name="detectBoard-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectBoard</h4>
<pre>public&nbsp;void&nbsp;detectBoard(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds,
                        java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;markerCorners)</pre>
<div class="block">detect aruco markers and interpolate position of ChArUco board corners</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image necesary for corner refinement. Note that markers are not detected and
 should be sent in corners and ids parameters.</dd>
<dd><code>charucoCorners</code> - interpolated chessboard corners.</dd>
<dd><code>charucoIds</code> - interpolated chessboard corners identifiers.</dd>
<dd><code>markerCorners</code> - vector of already detected markers corners. For each marker, its four
 corners are provided, (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers, the
 dimensions of this array should be Nx4. The order of the corners should be clockwise.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.

 This function receives the detected markers and returns the 2D position of the chessboard corners
 from a ChArUco board using the detected Aruco markers.

 If markerCorners and markerCorners are empty, the detectMarkers() will run and detect aruco markers and ids.

 If camera parameters are provided, the process is based in an approximated pose estimation, else it is based on local homography.
 Only visible corners are returned. For each corner, its corresponding identifier is also returned in charucoIds.
 SEE: findChessboardCorners</dd>
</dl>
</li>
</ul>
<a name="detectBoard-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectBoard</h4>
<pre>public&nbsp;void&nbsp;detectBoard(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds,
                        java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;markerCorners,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;markerIds)</pre>
<div class="block">detect aruco markers and interpolate position of ChArUco board corners</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image necesary for corner refinement. Note that markers are not detected and
 should be sent in corners and ids parameters.</dd>
<dd><code>charucoCorners</code> - interpolated chessboard corners.</dd>
<dd><code>charucoIds</code> - interpolated chessboard corners identifiers.</dd>
<dd><code>markerCorners</code> - vector of already detected markers corners. For each marker, its four
 corners are provided, (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers, the
 dimensions of this array should be Nx4. The order of the corners should be clockwise.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.</dd>
<dd><code>markerIds</code> - list of identifiers for each marker in corners.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.

 This function receives the detected markers and returns the 2D position of the chessboard corners
 from a ChArUco board using the detected Aruco markers.

 If markerCorners and markerCorners are empty, the detectMarkers() will run and detect aruco markers and ids.

 If camera parameters are provided, the process is based in an approximated pose estimation, else it is based on local homography.
 Only visible corners are returned. For each corner, its corresponding identifier is also returned in charucoIds.
 SEE: findChessboardCorners</dd>
</dl>
</li>
</ul>
<a name="detectDiamonds-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectDiamonds</h4>
<pre>public&nbsp;void&nbsp;detectDiamonds(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds)</pre>
<div class="block">Detect ChArUco Diamond markers</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image necessary for corner subpixel.</dd>
<dd><code>diamondCorners</code> - output list of detected diamond corners (4 corners per diamond). The order
 is the same than in marker corners: top left, top right, bottom right and bottom left. Similar
 format than the corners returned by detectMarkers (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ).</dd>
<dd><code>diamondIds</code> - ids of the diamonds in diamondCorners. The id of each diamond is in fact of
 type Vec4i, so each diamond has 4 ids, which are the ids of the aruco markers composing the
 diamond.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.

 This function detects Diamond markers from the previous detected ArUco markers. The diamonds
 are returned in the diamondCorners and diamondIds parameters. If camera calibration parameters
 are provided, the diamond search is based on reprojection. If not, diamond search is based on
 homography. Homography is faster than reprojection, but less accurate.</dd>
</dl>
</li>
</ul>
<a name="detectDiamonds-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectDiamonds</h4>
<pre>public&nbsp;void&nbsp;detectDiamonds(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds,
                           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;markerCorners)</pre>
<div class="block">Detect ChArUco Diamond markers</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image necessary for corner subpixel.</dd>
<dd><code>diamondCorners</code> - output list of detected diamond corners (4 corners per diamond). The order
 is the same than in marker corners: top left, top right, bottom right and bottom left. Similar
 format than the corners returned by detectMarkers (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ).</dd>
<dd><code>diamondIds</code> - ids of the diamonds in diamondCorners. The id of each diamond is in fact of
 type Vec4i, so each diamond has 4 ids, which are the ids of the aruco markers composing the
 diamond.</dd>
<dd><code>markerCorners</code> - list of detected marker corners from detectMarkers function.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.

 This function detects Diamond markers from the previous detected ArUco markers. The diamonds
 are returned in the diamondCorners and diamondIds parameters. If camera calibration parameters
 are provided, the diamond search is based on reprojection. If not, diamond search is based on
 homography. Homography is faster than reprojection, but less accurate.</dd>
</dl>
</li>
</ul>
<a name="detectDiamonds-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectDiamonds</h4>
<pre>public&nbsp;void&nbsp;detectDiamonds(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds,
                           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;markerCorners,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;markerIds)</pre>
<div class="block">Detect ChArUco Diamond markers</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image necessary for corner subpixel.</dd>
<dd><code>diamondCorners</code> - output list of detected diamond corners (4 corners per diamond). The order
 is the same than in marker corners: top left, top right, bottom right and bottom left. Similar
 format than the corners returned by detectMarkers (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ).</dd>
<dd><code>diamondIds</code> - ids of the diamonds in diamondCorners. The id of each diamond is in fact of
 type Vec4i, so each diamond has 4 ids, which are the ids of the aruco markers composing the
 diamond.</dd>
<dd><code>markerCorners</code> - list of detected marker corners from detectMarkers function.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.</dd>
<dd><code>markerIds</code> - list of marker ids in markerCorners.
 If markerCorners and markerCorners are empty, the function detect aruco markers and ids.

 This function detects Diamond markers from the previous detected ArUco markers. The diamonds
 are returned in the diamondCorners and diamondIds parameters. If camera calibration parameters
 are provided, the diamond search is based on reprojection. If not, diamond search is based on
 homography. Homography is faster than reprojection, but less accurate.</dd>
</dl>
</li>
</ul>
<a name="getBoard--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBoard</h4>
<pre>public&nbsp;<a href="../../../org/opencv/objdetect/CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;getBoard()</pre>
</li>
</ul>
<a name="getCharucoParameters--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCharucoParameters</h4>
<pre>public&nbsp;<a href="../../../org/opencv/objdetect/CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a>&nbsp;getCharucoParameters()</pre>
</li>
</ul>
<a name="getDetectorParameters--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDetectorParameters</h4>
<pre>public&nbsp;<a href="../../../org/opencv/objdetect/DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;getDetectorParameters()</pre>
</li>
</ul>
<a name="getRefineParameters--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRefineParameters</h4>
<pre>public&nbsp;<a href="../../../org/opencv/objdetect/RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a>&nbsp;getRefineParameters()</pre>
</li>
</ul>
<a name="setBoard-org.opencv.objdetect.CharucoBoard-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBoard</h4>
<pre>public&nbsp;void&nbsp;setBoard(<a href="../../../org/opencv/objdetect/CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>&nbsp;board)</pre>
</li>
</ul>
<a name="setCharucoParameters-org.opencv.objdetect.CharucoParameters-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCharucoParameters</h4>
<pre>public&nbsp;void&nbsp;setCharucoParameters(<a href="../../../org/opencv/objdetect/CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a>&nbsp;charucoParameters)</pre>
</li>
</ul>
<a name="setDetectorParameters-org.opencv.objdetect.DetectorParameters-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDetectorParameters</h4>
<pre>public&nbsp;void&nbsp;setDetectorParameters(<a href="../../../org/opencv/objdetect/DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParameters)</pre>
</li>
</ul>
<a name="setRefineParameters-org.opencv.objdetect.RefineParameters-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setRefineParameters</h4>
<pre>public&nbsp;void&nbsp;setRefineParameters(<a href="../../../org/opencv/objdetect/RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a>&nbsp;refineParameters)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/CharucoBoard.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/CharucoParameters.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/CharucoDetector.html" target="_top">Frames</a></li>
<li><a href="CharucoDetector.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
