<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:22 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ArucoDetector (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ArucoDetector (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/opencv/objdetect/BarcodeDetector.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/ArucoDetector.html" target="_top">Frames</a></li>
<li><a href="ArucoDetector.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.objdetect</div>
<h2 title="Class ArucoDetector" class="title">Class ArucoDetector</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.objdetect.ArucoDetector</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ArucoDetector</span>
extends <a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></pre>
<div class="block">The main functionality of ArucoDetector class is detection of markers in an image with detectMarkers() method.

 After detecting some markers in the image, you can try to find undetected markers from this dictionary with
 refineDetectedMarkers() method.

 SEE: DetectorParameters, RefineParameters</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/ArucoDetector.html#ArucoDetector--">ArucoDetector</a></span>()</code>
<div class="block">Basic ArucoDetector constructor</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/ArucoDetector.html#ArucoDetector-org.opencv.objdetect.Dictionary-">ArucoDetector</a></span>(<a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary)</code>
<div class="block">Basic ArucoDetector constructor</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/ArucoDetector.html#ArucoDetector-org.opencv.objdetect.Dictionary-org.opencv.objdetect.DetectorParameters-">ArucoDetector</a></span>(<a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
             <a href="../../../org/opencv/objdetect/DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParams)</code>
<div class="block">Basic ArucoDetector constructor</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/ArucoDetector.html#ArucoDetector-org.opencv.objdetect.Dictionary-org.opencv.objdetect.DetectorParameters-org.opencv.objdetect.RefineParameters-">ArucoDetector</a></span>(<a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
             <a href="../../../org/opencv/objdetect/DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParams,
             <a href="../../../org/opencv/objdetect/RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a>&nbsp;refineParams)</code>
<div class="block">Basic ArucoDetector constructor</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/ArucoDetector.html" title="class in org.opencv.objdetect">ArucoDetector</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/ArucoDetector.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/ArucoDetector.html#detectMarkers-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-">detectMarkers</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
             java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids)</code>
<div class="block">Basic marker detection</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/ArucoDetector.html#detectMarkers-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-java.util.List-">detectMarkers</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
             java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids,
             java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedImgPoints)</code>
<div class="block">Basic marker detection</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/objdetect/DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/ArucoDetector.html#getDetectorParameters--">getDetectorParameters</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/ArucoDetector.html#getDictionary--">getDictionary</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/objdetect/RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/ArucoDetector.html#getRefineParameters--">getRefineParameters</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/ArucoDetector.html#refineDetectedMarkers-org.opencv.core.Mat-org.opencv.objdetect.Board-java.util.List-org.opencv.core.Mat-java.util.List-">refineDetectedMarkers</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                     <a href="../../../org/opencv/objdetect/Board.html" title="class in org.opencv.objdetect">Board</a>&nbsp;board,
                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedCorners)</code>
<div class="block">Refine not detected markers based on the already detected and the board layout</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/ArucoDetector.html#refineDetectedMarkers-org.opencv.core.Mat-org.opencv.objdetect.Board-java.util.List-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-">refineDetectedMarkers</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                     <a href="../../../org/opencv/objdetect/Board.html" title="class in org.opencv.objdetect">Board</a>&nbsp;board,
                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedCorners,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix)</code>
<div class="block">Refine not detected markers based on the already detected and the board layout</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/ArucoDetector.html#refineDetectedMarkers-org.opencv.core.Mat-org.opencv.objdetect.Board-java.util.List-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-">refineDetectedMarkers</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                     <a href="../../../org/opencv/objdetect/Board.html" title="class in org.opencv.objdetect">Board</a>&nbsp;board,
                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedCorners,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs)</code>
<div class="block">Refine not detected markers based on the already detected and the board layout</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/ArucoDetector.html#refineDetectedMarkers-org.opencv.core.Mat-org.opencv.objdetect.Board-java.util.List-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">refineDetectedMarkers</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                     <a href="../../../org/opencv/objdetect/Board.html" title="class in org.opencv.objdetect">Board</a>&nbsp;board,
                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedCorners,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;recoveredIdxs)</code>
<div class="block">Refine not detected markers based on the already detected and the board layout</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/ArucoDetector.html#setDetectorParameters-org.opencv.objdetect.DetectorParameters-">setDetectorParameters</a></span>(<a href="../../../org/opencv/objdetect/DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParameters)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/ArucoDetector.html#setDictionary-org.opencv.objdetect.Dictionary-">setDictionary</a></span>(<a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/ArucoDetector.html#setRefineParameters-org.opencv.objdetect.RefineParameters-">setRefineParameters</a></span>(<a href="../../../org/opencv/objdetect/RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a>&nbsp;refineParameters)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#empty--">empty</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ArucoDetector--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ArucoDetector</h4>
<pre>public&nbsp;ArucoDetector()</pre>
<div class="block">Basic ArucoDetector constructor</div>
</li>
</ul>
<a name="ArucoDetector-org.opencv.objdetect.Dictionary-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ArucoDetector</h4>
<pre>public&nbsp;ArucoDetector(<a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary)</pre>
<div class="block">Basic ArucoDetector constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dictionary</code> - indicates the type of markers that will be searched</dd>
</dl>
</li>
</ul>
<a name="ArucoDetector-org.opencv.objdetect.Dictionary-org.opencv.objdetect.DetectorParameters-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ArucoDetector</h4>
<pre>public&nbsp;ArucoDetector(<a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
                     <a href="../../../org/opencv/objdetect/DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParams)</pre>
<div class="block">Basic ArucoDetector constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dictionary</code> - indicates the type of markers that will be searched</dd>
<dd><code>detectorParams</code> - marker detection parameters</dd>
</dl>
</li>
</ul>
<a name="ArucoDetector-org.opencv.objdetect.Dictionary-org.opencv.objdetect.DetectorParameters-org.opencv.objdetect.RefineParameters-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ArucoDetector</h4>
<pre>public&nbsp;ArucoDetector(<a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
                     <a href="../../../org/opencv/objdetect/DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParams,
                     <a href="../../../org/opencv/objdetect/RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a>&nbsp;refineParams)</pre>
<div class="block">Basic ArucoDetector constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dictionary</code> - indicates the type of markers that will be searched</dd>
<dd><code>detectorParams</code> - marker detection parameters</dd>
<dd><code>refineParams</code> - marker refine detection parameters</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/ArucoDetector.html" title="class in org.opencv.objdetect">ArucoDetector</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="detectMarkers-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMarkers</h4>
<pre>public&nbsp;void&nbsp;detectMarkers(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                          java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids)</pre>
<div class="block">Basic marker detection</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image</dd>
<dd><code>corners</code> - vector of detected marker corners. For each marker, its four corners
 are provided, (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers,
 the dimensions of this array is Nx4. The order of the corners is clockwise.</dd>
<dd><code>ids</code> - vector of identifiers of the detected markers. The identifier is of type int
 (e.g. std::vector&lt;int&gt;). For N detected markers, the size of ids is also N.
 The identifiers have the same order than the markers in the imgPoints array.
 correct codification. Useful for debugging purposes.

 Performs marker detection in the input image. Only markers included in the specific dictionary
 are searched. For each detected marker, it returns the 2D position of its corner in the image
 and its corresponding identifier.
 Note that this function does not perform pose estimation.
 <b>Note:</b> The function does not correct lens distortion or takes it into account. It's recommended to undistort
 input image with corresponding camera model, if camera parameters are known
 SEE: undistort, estimatePoseSingleMarkers,  estimatePoseBoard</dd>
</dl>
</li>
</ul>
<a name="detectMarkers-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMarkers</h4>
<pre>public&nbsp;void&nbsp;detectMarkers(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                          java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids,
                          java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedImgPoints)</pre>
<div class="block">Basic marker detection</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image</dd>
<dd><code>corners</code> - vector of detected marker corners. For each marker, its four corners
 are provided, (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers,
 the dimensions of this array is Nx4. The order of the corners is clockwise.</dd>
<dd><code>ids</code> - vector of identifiers of the detected markers. The identifier is of type int
 (e.g. std::vector&lt;int&gt;). For N detected markers, the size of ids is also N.
 The identifiers have the same order than the markers in the imgPoints array.</dd>
<dd><code>rejectedImgPoints</code> - contains the imgPoints of those squares whose inner code has not a
 correct codification. Useful for debugging purposes.

 Performs marker detection in the input image. Only markers included in the specific dictionary
 are searched. For each detected marker, it returns the 2D position of its corner in the image
 and its corresponding identifier.
 Note that this function does not perform pose estimation.
 <b>Note:</b> The function does not correct lens distortion or takes it into account. It's recommended to undistort
 input image with corresponding camera model, if camera parameters are known
 SEE: undistort, estimatePoseSingleMarkers,  estimatePoseBoard</dd>
</dl>
</li>
</ul>
<a name="getDetectorParameters--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDetectorParameters</h4>
<pre>public&nbsp;<a href="../../../org/opencv/objdetect/DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;getDetectorParameters()</pre>
</li>
</ul>
<a name="getDictionary--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDictionary</h4>
<pre>public&nbsp;<a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;getDictionary()</pre>
</li>
</ul>
<a name="getRefineParameters--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRefineParameters</h4>
<pre>public&nbsp;<a href="../../../org/opencv/objdetect/RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a>&nbsp;getRefineParameters()</pre>
</li>
</ul>
<a name="refineDetectedMarkers-org.opencv.core.Mat-org.opencv.objdetect.Board-java.util.List-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>refineDetectedMarkers</h4>
<pre>public&nbsp;void&nbsp;refineDetectedMarkers(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                  <a href="../../../org/opencv/objdetect/Board.html" title="class in org.opencv.objdetect">Board</a>&nbsp;board,
                                  java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
                                  java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedCorners)</pre>
<div class="block">Refine not detected markers based on the already detected and the board layout</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image</dd>
<dd><code>board</code> - layout of markers in the board.</dd>
<dd><code>detectedCorners</code> - vector of already detected marker corners.</dd>
<dd><code>detectedIds</code> - vector of already detected marker identifiers.</dd>
<dd><code>rejectedCorners</code> - vector of rejected candidates during the marker detection process.
 \(A = \vecthreethree{f_x}{0}{c_x}{0}{f_y}{c_y}{0}{0}{1}\)
 \((k_1, k_2, p_1, p_2[, k_3[, k_4, k_5, k_6],[s_1, s_2, s_3, s_4]])\) of 4, 5, 8 or 12 elements
 original rejectedCorners array.

 This function tries to find markers that were not detected in the basic detecMarkers function.
 First, based on the current detected marker and the board layout, the function interpolates
 the position of the missing markers. Then it tries to find correspondence between the reprojected
 markers and the rejected candidates based on the minRepDistance and errorCorrectionRate parameters.
 If camera parameters and distortion coefficients are provided, missing markers are reprojected
 using projectPoint function. If not, missing marker projections are interpolated using global
 homography, and all the marker corners in the board must have the same Z coordinate.</dd>
</dl>
</li>
</ul>
<a name="refineDetectedMarkers-org.opencv.core.Mat-org.opencv.objdetect.Board-java.util.List-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>refineDetectedMarkers</h4>
<pre>public&nbsp;void&nbsp;refineDetectedMarkers(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                  <a href="../../../org/opencv/objdetect/Board.html" title="class in org.opencv.objdetect">Board</a>&nbsp;board,
                                  java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
                                  java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedCorners,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix)</pre>
<div class="block">Refine not detected markers based on the already detected and the board layout</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image</dd>
<dd><code>board</code> - layout of markers in the board.</dd>
<dd><code>detectedCorners</code> - vector of already detected marker corners.</dd>
<dd><code>detectedIds</code> - vector of already detected marker identifiers.</dd>
<dd><code>rejectedCorners</code> - vector of rejected candidates during the marker detection process.</dd>
<dd><code>cameraMatrix</code> - optional input 3x3 floating-point camera matrix
 \(A = \vecthreethree{f_x}{0}{c_x}{0}{f_y}{c_y}{0}{0}{1}\)
 \((k_1, k_2, p_1, p_2[, k_3[, k_4, k_5, k_6],[s_1, s_2, s_3, s_4]])\) of 4, 5, 8 or 12 elements
 original rejectedCorners array.

 This function tries to find markers that were not detected in the basic detecMarkers function.
 First, based on the current detected marker and the board layout, the function interpolates
 the position of the missing markers. Then it tries to find correspondence between the reprojected
 markers and the rejected candidates based on the minRepDistance and errorCorrectionRate parameters.
 If camera parameters and distortion coefficients are provided, missing markers are reprojected
 using projectPoint function. If not, missing marker projections are interpolated using global
 homography, and all the marker corners in the board must have the same Z coordinate.</dd>
</dl>
</li>
</ul>
<a name="refineDetectedMarkers-org.opencv.core.Mat-org.opencv.objdetect.Board-java.util.List-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>refineDetectedMarkers</h4>
<pre>public&nbsp;void&nbsp;refineDetectedMarkers(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                  <a href="../../../org/opencv/objdetect/Board.html" title="class in org.opencv.objdetect">Board</a>&nbsp;board,
                                  java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
                                  java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedCorners,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs)</pre>
<div class="block">Refine not detected markers based on the already detected and the board layout</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image</dd>
<dd><code>board</code> - layout of markers in the board.</dd>
<dd><code>detectedCorners</code> - vector of already detected marker corners.</dd>
<dd><code>detectedIds</code> - vector of already detected marker identifiers.</dd>
<dd><code>rejectedCorners</code> - vector of rejected candidates during the marker detection process.</dd>
<dd><code>cameraMatrix</code> - optional input 3x3 floating-point camera matrix
 \(A = \vecthreethree{f_x}{0}{c_x}{0}{f_y}{c_y}{0}{0}{1}\)</dd>
<dd><code>distCoeffs</code> - optional vector of distortion coefficients
 \((k_1, k_2, p_1, p_2[, k_3[, k_4, k_5, k_6],[s_1, s_2, s_3, s_4]])\) of 4, 5, 8 or 12 elements
 original rejectedCorners array.

 This function tries to find markers that were not detected in the basic detecMarkers function.
 First, based on the current detected marker and the board layout, the function interpolates
 the position of the missing markers. Then it tries to find correspondence between the reprojected
 markers and the rejected candidates based on the minRepDistance and errorCorrectionRate parameters.
 If camera parameters and distortion coefficients are provided, missing markers are reprojected
 using projectPoint function. If not, missing marker projections are interpolated using global
 homography, and all the marker corners in the board must have the same Z coordinate.</dd>
</dl>
</li>
</ul>
<a name="refineDetectedMarkers-org.opencv.core.Mat-org.opencv.objdetect.Board-java.util.List-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>refineDetectedMarkers</h4>
<pre>public&nbsp;void&nbsp;refineDetectedMarkers(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                  <a href="../../../org/opencv/objdetect/Board.html" title="class in org.opencv.objdetect">Board</a>&nbsp;board,
                                  java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
                                  java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rejectedCorners,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;recoveredIdxs)</pre>
<div class="block">Refine not detected markers based on the already detected and the board layout</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input image</dd>
<dd><code>board</code> - layout of markers in the board.</dd>
<dd><code>detectedCorners</code> - vector of already detected marker corners.</dd>
<dd><code>detectedIds</code> - vector of already detected marker identifiers.</dd>
<dd><code>rejectedCorners</code> - vector of rejected candidates during the marker detection process.</dd>
<dd><code>cameraMatrix</code> - optional input 3x3 floating-point camera matrix
 \(A = \vecthreethree{f_x}{0}{c_x}{0}{f_y}{c_y}{0}{0}{1}\)</dd>
<dd><code>distCoeffs</code> - optional vector of distortion coefficients
 \((k_1, k_2, p_1, p_2[, k_3[, k_4, k_5, k_6],[s_1, s_2, s_3, s_4]])\) of 4, 5, 8 or 12 elements</dd>
<dd><code>recoveredIdxs</code> - Optional array to returns the indexes of the recovered candidates in the
 original rejectedCorners array.

 This function tries to find markers that were not detected in the basic detecMarkers function.
 First, based on the current detected marker and the board layout, the function interpolates
 the position of the missing markers. Then it tries to find correspondence between the reprojected
 markers and the rejected candidates based on the minRepDistance and errorCorrectionRate parameters.
 If camera parameters and distortion coefficients are provided, missing markers are reprojected
 using projectPoint function. If not, missing marker projections are interpolated using global
 homography, and all the marker corners in the board must have the same Z coordinate.</dd>
</dl>
</li>
</ul>
<a name="setDetectorParameters-org.opencv.objdetect.DetectorParameters-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDetectorParameters</h4>
<pre>public&nbsp;void&nbsp;setDetectorParameters(<a href="../../../org/opencv/objdetect/DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;detectorParameters)</pre>
</li>
</ul>
<a name="setDictionary-org.opencv.objdetect.Dictionary-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDictionary</h4>
<pre>public&nbsp;void&nbsp;setDictionary(<a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary)</pre>
</li>
</ul>
<a name="setRefineParameters-org.opencv.objdetect.RefineParameters-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setRefineParameters</h4>
<pre>public&nbsp;void&nbsp;setRefineParameters(<a href="../../../org/opencv/objdetect/RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a>&nbsp;refineParameters)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/opencv/objdetect/BarcodeDetector.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/ArucoDetector.html" target="_top">Frames</a></li>
<li><a href="ArucoDetector.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
