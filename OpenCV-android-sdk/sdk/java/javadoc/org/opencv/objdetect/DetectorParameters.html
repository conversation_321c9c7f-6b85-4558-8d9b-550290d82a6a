<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:23 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DetectorParameters (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DetectorParameters (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/CharucoParameters.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/DetectorParameters.html" target="_top">Frames</a></li>
<li><a href="DetectorParameters.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.objdetect</div>
<h2 title="Class DetectorParameters" class="title">Class DetectorParameters</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.objdetect.DetectorParameters</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">DetectorParameters</span>
extends java.lang.Object</pre>
<div class="block">struct DetectorParameters is used by ArucoDetector</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#DetectorParameters--">DetectorParameters</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_adaptiveThreshConstant--">get_adaptiveThreshConstant</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_adaptiveThreshWinSizeMax--">get_adaptiveThreshWinSizeMax</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_adaptiveThreshWinSizeMin--">get_adaptiveThreshWinSizeMin</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_adaptiveThreshWinSizeStep--">get_adaptiveThreshWinSizeStep</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_aprilTagCriticalRad--">get_aprilTagCriticalRad</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_aprilTagDeglitch--">get_aprilTagDeglitch</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_aprilTagMaxLineFitMse--">get_aprilTagMaxLineFitMse</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_aprilTagMaxNmaxima--">get_aprilTagMaxNmaxima</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_aprilTagMinClusterPixels--">get_aprilTagMinClusterPixels</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_aprilTagMinWhiteBlackDiff--">get_aprilTagMinWhiteBlackDiff</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_aprilTagQuadDecimate--">get_aprilTagQuadDecimate</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_aprilTagQuadSigma--">get_aprilTagQuadSigma</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_cornerRefinementMaxIterations--">get_cornerRefinementMaxIterations</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_cornerRefinementMethod--">get_cornerRefinementMethod</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_cornerRefinementMinAccuracy--">get_cornerRefinementMinAccuracy</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_cornerRefinementWinSize--">get_cornerRefinementWinSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_detectInvertedMarker--">get_detectInvertedMarker</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_errorCorrectionRate--">get_errorCorrectionRate</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_markerBorderBits--">get_markerBorderBits</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_maxErroneousBitsInBorderRate--">get_maxErroneousBitsInBorderRate</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_maxMarkerPerimeterRate--">get_maxMarkerPerimeterRate</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_minCornerDistanceRate--">get_minCornerDistanceRate</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_minDistanceToBorder--">get_minDistanceToBorder</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_minMarkerDistanceRate--">get_minMarkerDistanceRate</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_minMarkerLengthRatioOriginalImg--">get_minMarkerLengthRatioOriginalImg</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_minMarkerPerimeterRate--">get_minMarkerPerimeterRate</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_minOtsuStdDev--">get_minOtsuStdDev</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_minSideLengthCanonicalImg--">get_minSideLengthCanonicalImg</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_perspectiveRemoveIgnoredMarginPerCell--">get_perspectiveRemoveIgnoredMarginPerCell</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_perspectiveRemovePixelPerCell--">get_perspectiveRemovePixelPerCell</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_polygonalApproxAccuracyRate--">get_polygonalApproxAccuracyRate</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#get_useAruco3Detection--">get_useAruco3Detection</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#getNativeObjAddr--">getNativeObjAddr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_adaptiveThreshConstant-double-">set_adaptiveThreshConstant</a></span>(double&nbsp;adaptiveThreshConstant)</code>&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_adaptiveThreshWinSizeMax-int-">set_adaptiveThreshWinSizeMax</a></span>(int&nbsp;adaptiveThreshWinSizeMax)</code>&nbsp;</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_adaptiveThreshWinSizeMin-int-">set_adaptiveThreshWinSizeMin</a></span>(int&nbsp;adaptiveThreshWinSizeMin)</code>&nbsp;</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_adaptiveThreshWinSizeStep-int-">set_adaptiveThreshWinSizeStep</a></span>(int&nbsp;adaptiveThreshWinSizeStep)</code>&nbsp;</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_aprilTagCriticalRad-float-">set_aprilTagCriticalRad</a></span>(float&nbsp;aprilTagCriticalRad)</code>&nbsp;</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_aprilTagDeglitch-int-">set_aprilTagDeglitch</a></span>(int&nbsp;aprilTagDeglitch)</code>&nbsp;</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_aprilTagMaxLineFitMse-float-">set_aprilTagMaxLineFitMse</a></span>(float&nbsp;aprilTagMaxLineFitMse)</code>&nbsp;</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_aprilTagMaxNmaxima-int-">set_aprilTagMaxNmaxima</a></span>(int&nbsp;aprilTagMaxNmaxima)</code>&nbsp;</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_aprilTagMinClusterPixels-int-">set_aprilTagMinClusterPixels</a></span>(int&nbsp;aprilTagMinClusterPixels)</code>&nbsp;</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_aprilTagMinWhiteBlackDiff-int-">set_aprilTagMinWhiteBlackDiff</a></span>(int&nbsp;aprilTagMinWhiteBlackDiff)</code>&nbsp;</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_aprilTagQuadDecimate-float-">set_aprilTagQuadDecimate</a></span>(float&nbsp;aprilTagQuadDecimate)</code>&nbsp;</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_aprilTagQuadSigma-float-">set_aprilTagQuadSigma</a></span>(float&nbsp;aprilTagQuadSigma)</code>&nbsp;</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_cornerRefinementMaxIterations-int-">set_cornerRefinementMaxIterations</a></span>(int&nbsp;cornerRefinementMaxIterations)</code>&nbsp;</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_cornerRefinementMethod-int-">set_cornerRefinementMethod</a></span>(int&nbsp;cornerRefinementMethod)</code>&nbsp;</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_cornerRefinementMinAccuracy-double-">set_cornerRefinementMinAccuracy</a></span>(double&nbsp;cornerRefinementMinAccuracy)</code>&nbsp;</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_cornerRefinementWinSize-int-">set_cornerRefinementWinSize</a></span>(int&nbsp;cornerRefinementWinSize)</code>&nbsp;</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_detectInvertedMarker-boolean-">set_detectInvertedMarker</a></span>(boolean&nbsp;detectInvertedMarker)</code>&nbsp;</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_errorCorrectionRate-double-">set_errorCorrectionRate</a></span>(double&nbsp;errorCorrectionRate)</code>&nbsp;</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_markerBorderBits-int-">set_markerBorderBits</a></span>(int&nbsp;markerBorderBits)</code>&nbsp;</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_maxErroneousBitsInBorderRate-double-">set_maxErroneousBitsInBorderRate</a></span>(double&nbsp;maxErroneousBitsInBorderRate)</code>&nbsp;</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_maxMarkerPerimeterRate-double-">set_maxMarkerPerimeterRate</a></span>(double&nbsp;maxMarkerPerimeterRate)</code>&nbsp;</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_minCornerDistanceRate-double-">set_minCornerDistanceRate</a></span>(double&nbsp;minCornerDistanceRate)</code>&nbsp;</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_minDistanceToBorder-int-">set_minDistanceToBorder</a></span>(int&nbsp;minDistanceToBorder)</code>&nbsp;</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_minMarkerDistanceRate-double-">set_minMarkerDistanceRate</a></span>(double&nbsp;minMarkerDistanceRate)</code>&nbsp;</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_minMarkerLengthRatioOriginalImg-float-">set_minMarkerLengthRatioOriginalImg</a></span>(float&nbsp;minMarkerLengthRatioOriginalImg)</code>&nbsp;</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_minMarkerPerimeterRate-double-">set_minMarkerPerimeterRate</a></span>(double&nbsp;minMarkerPerimeterRate)</code>&nbsp;</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_minOtsuStdDev-double-">set_minOtsuStdDev</a></span>(double&nbsp;minOtsuStdDev)</code>&nbsp;</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_minSideLengthCanonicalImg-int-">set_minSideLengthCanonicalImg</a></span>(int&nbsp;minSideLengthCanonicalImg)</code>&nbsp;</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_perspectiveRemoveIgnoredMarginPerCell-double-">set_perspectiveRemoveIgnoredMarginPerCell</a></span>(double&nbsp;perspectiveRemoveIgnoredMarginPerCell)</code>&nbsp;</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_perspectiveRemovePixelPerCell-int-">set_perspectiveRemovePixelPerCell</a></span>(int&nbsp;perspectiveRemovePixelPerCell)</code>&nbsp;</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_polygonalApproxAccuracyRate-double-">set_polygonalApproxAccuracyRate</a></span>(double&nbsp;polygonalApproxAccuracyRate)</code>&nbsp;</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/DetectorParameters.html#set_useAruco3Detection-boolean-">set_useAruco3Detection</a></span>(boolean&nbsp;useAruco3Detection)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="DetectorParameters--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DetectorParameters</h4>
<pre>public&nbsp;DetectorParameters()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="get_adaptiveThreshConstant--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_adaptiveThreshConstant</h4>
<pre>public&nbsp;double&nbsp;get_adaptiveThreshConstant()</pre>
</li>
</ul>
<a name="get_adaptiveThreshWinSizeMax--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_adaptiveThreshWinSizeMax</h4>
<pre>public&nbsp;int&nbsp;get_adaptiveThreshWinSizeMax()</pre>
</li>
</ul>
<a name="get_adaptiveThreshWinSizeMin--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_adaptiveThreshWinSizeMin</h4>
<pre>public&nbsp;int&nbsp;get_adaptiveThreshWinSizeMin()</pre>
</li>
</ul>
<a name="get_adaptiveThreshWinSizeStep--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_adaptiveThreshWinSizeStep</h4>
<pre>public&nbsp;int&nbsp;get_adaptiveThreshWinSizeStep()</pre>
</li>
</ul>
<a name="get_aprilTagCriticalRad--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_aprilTagCriticalRad</h4>
<pre>public&nbsp;float&nbsp;get_aprilTagCriticalRad()</pre>
</li>
</ul>
<a name="get_aprilTagDeglitch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_aprilTagDeglitch</h4>
<pre>public&nbsp;int&nbsp;get_aprilTagDeglitch()</pre>
</li>
</ul>
<a name="get_aprilTagMaxLineFitMse--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_aprilTagMaxLineFitMse</h4>
<pre>public&nbsp;float&nbsp;get_aprilTagMaxLineFitMse()</pre>
</li>
</ul>
<a name="get_aprilTagMaxNmaxima--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_aprilTagMaxNmaxima</h4>
<pre>public&nbsp;int&nbsp;get_aprilTagMaxNmaxima()</pre>
</li>
</ul>
<a name="get_aprilTagMinClusterPixels--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_aprilTagMinClusterPixels</h4>
<pre>public&nbsp;int&nbsp;get_aprilTagMinClusterPixels()</pre>
</li>
</ul>
<a name="get_aprilTagMinWhiteBlackDiff--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_aprilTagMinWhiteBlackDiff</h4>
<pre>public&nbsp;int&nbsp;get_aprilTagMinWhiteBlackDiff()</pre>
</li>
</ul>
<a name="get_aprilTagQuadDecimate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_aprilTagQuadDecimate</h4>
<pre>public&nbsp;float&nbsp;get_aprilTagQuadDecimate()</pre>
</li>
</ul>
<a name="get_aprilTagQuadSigma--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_aprilTagQuadSigma</h4>
<pre>public&nbsp;float&nbsp;get_aprilTagQuadSigma()</pre>
</li>
</ul>
<a name="get_cornerRefinementMaxIterations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_cornerRefinementMaxIterations</h4>
<pre>public&nbsp;int&nbsp;get_cornerRefinementMaxIterations()</pre>
</li>
</ul>
<a name="get_cornerRefinementMethod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_cornerRefinementMethod</h4>
<pre>public&nbsp;int&nbsp;get_cornerRefinementMethod()</pre>
</li>
</ul>
<a name="get_cornerRefinementMinAccuracy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_cornerRefinementMinAccuracy</h4>
<pre>public&nbsp;double&nbsp;get_cornerRefinementMinAccuracy()</pre>
</li>
</ul>
<a name="get_cornerRefinementWinSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_cornerRefinementWinSize</h4>
<pre>public&nbsp;int&nbsp;get_cornerRefinementWinSize()</pre>
</li>
</ul>
<a name="get_detectInvertedMarker--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_detectInvertedMarker</h4>
<pre>public&nbsp;boolean&nbsp;get_detectInvertedMarker()</pre>
</li>
</ul>
<a name="get_errorCorrectionRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_errorCorrectionRate</h4>
<pre>public&nbsp;double&nbsp;get_errorCorrectionRate()</pre>
</li>
</ul>
<a name="get_markerBorderBits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_markerBorderBits</h4>
<pre>public&nbsp;int&nbsp;get_markerBorderBits()</pre>
</li>
</ul>
<a name="get_maxErroneousBitsInBorderRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_maxErroneousBitsInBorderRate</h4>
<pre>public&nbsp;double&nbsp;get_maxErroneousBitsInBorderRate()</pre>
</li>
</ul>
<a name="get_maxMarkerPerimeterRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_maxMarkerPerimeterRate</h4>
<pre>public&nbsp;double&nbsp;get_maxMarkerPerimeterRate()</pre>
</li>
</ul>
<a name="get_minCornerDistanceRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_minCornerDistanceRate</h4>
<pre>public&nbsp;double&nbsp;get_minCornerDistanceRate()</pre>
</li>
</ul>
<a name="get_minDistanceToBorder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_minDistanceToBorder</h4>
<pre>public&nbsp;int&nbsp;get_minDistanceToBorder()</pre>
</li>
</ul>
<a name="get_minMarkerDistanceRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_minMarkerDistanceRate</h4>
<pre>public&nbsp;double&nbsp;get_minMarkerDistanceRate()</pre>
</li>
</ul>
<a name="get_minMarkerLengthRatioOriginalImg--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_minMarkerLengthRatioOriginalImg</h4>
<pre>public&nbsp;float&nbsp;get_minMarkerLengthRatioOriginalImg()</pre>
</li>
</ul>
<a name="get_minMarkerPerimeterRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_minMarkerPerimeterRate</h4>
<pre>public&nbsp;double&nbsp;get_minMarkerPerimeterRate()</pre>
</li>
</ul>
<a name="get_minOtsuStdDev--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_minOtsuStdDev</h4>
<pre>public&nbsp;double&nbsp;get_minOtsuStdDev()</pre>
</li>
</ul>
<a name="get_minSideLengthCanonicalImg--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_minSideLengthCanonicalImg</h4>
<pre>public&nbsp;int&nbsp;get_minSideLengthCanonicalImg()</pre>
</li>
</ul>
<a name="get_perspectiveRemoveIgnoredMarginPerCell--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_perspectiveRemoveIgnoredMarginPerCell</h4>
<pre>public&nbsp;double&nbsp;get_perspectiveRemoveIgnoredMarginPerCell()</pre>
</li>
</ul>
<a name="get_perspectiveRemovePixelPerCell--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_perspectiveRemovePixelPerCell</h4>
<pre>public&nbsp;int&nbsp;get_perspectiveRemovePixelPerCell()</pre>
</li>
</ul>
<a name="get_polygonalApproxAccuracyRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_polygonalApproxAccuracyRate</h4>
<pre>public&nbsp;double&nbsp;get_polygonalApproxAccuracyRate()</pre>
</li>
</ul>
<a name="get_useAruco3Detection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_useAruco3Detection</h4>
<pre>public&nbsp;boolean&nbsp;get_useAruco3Detection()</pre>
</li>
</ul>
<a name="getNativeObjAddr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNativeObjAddr</h4>
<pre>public&nbsp;long&nbsp;getNativeObjAddr()</pre>
</li>
</ul>
<a name="set_adaptiveThreshConstant-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_adaptiveThreshConstant</h4>
<pre>public&nbsp;void&nbsp;set_adaptiveThreshConstant(double&nbsp;adaptiveThreshConstant)</pre>
</li>
</ul>
<a name="set_adaptiveThreshWinSizeMax-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_adaptiveThreshWinSizeMax</h4>
<pre>public&nbsp;void&nbsp;set_adaptiveThreshWinSizeMax(int&nbsp;adaptiveThreshWinSizeMax)</pre>
</li>
</ul>
<a name="set_adaptiveThreshWinSizeMin-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_adaptiveThreshWinSizeMin</h4>
<pre>public&nbsp;void&nbsp;set_adaptiveThreshWinSizeMin(int&nbsp;adaptiveThreshWinSizeMin)</pre>
</li>
</ul>
<a name="set_adaptiveThreshWinSizeStep-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_adaptiveThreshWinSizeStep</h4>
<pre>public&nbsp;void&nbsp;set_adaptiveThreshWinSizeStep(int&nbsp;adaptiveThreshWinSizeStep)</pre>
</li>
</ul>
<a name="set_aprilTagCriticalRad-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_aprilTagCriticalRad</h4>
<pre>public&nbsp;void&nbsp;set_aprilTagCriticalRad(float&nbsp;aprilTagCriticalRad)</pre>
</li>
</ul>
<a name="set_aprilTagDeglitch-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_aprilTagDeglitch</h4>
<pre>public&nbsp;void&nbsp;set_aprilTagDeglitch(int&nbsp;aprilTagDeglitch)</pre>
</li>
</ul>
<a name="set_aprilTagMaxLineFitMse-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_aprilTagMaxLineFitMse</h4>
<pre>public&nbsp;void&nbsp;set_aprilTagMaxLineFitMse(float&nbsp;aprilTagMaxLineFitMse)</pre>
</li>
</ul>
<a name="set_aprilTagMaxNmaxima-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_aprilTagMaxNmaxima</h4>
<pre>public&nbsp;void&nbsp;set_aprilTagMaxNmaxima(int&nbsp;aprilTagMaxNmaxima)</pre>
</li>
</ul>
<a name="set_aprilTagMinClusterPixels-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_aprilTagMinClusterPixels</h4>
<pre>public&nbsp;void&nbsp;set_aprilTagMinClusterPixels(int&nbsp;aprilTagMinClusterPixels)</pre>
</li>
</ul>
<a name="set_aprilTagMinWhiteBlackDiff-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_aprilTagMinWhiteBlackDiff</h4>
<pre>public&nbsp;void&nbsp;set_aprilTagMinWhiteBlackDiff(int&nbsp;aprilTagMinWhiteBlackDiff)</pre>
</li>
</ul>
<a name="set_aprilTagQuadDecimate-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_aprilTagQuadDecimate</h4>
<pre>public&nbsp;void&nbsp;set_aprilTagQuadDecimate(float&nbsp;aprilTagQuadDecimate)</pre>
</li>
</ul>
<a name="set_aprilTagQuadSigma-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_aprilTagQuadSigma</h4>
<pre>public&nbsp;void&nbsp;set_aprilTagQuadSigma(float&nbsp;aprilTagQuadSigma)</pre>
</li>
</ul>
<a name="set_cornerRefinementMaxIterations-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_cornerRefinementMaxIterations</h4>
<pre>public&nbsp;void&nbsp;set_cornerRefinementMaxIterations(int&nbsp;cornerRefinementMaxIterations)</pre>
</li>
</ul>
<a name="set_cornerRefinementMethod-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_cornerRefinementMethod</h4>
<pre>public&nbsp;void&nbsp;set_cornerRefinementMethod(int&nbsp;cornerRefinementMethod)</pre>
</li>
</ul>
<a name="set_cornerRefinementMinAccuracy-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_cornerRefinementMinAccuracy</h4>
<pre>public&nbsp;void&nbsp;set_cornerRefinementMinAccuracy(double&nbsp;cornerRefinementMinAccuracy)</pre>
</li>
</ul>
<a name="set_cornerRefinementWinSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_cornerRefinementWinSize</h4>
<pre>public&nbsp;void&nbsp;set_cornerRefinementWinSize(int&nbsp;cornerRefinementWinSize)</pre>
</li>
</ul>
<a name="set_detectInvertedMarker-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_detectInvertedMarker</h4>
<pre>public&nbsp;void&nbsp;set_detectInvertedMarker(boolean&nbsp;detectInvertedMarker)</pre>
</li>
</ul>
<a name="set_errorCorrectionRate-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_errorCorrectionRate</h4>
<pre>public&nbsp;void&nbsp;set_errorCorrectionRate(double&nbsp;errorCorrectionRate)</pre>
</li>
</ul>
<a name="set_markerBorderBits-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_markerBorderBits</h4>
<pre>public&nbsp;void&nbsp;set_markerBorderBits(int&nbsp;markerBorderBits)</pre>
</li>
</ul>
<a name="set_maxErroneousBitsInBorderRate-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_maxErroneousBitsInBorderRate</h4>
<pre>public&nbsp;void&nbsp;set_maxErroneousBitsInBorderRate(double&nbsp;maxErroneousBitsInBorderRate)</pre>
</li>
</ul>
<a name="set_maxMarkerPerimeterRate-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_maxMarkerPerimeterRate</h4>
<pre>public&nbsp;void&nbsp;set_maxMarkerPerimeterRate(double&nbsp;maxMarkerPerimeterRate)</pre>
</li>
</ul>
<a name="set_minCornerDistanceRate-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_minCornerDistanceRate</h4>
<pre>public&nbsp;void&nbsp;set_minCornerDistanceRate(double&nbsp;minCornerDistanceRate)</pre>
</li>
</ul>
<a name="set_minDistanceToBorder-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_minDistanceToBorder</h4>
<pre>public&nbsp;void&nbsp;set_minDistanceToBorder(int&nbsp;minDistanceToBorder)</pre>
</li>
</ul>
<a name="set_minMarkerDistanceRate-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_minMarkerDistanceRate</h4>
<pre>public&nbsp;void&nbsp;set_minMarkerDistanceRate(double&nbsp;minMarkerDistanceRate)</pre>
</li>
</ul>
<a name="set_minMarkerLengthRatioOriginalImg-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_minMarkerLengthRatioOriginalImg</h4>
<pre>public&nbsp;void&nbsp;set_minMarkerLengthRatioOriginalImg(float&nbsp;minMarkerLengthRatioOriginalImg)</pre>
</li>
</ul>
<a name="set_minMarkerPerimeterRate-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_minMarkerPerimeterRate</h4>
<pre>public&nbsp;void&nbsp;set_minMarkerPerimeterRate(double&nbsp;minMarkerPerimeterRate)</pre>
</li>
</ul>
<a name="set_minOtsuStdDev-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_minOtsuStdDev</h4>
<pre>public&nbsp;void&nbsp;set_minOtsuStdDev(double&nbsp;minOtsuStdDev)</pre>
</li>
</ul>
<a name="set_minSideLengthCanonicalImg-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_minSideLengthCanonicalImg</h4>
<pre>public&nbsp;void&nbsp;set_minSideLengthCanonicalImg(int&nbsp;minSideLengthCanonicalImg)</pre>
</li>
</ul>
<a name="set_perspectiveRemoveIgnoredMarginPerCell-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_perspectiveRemoveIgnoredMarginPerCell</h4>
<pre>public&nbsp;void&nbsp;set_perspectiveRemoveIgnoredMarginPerCell(double&nbsp;perspectiveRemoveIgnoredMarginPerCell)</pre>
</li>
</ul>
<a name="set_perspectiveRemovePixelPerCell-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_perspectiveRemovePixelPerCell</h4>
<pre>public&nbsp;void&nbsp;set_perspectiveRemovePixelPerCell(int&nbsp;perspectiveRemovePixelPerCell)</pre>
</li>
</ul>
<a name="set_polygonalApproxAccuracyRate-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_polygonalApproxAccuracyRate</h4>
<pre>public&nbsp;void&nbsp;set_polygonalApproxAccuracyRate(double&nbsp;polygonalApproxAccuracyRate)</pre>
</li>
</ul>
<a name="set_useAruco3Detection-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>set_useAruco3Detection</h4>
<pre>public&nbsp;void&nbsp;set_useAruco3Detection(boolean&nbsp;useAruco3Detection)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/CharucoParameters.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/DetectorParameters.html" target="_top">Frames</a></li>
<li><a href="DetectorParameters.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
