<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:23 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Objdetect (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Objdetect (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/HOGDescriptor.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/QRCodeDetector.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/Objdetect.html" target="_top">Frames</a></li>
<li><a href="Objdetect.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.objdetect</div>
<h2 title="Class Objdetect" class="title">Class Objdetect</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.objdetect.Objdetect</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Objdetect</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#CASCADE_DO_CANNY_PRUNING">CASCADE_DO_CANNY_PRUNING</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#CASCADE_DO_ROUGH_SEARCH">CASCADE_DO_ROUGH_SEARCH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#CASCADE_FIND_BIGGEST_OBJECT">CASCADE_FIND_BIGGEST_OBJECT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#CASCADE_SCALE_IMAGE">CASCADE_SCALE_IMAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#CORNER_REFINE_APRILTAG">CORNER_REFINE_APRILTAG</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#CORNER_REFINE_CONTOUR">CORNER_REFINE_CONTOUR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#CORNER_REFINE_NONE">CORNER_REFINE_NONE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#CORNER_REFINE_SUBPIX">CORNER_REFINE_SUBPIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DetectionBasedTracker_DETECTED">DetectionBasedTracker_DETECTED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DetectionBasedTracker_DETECTED_NOT_SHOWN_YET">DetectionBasedTracker_DETECTED_NOT_SHOWN_YET</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DetectionBasedTracker_DETECTED_TEMPORARY_LOST">DetectionBasedTracker_DETECTED_TEMPORARY_LOST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DetectionBasedTracker_WRONG_OBJECT">DetectionBasedTracker_WRONG_OBJECT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_4X4_100">DICT_4X4_100</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_4X4_1000">DICT_4X4_1000</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_4X4_250">DICT_4X4_250</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_4X4_50">DICT_4X4_50</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_5X5_100">DICT_5X5_100</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_5X5_1000">DICT_5X5_1000</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_5X5_250">DICT_5X5_250</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_5X5_50">DICT_5X5_50</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_6X6_100">DICT_6X6_100</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_6X6_1000">DICT_6X6_1000</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_6X6_250">DICT_6X6_250</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_6X6_50">DICT_6X6_50</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_7X7_100">DICT_7X7_100</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_7X7_1000">DICT_7X7_1000</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_7X7_250">DICT_7X7_250</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_7X7_50">DICT_7X7_50</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_APRILTAG_16h5">DICT_APRILTAG_16h5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_APRILTAG_25h9">DICT_APRILTAG_25h9</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_APRILTAG_36h10">DICT_APRILTAG_36h10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_APRILTAG_36h11">DICT_APRILTAG_36h11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_ARUCO_MIP_36h12">DICT_ARUCO_MIP_36h12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#DICT_ARUCO_ORIGINAL">DICT_ARUCO_ORIGINAL</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#Objdetect--">Objdetect</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#drawDetectedCornersCharuco-org.opencv.core.Mat-org.opencv.core.Mat-">drawDetectedCornersCharuco</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners)</code>
<div class="block">Draws a set of Charuco corners</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#drawDetectedCornersCharuco-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">drawDetectedCornersCharuco</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds)</code>
<div class="block">Draws a set of Charuco corners</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#drawDetectedCornersCharuco-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Scalar-">drawDetectedCornersCharuco</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds,
                          <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;cornerColor)</code>
<div class="block">Draws a set of Charuco corners</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#drawDetectedDiamonds-org.opencv.core.Mat-java.util.List-">drawDetectedDiamonds</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                    java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners)</code>
<div class="block">Draw a set of detected ChArUco Diamond markers</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#drawDetectedDiamonds-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-">drawDetectedDiamonds</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                    java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds)</code>
<div class="block">Draw a set of detected ChArUco Diamond markers</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#drawDetectedDiamonds-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-org.opencv.core.Scalar-">drawDetectedDiamonds</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                    java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds,
                    <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;borderColor)</code>
<div class="block">Draw a set of detected ChArUco Diamond markers</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#drawDetectedMarkers-org.opencv.core.Mat-java.util.List-">drawDetectedMarkers</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                   java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners)</code>
<div class="block">Draw detected markers in image</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#drawDetectedMarkers-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-">drawDetectedMarkers</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                   java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners,
                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids)</code>
<div class="block">Draw detected markers in image</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#drawDetectedMarkers-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-org.opencv.core.Scalar-">drawDetectedMarkers</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                   java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners,
                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids,
                   <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;borderColor)</code>
<div class="block">Draw detected markers in image</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#extendDictionary-int-int-">extendDictionary</a></span>(int&nbsp;nMarkers,
                int&nbsp;markerSize)</code>
<div class="block">Extend base dictionary by new nMarkers</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#extendDictionary-int-int-org.opencv.objdetect.Dictionary-">extendDictionary</a></span>(int&nbsp;nMarkers,
                int&nbsp;markerSize,
                <a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;baseDictionary)</code>
<div class="block">Extend base dictionary by new nMarkers</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#extendDictionary-int-int-org.opencv.objdetect.Dictionary-int-">extendDictionary</a></span>(int&nbsp;nMarkers,
                int&nbsp;markerSize,
                <a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;baseDictionary,
                int&nbsp;randomSeed)</code>
<div class="block">Extend base dictionary by new nMarkers</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#generateImageMarker-org.opencv.objdetect.Dictionary-int-int-org.opencv.core.Mat-">generateImageMarker</a></span>(<a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
                   int&nbsp;id,
                   int&nbsp;sidePixels,
                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</code>
<div class="block">Generate a canonical marker image</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#generateImageMarker-org.opencv.objdetect.Dictionary-int-int-org.opencv.core.Mat-int-">generateImageMarker</a></span>(<a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
                   int&nbsp;id,
                   int&nbsp;sidePixels,
                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                   int&nbsp;borderBits)</code>
<div class="block">Generate a canonical marker image</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#getPredefinedDictionary-int-">getPredefinedDictionary</a></span>(int&nbsp;dict)</code>
<div class="block">Returns one of the predefined dictionaries referenced by DICT_*.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#groupRectangles-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-int-">groupRectangles</a></span>(<a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;rectList,
               <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;weights,
               int&nbsp;groupThreshold)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Objdetect.html#groupRectangles-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-int-double-">groupRectangles</a></span>(<a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;rectList,
               <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;weights,
               int&nbsp;groupThreshold,
               double&nbsp;eps)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="CASCADE_DO_CANNY_PRUNING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CASCADE_DO_CANNY_PRUNING</h4>
<pre>public static final&nbsp;int CASCADE_DO_CANNY_PRUNING</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.CASCADE_DO_CANNY_PRUNING">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CASCADE_DO_ROUGH_SEARCH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CASCADE_DO_ROUGH_SEARCH</h4>
<pre>public static final&nbsp;int CASCADE_DO_ROUGH_SEARCH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.CASCADE_DO_ROUGH_SEARCH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CASCADE_FIND_BIGGEST_OBJECT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CASCADE_FIND_BIGGEST_OBJECT</h4>
<pre>public static final&nbsp;int CASCADE_FIND_BIGGEST_OBJECT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.CASCADE_FIND_BIGGEST_OBJECT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CASCADE_SCALE_IMAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CASCADE_SCALE_IMAGE</h4>
<pre>public static final&nbsp;int CASCADE_SCALE_IMAGE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.CASCADE_SCALE_IMAGE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CORNER_REFINE_APRILTAG">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CORNER_REFINE_APRILTAG</h4>
<pre>public static final&nbsp;int CORNER_REFINE_APRILTAG</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.CORNER_REFINE_APRILTAG">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CORNER_REFINE_CONTOUR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CORNER_REFINE_CONTOUR</h4>
<pre>public static final&nbsp;int CORNER_REFINE_CONTOUR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.CORNER_REFINE_CONTOUR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CORNER_REFINE_NONE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CORNER_REFINE_NONE</h4>
<pre>public static final&nbsp;int CORNER_REFINE_NONE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.CORNER_REFINE_NONE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CORNER_REFINE_SUBPIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CORNER_REFINE_SUBPIX</h4>
<pre>public static final&nbsp;int CORNER_REFINE_SUBPIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.CORNER_REFINE_SUBPIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DetectionBasedTracker_DETECTED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DetectionBasedTracker_DETECTED</h4>
<pre>public static final&nbsp;int DetectionBasedTracker_DETECTED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DetectionBasedTracker_DETECTED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DetectionBasedTracker_DETECTED_NOT_SHOWN_YET">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DetectionBasedTracker_DETECTED_NOT_SHOWN_YET</h4>
<pre>public static final&nbsp;int DetectionBasedTracker_DETECTED_NOT_SHOWN_YET</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DetectionBasedTracker_DETECTED_NOT_SHOWN_YET">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DetectionBasedTracker_DETECTED_TEMPORARY_LOST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DetectionBasedTracker_DETECTED_TEMPORARY_LOST</h4>
<pre>public static final&nbsp;int DetectionBasedTracker_DETECTED_TEMPORARY_LOST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DetectionBasedTracker_DETECTED_TEMPORARY_LOST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DetectionBasedTracker_WRONG_OBJECT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DetectionBasedTracker_WRONG_OBJECT</h4>
<pre>public static final&nbsp;int DetectionBasedTracker_WRONG_OBJECT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DetectionBasedTracker_WRONG_OBJECT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_4X4_100">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_4X4_100</h4>
<pre>public static final&nbsp;int DICT_4X4_100</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_4X4_100">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_4X4_1000">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_4X4_1000</h4>
<pre>public static final&nbsp;int DICT_4X4_1000</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_4X4_1000">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_4X4_250">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_4X4_250</h4>
<pre>public static final&nbsp;int DICT_4X4_250</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_4X4_250">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_4X4_50">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_4X4_50</h4>
<pre>public static final&nbsp;int DICT_4X4_50</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_4X4_50">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_5X5_100">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_5X5_100</h4>
<pre>public static final&nbsp;int DICT_5X5_100</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_5X5_100">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_5X5_1000">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_5X5_1000</h4>
<pre>public static final&nbsp;int DICT_5X5_1000</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_5X5_1000">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_5X5_250">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_5X5_250</h4>
<pre>public static final&nbsp;int DICT_5X5_250</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_5X5_250">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_5X5_50">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_5X5_50</h4>
<pre>public static final&nbsp;int DICT_5X5_50</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_5X5_50">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_6X6_100">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_6X6_100</h4>
<pre>public static final&nbsp;int DICT_6X6_100</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_6X6_100">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_6X6_1000">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_6X6_1000</h4>
<pre>public static final&nbsp;int DICT_6X6_1000</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_6X6_1000">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_6X6_250">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_6X6_250</h4>
<pre>public static final&nbsp;int DICT_6X6_250</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_6X6_250">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_6X6_50">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_6X6_50</h4>
<pre>public static final&nbsp;int DICT_6X6_50</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_6X6_50">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_7X7_100">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_7X7_100</h4>
<pre>public static final&nbsp;int DICT_7X7_100</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_7X7_100">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_7X7_1000">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_7X7_1000</h4>
<pre>public static final&nbsp;int DICT_7X7_1000</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_7X7_1000">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_7X7_250">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_7X7_250</h4>
<pre>public static final&nbsp;int DICT_7X7_250</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_7X7_250">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_7X7_50">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_7X7_50</h4>
<pre>public static final&nbsp;int DICT_7X7_50</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_7X7_50">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_APRILTAG_16h5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_APRILTAG_16h5</h4>
<pre>public static final&nbsp;int DICT_APRILTAG_16h5</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_APRILTAG_16h5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_APRILTAG_25h9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_APRILTAG_25h9</h4>
<pre>public static final&nbsp;int DICT_APRILTAG_25h9</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_APRILTAG_25h9">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_APRILTAG_36h10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_APRILTAG_36h10</h4>
<pre>public static final&nbsp;int DICT_APRILTAG_36h10</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_APRILTAG_36h10">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_APRILTAG_36h11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_APRILTAG_36h11</h4>
<pre>public static final&nbsp;int DICT_APRILTAG_36h11</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_APRILTAG_36h11">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_ARUCO_MIP_36h12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICT_ARUCO_MIP_36h12</h4>
<pre>public static final&nbsp;int DICT_ARUCO_MIP_36h12</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_ARUCO_MIP_36h12">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DICT_ARUCO_ORIGINAL">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DICT_ARUCO_ORIGINAL</h4>
<pre>public static final&nbsp;int DICT_ARUCO_ORIGINAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.Objdetect.DICT_ARUCO_ORIGINAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Objdetect--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Objdetect</h4>
<pre>public&nbsp;Objdetect()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="drawDetectedCornersCharuco-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawDetectedCornersCharuco</h4>
<pre>public static&nbsp;void&nbsp;drawDetectedCornersCharuco(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners)</pre>
<div class="block">Draws a set of Charuco corners</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input/output image. It must have 1 or 3 channels. The number of channels is not
 altered.</dd>
<dd><code>charucoCorners</code> - vector of detected charuco corners

 This function draws a set of detected Charuco corners. If identifiers vector is provided, it also
 draws the id of each corner.</dd>
</dl>
</li>
</ul>
<a name="drawDetectedCornersCharuco-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawDetectedCornersCharuco</h4>
<pre>public static&nbsp;void&nbsp;drawDetectedCornersCharuco(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
                                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds)</pre>
<div class="block">Draws a set of Charuco corners</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input/output image. It must have 1 or 3 channels. The number of channels is not
 altered.</dd>
<dd><code>charucoCorners</code> - vector of detected charuco corners</dd>
<dd><code>charucoIds</code> - list of identifiers for each corner in charucoCorners

 This function draws a set of detected Charuco corners. If identifiers vector is provided, it also
 draws the id of each corner.</dd>
</dl>
</li>
</ul>
<a name="drawDetectedCornersCharuco-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawDetectedCornersCharuco</h4>
<pre>public static&nbsp;void&nbsp;drawDetectedCornersCharuco(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoCorners,
                                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;charucoIds,
                                              <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;cornerColor)</pre>
<div class="block">Draws a set of Charuco corners</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input/output image. It must have 1 or 3 channels. The number of channels is not
 altered.</dd>
<dd><code>charucoCorners</code> - vector of detected charuco corners</dd>
<dd><code>charucoIds</code> - list of identifiers for each corner in charucoCorners</dd>
<dd><code>cornerColor</code> - color of the square surrounding each corner

 This function draws a set of detected Charuco corners. If identifiers vector is provided, it also
 draws the id of each corner.</dd>
</dl>
</li>
</ul>
<a name="drawDetectedDiamonds-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawDetectedDiamonds</h4>
<pre>public static&nbsp;void&nbsp;drawDetectedDiamonds(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                        java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners)</pre>
<div class="block">Draw a set of detected ChArUco Diamond markers</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input/output image. It must have 1 or 3 channels. The number of channels is not
 altered.</dd>
<dd><code>diamondCorners</code> - positions of diamond corners in the same format returned by
 detectCharucoDiamond(). (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers,
 the dimensions of this array should be Nx4. The order of the corners should be clockwise.
 returned by detectCharucoDiamond() (e.g. std::vector&lt;Vec4i&gt;).
 Optional, if not provided, ids are not painted.
 are calculated based on this one.

 Given an array of detected diamonds, this functions draws them in the image. The marker borders
 are painted and the markers identifiers if provided.
 Useful for debugging purposes.</dd>
</dl>
</li>
</ul>
<a name="drawDetectedDiamonds-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawDetectedDiamonds</h4>
<pre>public static&nbsp;void&nbsp;drawDetectedDiamonds(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                        java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds)</pre>
<div class="block">Draw a set of detected ChArUco Diamond markers</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input/output image. It must have 1 or 3 channels. The number of channels is not
 altered.</dd>
<dd><code>diamondCorners</code> - positions of diamond corners in the same format returned by
 detectCharucoDiamond(). (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers,
 the dimensions of this array should be Nx4. The order of the corners should be clockwise.</dd>
<dd><code>diamondIds</code> - vector of identifiers for diamonds in diamondCorners, in the same format
 returned by detectCharucoDiamond() (e.g. std::vector&lt;Vec4i&gt;).
 Optional, if not provided, ids are not painted.
 are calculated based on this one.

 Given an array of detected diamonds, this functions draws them in the image. The marker borders
 are painted and the markers identifiers if provided.
 Useful for debugging purposes.</dd>
</dl>
</li>
</ul>
<a name="drawDetectedDiamonds-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawDetectedDiamonds</h4>
<pre>public static&nbsp;void&nbsp;drawDetectedDiamonds(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                        java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;diamondCorners,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diamondIds,
                                        <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;borderColor)</pre>
<div class="block">Draw a set of detected ChArUco Diamond markers</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input/output image. It must have 1 or 3 channels. The number of channels is not
 altered.</dd>
<dd><code>diamondCorners</code> - positions of diamond corners in the same format returned by
 detectCharucoDiamond(). (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers,
 the dimensions of this array should be Nx4. The order of the corners should be clockwise.</dd>
<dd><code>diamondIds</code> - vector of identifiers for diamonds in diamondCorners, in the same format
 returned by detectCharucoDiamond() (e.g. std::vector&lt;Vec4i&gt;).
 Optional, if not provided, ids are not painted.</dd>
<dd><code>borderColor</code> - color of marker borders. Rest of colors (text color and first corner color)
 are calculated based on this one.

 Given an array of detected diamonds, this functions draws them in the image. The marker borders
 are painted and the markers identifiers if provided.
 Useful for debugging purposes.</dd>
</dl>
</li>
</ul>
<a name="drawDetectedMarkers-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawDetectedMarkers</h4>
<pre>public static&nbsp;void&nbsp;drawDetectedMarkers(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                       java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners)</pre>
<div class="block">Draw detected markers in image</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input/output image. It must have 1 or 3 channels. The number of channels is not altered.</dd>
<dd><code>corners</code> - positions of marker corners on input image.
 (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers, the dimensions of
 this array should be Nx4. The order of the corners should be clockwise.
 Optional, if not provided, ids are not painted.
 are calculated based on this one to improve visualization.

 Given an array of detected marker corners and its corresponding ids, this functions draws
 the markers in the image. The marker borders are painted and the markers identifiers if provided.
 Useful for debugging purposes.</dd>
</dl>
</li>
</ul>
<a name="drawDetectedMarkers-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawDetectedMarkers</h4>
<pre>public static&nbsp;void&nbsp;drawDetectedMarkers(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                       java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners,
                                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids)</pre>
<div class="block">Draw detected markers in image</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input/output image. It must have 1 or 3 channels. The number of channels is not altered.</dd>
<dd><code>corners</code> - positions of marker corners on input image.
 (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers, the dimensions of
 this array should be Nx4. The order of the corners should be clockwise.</dd>
<dd><code>ids</code> - vector of identifiers for markers in markersCorners .
 Optional, if not provided, ids are not painted.
 are calculated based on this one to improve visualization.

 Given an array of detected marker corners and its corresponding ids, this functions draws
 the markers in the image. The marker borders are painted and the markers identifiers if provided.
 Useful for debugging purposes.</dd>
</dl>
</li>
</ul>
<a name="drawDetectedMarkers-org.opencv.core.Mat-java.util.List-org.opencv.core.Mat-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawDetectedMarkers</h4>
<pre>public static&nbsp;void&nbsp;drawDetectedMarkers(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                       java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;corners,
                                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids,
                                       <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;borderColor)</pre>
<div class="block">Draw detected markers in image</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>image</code> - input/output image. It must have 1 or 3 channels. The number of channels is not altered.</dd>
<dd><code>corners</code> - positions of marker corners on input image.
 (e.g std::vector&lt;std::vector&lt;cv::Point2f&gt; &gt; ). For N detected markers, the dimensions of
 this array should be Nx4. The order of the corners should be clockwise.</dd>
<dd><code>ids</code> - vector of identifiers for markers in markersCorners .
 Optional, if not provided, ids are not painted.</dd>
<dd><code>borderColor</code> - color of marker borders. Rest of colors (text color and first corner color)
 are calculated based on this one to improve visualization.

 Given an array of detected marker corners and its corresponding ids, this functions draws
 the markers in the image. The marker borders are painted and the markers identifiers if provided.
 Useful for debugging purposes.</dd>
</dl>
</li>
</ul>
<a name="extendDictionary-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>extendDictionary</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;extendDictionary(int&nbsp;nMarkers,
                                          int&nbsp;markerSize)</pre>
<div class="block">Extend base dictionary by new nMarkers</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nMarkers</code> - number of markers in the dictionary</dd>
<dd><code>markerSize</code> - number of bits per dimension of each markers

 This function creates a new dictionary composed by nMarkers markers and each markers composed
 by markerSize x markerSize bits. If baseDictionary is provided, its markers are directly
 included and the rest are generated based on them. If the size of baseDictionary is higher
 than nMarkers, only the first nMarkers in baseDictionary are taken and no new marker is added.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="extendDictionary-int-int-org.opencv.objdetect.Dictionary-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>extendDictionary</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;extendDictionary(int&nbsp;nMarkers,
                                          int&nbsp;markerSize,
                                          <a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;baseDictionary)</pre>
<div class="block">Extend base dictionary by new nMarkers</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nMarkers</code> - number of markers in the dictionary</dd>
<dd><code>markerSize</code> - number of bits per dimension of each markers</dd>
<dd><code>baseDictionary</code> - Include the markers in this dictionary at the beginning (optional)

 This function creates a new dictionary composed by nMarkers markers and each markers composed
 by markerSize x markerSize bits. If baseDictionary is provided, its markers are directly
 included and the rest are generated based on them. If the size of baseDictionary is higher
 than nMarkers, only the first nMarkers in baseDictionary are taken and no new marker is added.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="extendDictionary-int-int-org.opencv.objdetect.Dictionary-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>extendDictionary</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;extendDictionary(int&nbsp;nMarkers,
                                          int&nbsp;markerSize,
                                          <a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;baseDictionary,
                                          int&nbsp;randomSeed)</pre>
<div class="block">Extend base dictionary by new nMarkers</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nMarkers</code> - number of markers in the dictionary</dd>
<dd><code>markerSize</code> - number of bits per dimension of each markers</dd>
<dd><code>baseDictionary</code> - Include the markers in this dictionary at the beginning (optional)</dd>
<dd><code>randomSeed</code> - a user supplied seed for theRNG()

 This function creates a new dictionary composed by nMarkers markers and each markers composed
 by markerSize x markerSize bits. If baseDictionary is provided, its markers are directly
 included and the rest are generated based on them. If the size of baseDictionary is higher
 than nMarkers, only the first nMarkers in baseDictionary are taken and no new marker is added.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="generateImageMarker-org.opencv.objdetect.Dictionary-int-int-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>generateImageMarker</h4>
<pre>public static&nbsp;void&nbsp;generateImageMarker(<a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
                                       int&nbsp;id,
                                       int&nbsp;sidePixels,
                                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</pre>
<div class="block">Generate a canonical marker image</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dictionary</code> - dictionary of markers indicating the type of markers</dd>
<dd><code>id</code> - identifier of the marker that will be returned. It has to be a valid id in the specified dictionary.</dd>
<dd><code>sidePixels</code> - size of the image in pixels</dd>
<dd><code>img</code> - output image with the marker

 This function returns a marker image in its canonical form (i.e. ready to be printed)</dd>
</dl>
</li>
</ul>
<a name="generateImageMarker-org.opencv.objdetect.Dictionary-int-int-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>generateImageMarker</h4>
<pre>public static&nbsp;void&nbsp;generateImageMarker(<a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
                                       int&nbsp;id,
                                       int&nbsp;sidePixels,
                                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                                       int&nbsp;borderBits)</pre>
<div class="block">Generate a canonical marker image</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dictionary</code> - dictionary of markers indicating the type of markers</dd>
<dd><code>id</code> - identifier of the marker that will be returned. It has to be a valid id in the specified dictionary.</dd>
<dd><code>sidePixels</code> - size of the image in pixels</dd>
<dd><code>img</code> - output image with the marker</dd>
<dd><code>borderBits</code> - width of the marker border.

 This function returns a marker image in its canonical form (i.e. ready to be printed)</dd>
</dl>
</li>
</ul>
<a name="getPredefinedDictionary-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPredefinedDictionary</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;getPredefinedDictionary(int&nbsp;dict)</pre>
<div class="block">Returns one of the predefined dictionaries referenced by DICT_*.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dict</code> - automatically generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="groupRectangles-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupRectangles</h4>
<pre>public static&nbsp;void&nbsp;groupRectangles(<a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;rectList,
                                   <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;weights,
                                   int&nbsp;groupThreshold)</pre>
</li>
</ul>
<a name="groupRectangles-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-int-double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>groupRectangles</h4>
<pre>public static&nbsp;void&nbsp;groupRectangles(<a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;rectList,
                                   <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;weights,
                                   int&nbsp;groupThreshold,
                                   double&nbsp;eps)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/HOGDescriptor.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/QRCodeDetector.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/Objdetect.html" target="_top">Frames</a></li>
<li><a href="Objdetect.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
