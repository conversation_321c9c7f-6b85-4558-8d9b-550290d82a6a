<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:22 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Board (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Board (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/BaseCascadeClassifier.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/CascadeClassifier.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/Board.html" target="_top">Frames</a></li>
<li><a href="Board.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.objdetect</div>
<h2 title="Class Board" class="title">Class Board</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.objdetect.Board</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../org/opencv/objdetect/CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a>, <a href="../../../org/opencv/objdetect/GridBoard.html" title="class in org.opencv.objdetect">GridBoard</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Board</span>
extends java.lang.Object</pre>
<div class="block">Board of ArUco markers

 A board is a set of markers in the 3D space with a common coordinate system.
 The common form of a board of marker is a planar (2D) board, however any 3D layout can be used.
 A Board object is composed by:
 - The object points of the marker corners, i.e. their coordinates respect to the board system.
 - The dictionary which indicates the type of markers of the board
 - The identifier of all the markers in the board.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Board.html#Board-java.util.List-org.opencv.objdetect.Dictionary-org.opencv.core.Mat-">Board</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objPoints,
     <a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids)</code>
<div class="block">Common Board constructor</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/Board.html" title="class in org.opencv.objdetect">Board</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Board.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Board.html#generateImage-org.opencv.core.Size-org.opencv.core.Mat-">generateImage</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;outSize,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</code>
<div class="block">Draw a planar board</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Board.html#generateImage-org.opencv.core.Size-org.opencv.core.Mat-int-">generateImage</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;outSize,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
             int&nbsp;marginSize)</code>
<div class="block">Draw a planar board</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Board.html#generateImage-org.opencv.core.Size-org.opencv.core.Mat-int-int-">generateImage</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;outSize,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
             int&nbsp;marginSize,
             int&nbsp;borderBits)</code>
<div class="block">Draw a planar board</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Board.html#getDictionary--">getDictionary</a></span>()</code>
<div class="block">return the Dictionary of markers employed for this board</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Board.html#getIds--">getIds</a></span>()</code>
<div class="block">vector of the identifiers of the markers in the board (should be the same size as objPoints)</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Board.html#getNativeObjAddr--">getNativeObjAddr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Board.html#getObjPoints--">getObjPoints</a></span>()</code>
<div class="block">return array of object points of all the marker corners in the board.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Point3.html" title="class in org.opencv.core">Point3</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Board.html#getRightBottomCorner--">getRightBottomCorner</a></span>()</code>
<div class="block">get coordinate of the bottom right corner of the board, is set when calling the function create()</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/Board.html#matchImagePoints-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">matchImagePoints</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;objPoints,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;imgPoints)</code>
<div class="block">Given a board configuration and a set of detected markers, returns the corresponding
 image points and object points, can be used in solvePnP()</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Board-java.util.List-org.opencv.objdetect.Dictionary-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Board</h4>
<pre>public&nbsp;Board(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objPoints,
             <a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;dictionary,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ids)</pre>
<div class="block">Common Board constructor</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>objPoints</code> - array of object points of all the marker corners in the board</dd>
<dd><code>dictionary</code> - the dictionary of markers employed for this board</dd>
<dd><code>ids</code> - vector of the identifiers of the markers in the board</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/Board.html" title="class in org.opencv.objdetect">Board</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="generateImage-org.opencv.core.Size-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>generateImage</h4>
<pre>public&nbsp;void&nbsp;generateImage(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;outSize,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</pre>
<div class="block">Draw a planar board</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>outSize</code> - size of the output image in pixels.</dd>
<dd><code>img</code> - output image with the board. The size of this image will be outSize
 and the board will be on the center, keeping the board proportions.

 This function return the image of the board, ready to be printed.</dd>
</dl>
</li>
</ul>
<a name="generateImage-org.opencv.core.Size-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>generateImage</h4>
<pre>public&nbsp;void&nbsp;generateImage(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;outSize,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                          int&nbsp;marginSize)</pre>
<div class="block">Draw a planar board</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>outSize</code> - size of the output image in pixels.</dd>
<dd><code>img</code> - output image with the board. The size of this image will be outSize
 and the board will be on the center, keeping the board proportions.</dd>
<dd><code>marginSize</code> - minimum margins (in pixels) of the board in the output image

 This function return the image of the board, ready to be printed.</dd>
</dl>
</li>
</ul>
<a name="generateImage-org.opencv.core.Size-org.opencv.core.Mat-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>generateImage</h4>
<pre>public&nbsp;void&nbsp;generateImage(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;outSize,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                          int&nbsp;marginSize,
                          int&nbsp;borderBits)</pre>
<div class="block">Draw a planar board</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>outSize</code> - size of the output image in pixels.</dd>
<dd><code>img</code> - output image with the board. The size of this image will be outSize
 and the board will be on the center, keeping the board proportions.</dd>
<dd><code>marginSize</code> - minimum margins (in pixels) of the board in the output image</dd>
<dd><code>borderBits</code> - width of the marker borders.

 This function return the image of the board, ready to be printed.</dd>
</dl>
</li>
</ul>
<a name="getDictionary--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDictionary</h4>
<pre>public&nbsp;<a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a>&nbsp;getDictionary()</pre>
<div class="block">return the Dictionary of markers employed for this board</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getIds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIds</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;getIds()</pre>
<div class="block">vector of the identifiers of the markers in the board (should be the same size as objPoints)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>vector of the identifiers of the markers</dd>
</dl>
</li>
</ul>
<a name="getNativeObjAddr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNativeObjAddr</h4>
<pre>public&nbsp;long&nbsp;getNativeObjAddr()</pre>
</li>
</ul>
<a name="getObjPoints--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getObjPoints</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&gt;&nbsp;getObjPoints()</pre>
<div class="block">return array of object points of all the marker corners in the board.

 Each marker include its 4 corners in this order:
 -   objPoints[i][0] - left-top point of i-th marker
 -   objPoints[i][1] - right-top point of i-th marker
 -   objPoints[i][2] - right-bottom point of i-th marker
 -   objPoints[i][3] - left-bottom point of i-th marker

 Markers are placed in a certain order - row by row, left to right in every row. For M markers, the size is Mx4.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getRightBottomCorner--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRightBottomCorner</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Point3.html" title="class in org.opencv.core">Point3</a>&nbsp;getRightBottomCorner()</pre>
<div class="block">get coordinate of the bottom right corner of the board, is set when calling the function create()</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="matchImagePoints-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>matchImagePoints</h4>
<pre>public&nbsp;void&nbsp;matchImagePoints(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;detectedCorners,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;detectedIds,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;objPoints,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;imgPoints)</pre>
<div class="block">Given a board configuration and a set of detected markers, returns the corresponding
 image points and object points, can be used in solvePnP()</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>detectedCorners</code> - List of detected marker corners of the board.
 For cv::Board and cv::GridBoard the method expects std::vector&lt;std::vector&lt;Point2f&gt;&gt; or std::vector&lt;Mat&gt; with Aruco marker corners.
 For cv::CharucoBoard the method expects std::vector&lt;Point2f&gt; or Mat with ChAruco corners (chess board corners matched with Aruco markers).</dd>
<dd><code>detectedIds</code> - List of identifiers for each marker or charuco corner.
 For any Board class the method expects std::vector&lt;int&gt; or Mat.</dd>
<dd><code>objPoints</code> - Vector of marker points in the board coordinate space.
 For any Board class the method expects std::vector&lt;cv::Point3f&gt; objectPoints or cv::Mat</dd>
<dd><code>imgPoints</code> - Vector of marker points in the image coordinate space.
 For any Board class the method expects std::vector&lt;cv::Point2f&gt; objectPoints or cv::Mat

 SEE: solvePnP</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/BaseCascadeClassifier.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/CascadeClassifier.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/Board.html" target="_top">Frames</a></li>
<li><a href="Board.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
