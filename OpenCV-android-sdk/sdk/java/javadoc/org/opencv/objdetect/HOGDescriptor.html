<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:23 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>HOGDescriptor (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HOGDescriptor (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":9,"i35":9,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/GridBoard.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/Objdetect.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/HOGDescriptor.html" target="_top">Frames</a></li>
<li><a href="HOGDescriptor.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.objdetect</div>
<h2 title="Class HOGDescriptor" class="title">Class HOGDescriptor</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.objdetect.HOGDescriptor</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">HOGDescriptor</span>
extends java.lang.Object</pre>
<div class="block">Implementation of HOG (Histogram of Oriented Gradients) descriptor and object detector.

 the HOG descriptor algorithm introduced by Navneet Dalal and Bill Triggs CITE: Dalal2005 .

 useful links:

 https://hal.inria.fr/inria-00548512/document/

 https://en.wikipedia.org/wiki/Histogram_of_oriented_gradients

 https://software.intel.com/en-us/ipp-dev-reference-histogram-of-oriented-gradients-hog-descriptor

 http://www.learnopencv.com/histogram-of-oriented-gradients

 http://www.learnopencv.com/handwritten-digits-classification-an-opencv-c-python-tutorial</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#DEFAULT_NLEVELS">DEFAULT_NLEVELS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#DESCR_FORMAT_COL_BY_COL">DESCR_FORMAT_COL_BY_COL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#DESCR_FORMAT_ROW_BY_ROW">DESCR_FORMAT_ROW_BY_ROW</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#L2Hys">L2Hys</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#HOGDescriptor--">HOGDescriptor</a></span>()</code>
<div class="block">Creates the HOG descriptor and detector with default parameters.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#HOGDescriptor-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-int-">HOGDescriptor</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
             int&nbsp;_nbins)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#HOGDescriptor-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-int-int-">HOGDescriptor</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
             int&nbsp;_nbins,
             int&nbsp;_derivAperture)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#HOGDescriptor-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-int-int-double-">HOGDescriptor</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
             int&nbsp;_nbins,
             int&nbsp;_derivAperture,
             double&nbsp;_winSigma)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#HOGDescriptor-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-int-int-double-int-">HOGDescriptor</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
             int&nbsp;_nbins,
             int&nbsp;_derivAperture,
             double&nbsp;_winSigma,
             int&nbsp;_histogramNormType)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#HOGDescriptor-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-int-int-double-int-double-">HOGDescriptor</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
             int&nbsp;_nbins,
             int&nbsp;_derivAperture,
             double&nbsp;_winSigma,
             int&nbsp;_histogramNormType,
             double&nbsp;_L2HysThreshold)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#HOGDescriptor-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-int-int-double-int-double-boolean-">HOGDescriptor</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
             int&nbsp;_nbins,
             int&nbsp;_derivAperture,
             double&nbsp;_winSigma,
             int&nbsp;_histogramNormType,
             double&nbsp;_L2HysThreshold,
             boolean&nbsp;_gammaCorrection)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#HOGDescriptor-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-int-int-double-int-double-boolean-int-">HOGDescriptor</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
             int&nbsp;_nbins,
             int&nbsp;_derivAperture,
             double&nbsp;_winSigma,
             int&nbsp;_histogramNormType,
             double&nbsp;_L2HysThreshold,
             boolean&nbsp;_gammaCorrection,
             int&nbsp;_nlevels)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#HOGDescriptor-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-int-int-double-int-double-boolean-int-boolean-">HOGDescriptor</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
             int&nbsp;_nbins,
             int&nbsp;_derivAperture,
             double&nbsp;_winSigma,
             int&nbsp;_histogramNormType,
             double&nbsp;_L2HysThreshold,
             boolean&nbsp;_gammaCorrection,
             int&nbsp;_nlevels,
             boolean&nbsp;_signedGradient)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#HOGDescriptor-java.lang.String-">HOGDescriptor</a></span>(java.lang.String&nbsp;filename)</code>
<div class="block">Creates the HOG descriptor and detector and loads HOGDescriptor parameters and coefficients for the linear SVM classifier from a file.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/HOGDescriptor.html" title="class in org.opencv.objdetect">HOGDescriptor</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#checkDetectorSize--">checkDetectorSize</a></span>()</code>
<div class="block">Checks if detector size equal to descriptor size.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#compute-org.opencv.core.Mat-org.opencv.core.MatOfFloat-">compute</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
       <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;descriptors)</code>
<div class="block">Computes HOG descriptors of given image.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#compute-org.opencv.core.Mat-org.opencv.core.MatOfFloat-org.opencv.core.Size-">compute</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
       <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;descriptors,
       <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride)</code>
<div class="block">Computes HOG descriptors of given image.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#compute-org.opencv.core.Mat-org.opencv.core.MatOfFloat-org.opencv.core.Size-org.opencv.core.Size-">compute</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
       <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;descriptors,
       <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
       <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding)</code>
<div class="block">Computes HOG descriptors of given image.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#compute-org.opencv.core.Mat-org.opencv.core.MatOfFloat-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.MatOfPoint-">compute</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
       <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;descriptors,
       <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
       <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
       <a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;locations)</code>
<div class="block">Computes HOG descriptors of given image.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#computeGradient-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">computeGradient</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;grad,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angleOfs)</code>
<div class="block">Computes gradients and quantized gradient orientations.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#computeGradient-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-">computeGradient</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;grad,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angleOfs,
               <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;paddingTL)</code>
<div class="block">Computes gradients and quantized gradient orientations.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#computeGradient-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Size-">computeGradient</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;grad,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angleOfs,
               <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;paddingTL,
               <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;paddingBR)</code>
<div class="block">Computes gradients and quantized gradient orientations.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfPoint-org.opencv.core.MatOfDouble-">detect</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
      <a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
      <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights)</code>
<div class="block">Performs object detection without a multi-scale window.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfPoint-org.opencv.core.MatOfDouble-double-">detect</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
      <a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
      <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights,
      double&nbsp;hitThreshold)</code>
<div class="block">Performs object detection without a multi-scale window.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfPoint-org.opencv.core.MatOfDouble-double-org.opencv.core.Size-">detect</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
      <a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
      <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights,
      double&nbsp;hitThreshold,
      <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride)</code>
<div class="block">Performs object detection without a multi-scale window.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfPoint-org.opencv.core.MatOfDouble-double-org.opencv.core.Size-org.opencv.core.Size-">detect</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
      <a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
      <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights,
      double&nbsp;hitThreshold,
      <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
      <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding)</code>
<div class="block">Performs object detection without a multi-scale window.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfPoint-org.opencv.core.MatOfDouble-double-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.MatOfPoint-">detect</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
      <a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
      <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights,
      double&nbsp;hitThreshold,
      <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
      <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
      <a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;searchLocations)</code>
<div class="block">Performs object detection without a multi-scale window.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfDouble-">detectMultiScale</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
                <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights)</code>
<div class="block">Detects objects of different sizes in the input image.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfDouble-double-">detectMultiScale</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
                <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
                double&nbsp;hitThreshold)</code>
<div class="block">Detects objects of different sizes in the input image.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfDouble-double-org.opencv.core.Size-">detectMultiScale</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
                <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
                double&nbsp;hitThreshold,
                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride)</code>
<div class="block">Detects objects of different sizes in the input image.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfDouble-double-org.opencv.core.Size-org.opencv.core.Size-">detectMultiScale</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
                <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
                double&nbsp;hitThreshold,
                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding)</code>
<div class="block">Detects objects of different sizes in the input image.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfDouble-double-org.opencv.core.Size-org.opencv.core.Size-double-">detectMultiScale</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
                <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
                double&nbsp;hitThreshold,
                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
                double&nbsp;scale)</code>
<div class="block">Detects objects of different sizes in the input image.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfDouble-double-org.opencv.core.Size-org.opencv.core.Size-double-double-">detectMultiScale</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
                <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
                double&nbsp;hitThreshold,
                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
                double&nbsp;scale,
                double&nbsp;groupThreshold)</code>
<div class="block">Detects objects of different sizes in the input image.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfDouble-double-org.opencv.core.Size-org.opencv.core.Size-double-double-boolean-">detectMultiScale</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
                <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
                double&nbsp;hitThreshold,
                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
                double&nbsp;scale,
                double&nbsp;groupThreshold,
                boolean&nbsp;useMeanshiftGrouping)</code>
<div class="block">Detects objects of different sizes in the input image.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#get_blockSize--">get_blockSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#get_blockStride--">get_blockStride</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#get_cellSize--">get_cellSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#get_derivAperture--">get_derivAperture</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#get_gammaCorrection--">get_gammaCorrection</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#get_histogramNormType--">get_histogramNormType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#get_L2HysThreshold--">get_L2HysThreshold</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#get_nbins--">get_nbins</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#get_nlevels--">get_nlevels</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#get_signedGradient--">get_signedGradient</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#get_svmDetector--">get_svmDetector</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#get_winSigma--">get_winSigma</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#get_winSize--">get_winSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#getDaimlerPeopleDetector--">getDaimlerPeopleDetector</a></span>()</code>
<div class="block">Returns coefficients of the classifier trained for people detection (for 48x96 windows).</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#getDefaultPeopleDetector--">getDefaultPeopleDetector</a></span>()</code>
<div class="block">Returns coefficients of the classifier trained for people detection (for 64x128 windows).</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#getDescriptorSize--">getDescriptorSize</a></span>()</code>
<div class="block">Returns the number of coefficients required for the classification.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#getNativeObjAddr--">getNativeObjAddr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#getWinSigma--">getWinSigma</a></span>()</code>
<div class="block">Returns winSigma value</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#load-java.lang.String-">load</a></span>(java.lang.String&nbsp;filename)</code>
<div class="block">loads HOGDescriptor parameters and coefficients for the linear SVM classifier from a file</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#load-java.lang.String-java.lang.String-">load</a></span>(java.lang.String&nbsp;filename,
    java.lang.String&nbsp;objname)</code>
<div class="block">loads HOGDescriptor parameters and coefficients for the linear SVM classifier from a file</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#save-java.lang.String-">save</a></span>(java.lang.String&nbsp;filename)</code>
<div class="block">saves HOGDescriptor parameters and coefficients for the linear SVM classifier to a file</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#save-java.lang.String-java.lang.String-">save</a></span>(java.lang.String&nbsp;filename,
    java.lang.String&nbsp;objname)</code>
<div class="block">saves HOGDescriptor parameters and coefficients for the linear SVM classifier to a file</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/HOGDescriptor.html#setSVMDetector-org.opencv.core.Mat-">setSVMDetector</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;svmdetector)</code>
<div class="block">Sets coefficients for the linear SVM classifier.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="DEFAULT_NLEVELS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEFAULT_NLEVELS</h4>
<pre>public static final&nbsp;int DEFAULT_NLEVELS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.HOGDescriptor.DEFAULT_NLEVELS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DESCR_FORMAT_COL_BY_COL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESCR_FORMAT_COL_BY_COL</h4>
<pre>public static final&nbsp;int DESCR_FORMAT_COL_BY_COL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.HOGDescriptor.DESCR_FORMAT_COL_BY_COL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DESCR_FORMAT_ROW_BY_ROW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESCR_FORMAT_ROW_BY_ROW</h4>
<pre>public static final&nbsp;int DESCR_FORMAT_ROW_BY_ROW</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.HOGDescriptor.DESCR_FORMAT_ROW_BY_ROW">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="L2Hys">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>L2Hys</h4>
<pre>public static final&nbsp;int L2Hys</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.objdetect.HOGDescriptor.L2Hys">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HOGDescriptor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HOGDescriptor</h4>
<pre>public&nbsp;HOGDescriptor()</pre>
<div class="block">Creates the HOG descriptor and detector with default parameters.

     aqual to HOGDescriptor(Size(64,128), Size(16,16), Size(8,8), Size(8,8), 9 )</div>
</li>
</ul>
<a name="HOGDescriptor-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HOGDescriptor</h4>
<pre>public&nbsp;HOGDescriptor(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
                     int&nbsp;_nbins)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>_winSize</code> - sets winSize with given value.</dd>
<dd><code>_blockSize</code> - sets blockSize with given value.</dd>
<dd><code>_blockStride</code> - sets blockStride with given value.</dd>
<dd><code>_cellSize</code> - sets cellSize with given value.</dd>
<dd><code>_nbins</code> - sets nbins with given value.</dd>
</dl>
</li>
</ul>
<a name="HOGDescriptor-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HOGDescriptor</h4>
<pre>public&nbsp;HOGDescriptor(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
                     int&nbsp;_nbins,
                     int&nbsp;_derivAperture)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>_winSize</code> - sets winSize with given value.</dd>
<dd><code>_blockSize</code> - sets blockSize with given value.</dd>
<dd><code>_blockStride</code> - sets blockStride with given value.</dd>
<dd><code>_cellSize</code> - sets cellSize with given value.</dd>
<dd><code>_nbins</code> - sets nbins with given value.</dd>
<dd><code>_derivAperture</code> - sets derivAperture with given value.</dd>
</dl>
</li>
</ul>
<a name="HOGDescriptor-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-int-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HOGDescriptor</h4>
<pre>public&nbsp;HOGDescriptor(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
                     int&nbsp;_nbins,
                     int&nbsp;_derivAperture,
                     double&nbsp;_winSigma)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>_winSize</code> - sets winSize with given value.</dd>
<dd><code>_blockSize</code> - sets blockSize with given value.</dd>
<dd><code>_blockStride</code> - sets blockStride with given value.</dd>
<dd><code>_cellSize</code> - sets cellSize with given value.</dd>
<dd><code>_nbins</code> - sets nbins with given value.</dd>
<dd><code>_derivAperture</code> - sets derivAperture with given value.</dd>
<dd><code>_winSigma</code> - sets winSigma with given value.</dd>
</dl>
</li>
</ul>
<a name="HOGDescriptor-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-int-int-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HOGDescriptor</h4>
<pre>public&nbsp;HOGDescriptor(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
                     int&nbsp;_nbins,
                     int&nbsp;_derivAperture,
                     double&nbsp;_winSigma,
                     int&nbsp;_histogramNormType)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>_winSize</code> - sets winSize with given value.</dd>
<dd><code>_blockSize</code> - sets blockSize with given value.</dd>
<dd><code>_blockStride</code> - sets blockStride with given value.</dd>
<dd><code>_cellSize</code> - sets cellSize with given value.</dd>
<dd><code>_nbins</code> - sets nbins with given value.</dd>
<dd><code>_derivAperture</code> - sets derivAperture with given value.</dd>
<dd><code>_winSigma</code> - sets winSigma with given value.</dd>
<dd><code>_histogramNormType</code> - sets histogramNormType with given value.</dd>
</dl>
</li>
</ul>
<a name="HOGDescriptor-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-int-int-double-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HOGDescriptor</h4>
<pre>public&nbsp;HOGDescriptor(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
                     int&nbsp;_nbins,
                     int&nbsp;_derivAperture,
                     double&nbsp;_winSigma,
                     int&nbsp;_histogramNormType,
                     double&nbsp;_L2HysThreshold)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>_winSize</code> - sets winSize with given value.</dd>
<dd><code>_blockSize</code> - sets blockSize with given value.</dd>
<dd><code>_blockStride</code> - sets blockStride with given value.</dd>
<dd><code>_cellSize</code> - sets cellSize with given value.</dd>
<dd><code>_nbins</code> - sets nbins with given value.</dd>
<dd><code>_derivAperture</code> - sets derivAperture with given value.</dd>
<dd><code>_winSigma</code> - sets winSigma with given value.</dd>
<dd><code>_histogramNormType</code> - sets histogramNormType with given value.</dd>
<dd><code>_L2HysThreshold</code> - sets L2HysThreshold with given value.</dd>
</dl>
</li>
</ul>
<a name="HOGDescriptor-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-int-int-double-int-double-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HOGDescriptor</h4>
<pre>public&nbsp;HOGDescriptor(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
                     int&nbsp;_nbins,
                     int&nbsp;_derivAperture,
                     double&nbsp;_winSigma,
                     int&nbsp;_histogramNormType,
                     double&nbsp;_L2HysThreshold,
                     boolean&nbsp;_gammaCorrection)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>_winSize</code> - sets winSize with given value.</dd>
<dd><code>_blockSize</code> - sets blockSize with given value.</dd>
<dd><code>_blockStride</code> - sets blockStride with given value.</dd>
<dd><code>_cellSize</code> - sets cellSize with given value.</dd>
<dd><code>_nbins</code> - sets nbins with given value.</dd>
<dd><code>_derivAperture</code> - sets derivAperture with given value.</dd>
<dd><code>_winSigma</code> - sets winSigma with given value.</dd>
<dd><code>_histogramNormType</code> - sets histogramNormType with given value.</dd>
<dd><code>_L2HysThreshold</code> - sets L2HysThreshold with given value.</dd>
<dd><code>_gammaCorrection</code> - sets gammaCorrection with given value.</dd>
</dl>
</li>
</ul>
<a name="HOGDescriptor-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-int-int-double-int-double-boolean-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HOGDescriptor</h4>
<pre>public&nbsp;HOGDescriptor(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
                     int&nbsp;_nbins,
                     int&nbsp;_derivAperture,
                     double&nbsp;_winSigma,
                     int&nbsp;_histogramNormType,
                     double&nbsp;_L2HysThreshold,
                     boolean&nbsp;_gammaCorrection,
                     int&nbsp;_nlevels)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>_winSize</code> - sets winSize with given value.</dd>
<dd><code>_blockSize</code> - sets blockSize with given value.</dd>
<dd><code>_blockStride</code> - sets blockStride with given value.</dd>
<dd><code>_cellSize</code> - sets cellSize with given value.</dd>
<dd><code>_nbins</code> - sets nbins with given value.</dd>
<dd><code>_derivAperture</code> - sets derivAperture with given value.</dd>
<dd><code>_winSigma</code> - sets winSigma with given value.</dd>
<dd><code>_histogramNormType</code> - sets histogramNormType with given value.</dd>
<dd><code>_L2HysThreshold</code> - sets L2HysThreshold with given value.</dd>
<dd><code>_gammaCorrection</code> - sets gammaCorrection with given value.</dd>
<dd><code>_nlevels</code> - sets nlevels with given value.</dd>
</dl>
</li>
</ul>
<a name="HOGDescriptor-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.Size-int-int-double-int-double-boolean-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HOGDescriptor</h4>
<pre>public&nbsp;HOGDescriptor(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_winSize,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockSize,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_blockStride,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;_cellSize,
                     int&nbsp;_nbins,
                     int&nbsp;_derivAperture,
                     double&nbsp;_winSigma,
                     int&nbsp;_histogramNormType,
                     double&nbsp;_L2HysThreshold,
                     boolean&nbsp;_gammaCorrection,
                     int&nbsp;_nlevels,
                     boolean&nbsp;_signedGradient)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>_winSize</code> - sets winSize with given value.</dd>
<dd><code>_blockSize</code> - sets blockSize with given value.</dd>
<dd><code>_blockStride</code> - sets blockStride with given value.</dd>
<dd><code>_cellSize</code> - sets cellSize with given value.</dd>
<dd><code>_nbins</code> - sets nbins with given value.</dd>
<dd><code>_derivAperture</code> - sets derivAperture with given value.</dd>
<dd><code>_winSigma</code> - sets winSigma with given value.</dd>
<dd><code>_histogramNormType</code> - sets histogramNormType with given value.</dd>
<dd><code>_L2HysThreshold</code> - sets L2HysThreshold with given value.</dd>
<dd><code>_gammaCorrection</code> - sets gammaCorrection with given value.</dd>
<dd><code>_nlevels</code> - sets nlevels with given value.</dd>
<dd><code>_signedGradient</code> - sets signedGradient with given value.</dd>
</dl>
</li>
</ul>
<a name="HOGDescriptor-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HOGDescriptor</h4>
<pre>public&nbsp;HOGDescriptor(java.lang.String&nbsp;filename)</pre>
<div class="block">Creates the HOG descriptor and detector and loads HOGDescriptor parameters and coefficients for the linear SVM classifier from a file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - The file name containing HOGDescriptor properties and coefficients for the linear SVM classifier.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/HOGDescriptor.html" title="class in org.opencv.objdetect">HOGDescriptor</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="checkDetectorSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkDetectorSize</h4>
<pre>public&nbsp;boolean&nbsp;checkDetectorSize()</pre>
<div class="block">Checks if detector size equal to descriptor size.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="compute-org.opencv.core.Mat-org.opencv.core.MatOfFloat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compute</h4>
<pre>public&nbsp;void&nbsp;compute(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                    <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;descriptors)</pre>
<div class="block">Computes HOG descriptors of given image.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - Matrix of the type CV_8U containing an image where HOG features will be calculated.</dd>
<dd><code>descriptors</code> - Matrix of the type CV_32F</dd>
</dl>
</li>
</ul>
<a name="compute-org.opencv.core.Mat-org.opencv.core.MatOfFloat-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compute</h4>
<pre>public&nbsp;void&nbsp;compute(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                    <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;descriptors,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride)</pre>
<div class="block">Computes HOG descriptors of given image.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - Matrix of the type CV_8U containing an image where HOG features will be calculated.</dd>
<dd><code>descriptors</code> - Matrix of the type CV_32F</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
</dl>
</li>
</ul>
<a name="compute-org.opencv.core.Mat-org.opencv.core.MatOfFloat-org.opencv.core.Size-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compute</h4>
<pre>public&nbsp;void&nbsp;compute(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                    <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;descriptors,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding)</pre>
<div class="block">Computes HOG descriptors of given image.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - Matrix of the type CV_8U containing an image where HOG features will be calculated.</dd>
<dd><code>descriptors</code> - Matrix of the type CV_32F</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
<dd><code>padding</code> - Padding</dd>
</dl>
</li>
</ul>
<a name="compute-org.opencv.core.Mat-org.opencv.core.MatOfFloat-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.MatOfPoint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compute</h4>
<pre>public&nbsp;void&nbsp;compute(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                    <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;descriptors,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
                    <a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;locations)</pre>
<div class="block">Computes HOG descriptors of given image.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - Matrix of the type CV_8U containing an image where HOG features will be calculated.</dd>
<dd><code>descriptors</code> - Matrix of the type CV_32F</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
<dd><code>padding</code> - Padding</dd>
<dd><code>locations</code> - Vector of Point</dd>
</dl>
</li>
</ul>
<a name="computeGradient-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>computeGradient</h4>
<pre>public&nbsp;void&nbsp;computeGradient(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;grad,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angleOfs)</pre>
<div class="block">Computes gradients and quantized gradient orientations.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - Matrix contains the image to be computed</dd>
<dd><code>grad</code> - Matrix of type CV_32FC2 contains computed gradients</dd>
<dd><code>angleOfs</code> - Matrix of type CV_8UC2 contains quantized gradient orientations</dd>
</dl>
</li>
</ul>
<a name="computeGradient-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>computeGradient</h4>
<pre>public&nbsp;void&nbsp;computeGradient(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;grad,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angleOfs,
                            <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;paddingTL)</pre>
<div class="block">Computes gradients and quantized gradient orientations.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - Matrix contains the image to be computed</dd>
<dd><code>grad</code> - Matrix of type CV_32FC2 contains computed gradients</dd>
<dd><code>angleOfs</code> - Matrix of type CV_8UC2 contains quantized gradient orientations</dd>
<dd><code>paddingTL</code> - Padding from top-left</dd>
</dl>
</li>
</ul>
<a name="computeGradient-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>computeGradient</h4>
<pre>public&nbsp;void&nbsp;computeGradient(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;grad,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angleOfs,
                            <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;paddingTL,
                            <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;paddingBR)</pre>
<div class="block">Computes gradients and quantized gradient orientations.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - Matrix contains the image to be computed</dd>
<dd><code>grad</code> - Matrix of type CV_32FC2 contains computed gradients</dd>
<dd><code>angleOfs</code> - Matrix of type CV_8UC2 contains quantized gradient orientations</dd>
<dd><code>paddingTL</code> - Padding from top-left</dd>
<dd><code>paddingBR</code> - Padding from bottom-right</dd>
</dl>
</li>
</ul>
<a name="detect-org.opencv.core.Mat-org.opencv.core.MatOfPoint-org.opencv.core.MatOfDouble-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detect</h4>
<pre>public&nbsp;void&nbsp;detect(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                   <a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
                   <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights)</pre>
<div class="block">Performs object detection without a multi-scale window.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of point where each point contains left-top corner point of detected object boundaries.</dd>
<dd><code>weights</code> - Vector that will contain confidence values for each detected object.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
</dl>
</li>
</ul>
<a name="detect-org.opencv.core.Mat-org.opencv.core.MatOfPoint-org.opencv.core.MatOfDouble-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detect</h4>
<pre>public&nbsp;void&nbsp;detect(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                   <a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
                   <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights,
                   double&nbsp;hitThreshold)</pre>
<div class="block">Performs object detection without a multi-scale window.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of point where each point contains left-top corner point of detected object boundaries.</dd>
<dd><code>weights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
</dl>
</li>
</ul>
<a name="detect-org.opencv.core.Mat-org.opencv.core.MatOfPoint-org.opencv.core.MatOfDouble-double-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detect</h4>
<pre>public&nbsp;void&nbsp;detect(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                   <a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
                   <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights,
                   double&nbsp;hitThreshold,
                   <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride)</pre>
<div class="block">Performs object detection without a multi-scale window.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of point where each point contains left-top corner point of detected object boundaries.</dd>
<dd><code>weights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
</dl>
</li>
</ul>
<a name="detect-org.opencv.core.Mat-org.opencv.core.MatOfPoint-org.opencv.core.MatOfDouble-double-org.opencv.core.Size-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detect</h4>
<pre>public&nbsp;void&nbsp;detect(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                   <a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
                   <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights,
                   double&nbsp;hitThreshold,
                   <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
                   <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding)</pre>
<div class="block">Performs object detection without a multi-scale window.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of point where each point contains left-top corner point of detected object boundaries.</dd>
<dd><code>weights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
<dd><code>padding</code> - Padding</dd>
</dl>
</li>
</ul>
<a name="detect-org.opencv.core.Mat-org.opencv.core.MatOfPoint-org.opencv.core.MatOfDouble-double-org.opencv.core.Size-org.opencv.core.Size-org.opencv.core.MatOfPoint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detect</h4>
<pre>public&nbsp;void&nbsp;detect(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                   <a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;foundLocations,
                   <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;weights,
                   double&nbsp;hitThreshold,
                   <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
                   <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
                   <a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>&nbsp;searchLocations)</pre>
<div class="block">Performs object detection without a multi-scale window.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of point where each point contains left-top corner point of detected object boundaries.</dd>
<dd><code>weights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
<dd><code>padding</code> - Padding</dd>
<dd><code>searchLocations</code> - Vector of Point includes set of requested locations to be evaluated.</dd>
</dl>
</li>
</ul>
<a name="detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfDouble-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                             <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
                             <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights)</pre>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of rectangles where each rectangle contains the detected object.</dd>
<dd><code>foundWeights</code> - Vector that will contain confidence values for each detected object.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.
     by many rectangles. 0 means not to perform grouping.</dd>
</dl>
</li>
</ul>
<a name="detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfDouble-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                             <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
                             <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
                             double&nbsp;hitThreshold)</pre>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of rectangles where each rectangle contains the detected object.</dd>
<dd><code>foundWeights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.
     by many rectangles. 0 means not to perform grouping.</dd>
</dl>
</li>
</ul>
<a name="detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfDouble-double-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                             <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
                             <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
                             double&nbsp;hitThreshold,
                             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride)</pre>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of rectangles where each rectangle contains the detected object.</dd>
<dd><code>foundWeights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.
     by many rectangles. 0 means not to perform grouping.</dd>
</dl>
</li>
</ul>
<a name="detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfDouble-double-org.opencv.core.Size-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                             <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
                             <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
                             double&nbsp;hitThreshold,
                             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
                             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding)</pre>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of rectangles where each rectangle contains the detected object.</dd>
<dd><code>foundWeights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
<dd><code>padding</code> - Padding
     by many rectangles. 0 means not to perform grouping.</dd>
</dl>
</li>
</ul>
<a name="detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfDouble-double-org.opencv.core.Size-org.opencv.core.Size-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                             <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
                             <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
                             double&nbsp;hitThreshold,
                             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
                             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
                             double&nbsp;scale)</pre>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of rectangles where each rectangle contains the detected object.</dd>
<dd><code>foundWeights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
<dd><code>padding</code> - Padding</dd>
<dd><code>scale</code> - Coefficient of the detection window increase.
     by many rectangles. 0 means not to perform grouping.</dd>
</dl>
</li>
</ul>
<a name="detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfDouble-double-org.opencv.core.Size-org.opencv.core.Size-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                             <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
                             <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
                             double&nbsp;hitThreshold,
                             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
                             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
                             double&nbsp;scale,
                             double&nbsp;groupThreshold)</pre>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of rectangles where each rectangle contains the detected object.</dd>
<dd><code>foundWeights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
<dd><code>padding</code> - Padding</dd>
<dd><code>scale</code> - Coefficient of the detection window increase.</dd>
<dd><code>groupThreshold</code> - Coefficient to regulate the similarity threshold. When detected, some objects can be covered
     by many rectangles. 0 means not to perform grouping.</dd>
</dl>
</li>
</ul>
<a name="detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfDouble-double-org.opencv.core.Size-org.opencv.core.Size-double-double-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                             <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;foundLocations,
                             <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;foundWeights,
                             double&nbsp;hitThreshold,
                             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winStride,
                             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;padding,
                             double&nbsp;scale,
                             double&nbsp;groupThreshold,
                             boolean&nbsp;useMeanshiftGrouping)</pre>
<div class="block">Detects objects of different sizes in the input image. The detected objects are returned as a list
     of rectangles.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>img</code> - Matrix of the type CV_8U or CV_8UC3 containing an image where objects are detected.</dd>
<dd><code>foundLocations</code> - Vector of rectangles where each rectangle contains the detected object.</dd>
<dd><code>foundWeights</code> - Vector that will contain confidence values for each detected object.</dd>
<dd><code>hitThreshold</code> - Threshold for the distance between features and SVM classifying plane.
     Usually it is 0 and should be specified in the detector coefficients (as the last free coefficient).
     But if the free coefficient is omitted (which is allowed), you can specify it manually here.</dd>
<dd><code>winStride</code> - Window stride. It must be a multiple of block stride.</dd>
<dd><code>padding</code> - Padding</dd>
<dd><code>scale</code> - Coefficient of the detection window increase.</dd>
<dd><code>groupThreshold</code> - Coefficient to regulate the similarity threshold. When detected, some objects can be covered
     by many rectangles. 0 means not to perform grouping.</dd>
<dd><code>useMeanshiftGrouping</code> - indicates grouping algorithm</dd>
</dl>
</li>
</ul>
<a name="get_blockSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_blockSize</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;get_blockSize()</pre>
</li>
</ul>
<a name="get_blockStride--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_blockStride</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;get_blockStride()</pre>
</li>
</ul>
<a name="get_cellSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_cellSize</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;get_cellSize()</pre>
</li>
</ul>
<a name="get_derivAperture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_derivAperture</h4>
<pre>public&nbsp;int&nbsp;get_derivAperture()</pre>
</li>
</ul>
<a name="get_gammaCorrection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_gammaCorrection</h4>
<pre>public&nbsp;boolean&nbsp;get_gammaCorrection()</pre>
</li>
</ul>
<a name="get_histogramNormType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_histogramNormType</h4>
<pre>public&nbsp;int&nbsp;get_histogramNormType()</pre>
</li>
</ul>
<a name="get_L2HysThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_L2HysThreshold</h4>
<pre>public&nbsp;double&nbsp;get_L2HysThreshold()</pre>
</li>
</ul>
<a name="get_nbins--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_nbins</h4>
<pre>public&nbsp;int&nbsp;get_nbins()</pre>
</li>
</ul>
<a name="get_nlevels--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_nlevels</h4>
<pre>public&nbsp;int&nbsp;get_nlevels()</pre>
</li>
</ul>
<a name="get_signedGradient--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_signedGradient</h4>
<pre>public&nbsp;boolean&nbsp;get_signedGradient()</pre>
</li>
</ul>
<a name="get_svmDetector--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_svmDetector</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;get_svmDetector()</pre>
</li>
</ul>
<a name="get_winSigma--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_winSigma</h4>
<pre>public&nbsp;double&nbsp;get_winSigma()</pre>
</li>
</ul>
<a name="get_winSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_winSize</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;get_winSize()</pre>
</li>
</ul>
<a name="getDaimlerPeopleDetector--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDaimlerPeopleDetector</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;getDaimlerPeopleDetector()</pre>
<div class="block">Returns coefficients of the classifier trained for people detection (for 48x96 windows).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getDefaultPeopleDetector--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultPeopleDetector</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;getDefaultPeopleDetector()</pre>
<div class="block">Returns coefficients of the classifier trained for people detection (for 64x128 windows).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getDescriptorSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDescriptorSize</h4>
<pre>public&nbsp;long&nbsp;getDescriptorSize()</pre>
<div class="block">Returns the number of coefficients required for the classification.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getNativeObjAddr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNativeObjAddr</h4>
<pre>public&nbsp;long&nbsp;getNativeObjAddr()</pre>
</li>
</ul>
<a name="getWinSigma--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWinSigma</h4>
<pre>public&nbsp;double&nbsp;getWinSigma()</pre>
<div class="block">Returns winSigma value</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="load-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>load</h4>
<pre>public&nbsp;boolean&nbsp;load(java.lang.String&nbsp;filename)</pre>
<div class="block">loads HOGDescriptor parameters and coefficients for the linear SVM classifier from a file</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - Name of the file to read.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="load-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>load</h4>
<pre>public&nbsp;boolean&nbsp;load(java.lang.String&nbsp;filename,
                    java.lang.String&nbsp;objname)</pre>
<div class="block">loads HOGDescriptor parameters and coefficients for the linear SVM classifier from a file</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - Name of the file to read.</dd>
<dd><code>objname</code> - The optional name of the node to read (if empty, the first top-level node will be used).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="save-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>save</h4>
<pre>public&nbsp;void&nbsp;save(java.lang.String&nbsp;filename)</pre>
<div class="block">saves HOGDescriptor parameters and coefficients for the linear SVM classifier to a file</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - File name</dd>
</dl>
</li>
</ul>
<a name="save-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>save</h4>
<pre>public&nbsp;void&nbsp;save(java.lang.String&nbsp;filename,
                 java.lang.String&nbsp;objname)</pre>
<div class="block">saves HOGDescriptor parameters and coefficients for the linear SVM classifier to a file</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - File name</dd>
<dd><code>objname</code> - Object name</dd>
</dl>
</li>
</ul>
<a name="setSVMDetector-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setSVMDetector</h4>
<pre>public&nbsp;void&nbsp;setSVMDetector(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;svmdetector)</pre>
<div class="block">Sets coefficients for the linear SVM classifier.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>svmdetector</code> - coefficients for the linear SVM classifier.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/GridBoard.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/Objdetect.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/HOGDescriptor.html" target="_top">Frames</a></li>
<li><a href="HOGDescriptor.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
