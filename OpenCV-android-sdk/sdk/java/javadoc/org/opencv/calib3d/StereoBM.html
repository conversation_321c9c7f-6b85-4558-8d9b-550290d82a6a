<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:25 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>StereoBM (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="StereoBM (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/calib3d/Calib3d.html" title="class in org.opencv.calib3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/calib3d/StereoBM.html" target="_top">Frames</a></li>
<li><a href="StereoBM.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.calib3d</div>
<h2 title="Class StereoBM" class="title">Class StereoBM</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d">org.opencv.calib3d.StereoMatcher</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.calib3d.StereoBM</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">StereoBM</span>
extends <a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d">StereoMatcher</a></pre>
<div class="block">Class for computing stereo correspondence using the block matching algorithm, introduced and
 contributed to OpenCV by K. Konolige.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#PREFILTER_NORMALIZED_RESPONSE">PREFILTER_NORMALIZED_RESPONSE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#PREFILTER_XSOBEL">PREFILTER_XSOBEL</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.opencv.calib3d.StereoMatcher">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.opencv.calib3d.<a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d">StereoMatcher</a></h3>
<code><a href="../../../org/opencv/calib3d/StereoMatcher.html#DISP_SCALE">DISP_SCALE</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#DISP_SHIFT">DISP_SHIFT</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoBM.html" title="class in org.opencv.calib3d">StereoBM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoBM.html" title="class in org.opencv.calib3d">StereoBM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#create--">create</a></span>()</code>
<div class="block">Creates StereoBM object

     disparity from 0 (default minimum disparity) to numDisparities.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoBM.html" title="class in org.opencv.calib3d">StereoBM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#create-int-">create</a></span>(int&nbsp;numDisparities)</code>
<div class="block">Creates StereoBM object</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoBM.html" title="class in org.opencv.calib3d">StereoBM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#create-int-int-">create</a></span>(int&nbsp;numDisparities,
      int&nbsp;blockSize)</code>
<div class="block">Creates StereoBM object</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#getPreFilterCap--">getPreFilterCap</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#getPreFilterSize--">getPreFilterSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#getPreFilterType--">getPreFilterType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#getROI1--">getROI1</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#getROI2--">getROI2</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#getSmallerBlockSize--">getSmallerBlockSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#getTextureThreshold--">getTextureThreshold</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#getUniquenessRatio--">getUniquenessRatio</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#setPreFilterCap-int-">setPreFilterCap</a></span>(int&nbsp;preFilterCap)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#setPreFilterSize-int-">setPreFilterSize</a></span>(int&nbsp;preFilterSize)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#setPreFilterType-int-">setPreFilterType</a></span>(int&nbsp;preFilterType)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#setROI1-org.opencv.core.Rect-">setROI1</a></span>(<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi1)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#setROI2-org.opencv.core.Rect-">setROI2</a></span>(<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi2)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#setSmallerBlockSize-int-">setSmallerBlockSize</a></span>(int&nbsp;blockSize)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#setTextureThreshold-int-">setTextureThreshold</a></span>(int&nbsp;textureThreshold)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoBM.html#setUniquenessRatio-int-">setUniquenessRatio</a></span>(int&nbsp;uniquenessRatio)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.calib3d.StereoMatcher">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.calib3d.<a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d">StereoMatcher</a></h3>
<code><a href="../../../org/opencv/calib3d/StereoMatcher.html#compute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">compute</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#getBlockSize--">getBlockSize</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#getDisp12MaxDiff--">getDisp12MaxDiff</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#getMinDisparity--">getMinDisparity</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#getNumDisparities--">getNumDisparities</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#getSpeckleRange--">getSpeckleRange</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#getSpeckleWindowSize--">getSpeckleWindowSize</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#setBlockSize-int-">setBlockSize</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#setDisp12MaxDiff-int-">setDisp12MaxDiff</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#setMinDisparity-int-">setMinDisparity</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#setNumDisparities-int-">setNumDisparities</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#setSpeckleRange-int-">setSpeckleRange</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#setSpeckleWindowSize-int-">setSpeckleWindowSize</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#empty--">empty</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="PREFILTER_NORMALIZED_RESPONSE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PREFILTER_NORMALIZED_RESPONSE</h4>
<pre>public static final&nbsp;int PREFILTER_NORMALIZED_RESPONSE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.StereoBM.PREFILTER_NORMALIZED_RESPONSE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PREFILTER_XSOBEL">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PREFILTER_XSOBEL</h4>
<pre>public static final&nbsp;int PREFILTER_XSOBEL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.StereoBM.PREFILTER_XSOBEL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoBM.html" title="class in org.opencv.calib3d">StereoBM</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoBM.html" title="class in org.opencv.calib3d">StereoBM</a>&nbsp;create()</pre>
<div class="block">Creates StereoBM object

     disparity from 0 (default minimum disparity) to numDisparities. The search range can then be
     shifted by changing the minimum disparity.
     (as the block is centered at the current pixel). Larger block size implies smoother, though less
     accurate disparity map. Smaller block size gives more detailed disparity map, but there is higher
     chance for algorithm to find a wrong correspondence.

     The function create StereoBM object. You can then call StereoBM::compute() to compute disparity for
     a specific stereo pair.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoBM.html" title="class in org.opencv.calib3d">StereoBM</a>&nbsp;create(int&nbsp;numDisparities)</pre>
<div class="block">Creates StereoBM object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>numDisparities</code> - the disparity search range. For each pixel algorithm will find the best
     disparity from 0 (default minimum disparity) to numDisparities. The search range can then be
     shifted by changing the minimum disparity.
     (as the block is centered at the current pixel). Larger block size implies smoother, though less
     accurate disparity map. Smaller block size gives more detailed disparity map, but there is higher
     chance for algorithm to find a wrong correspondence.

     The function create StereoBM object. You can then call StereoBM::compute() to compute disparity for
     a specific stereo pair.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoBM.html" title="class in org.opencv.calib3d">StereoBM</a>&nbsp;create(int&nbsp;numDisparities,
                              int&nbsp;blockSize)</pre>
<div class="block">Creates StereoBM object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>numDisparities</code> - the disparity search range. For each pixel algorithm will find the best
     disparity from 0 (default minimum disparity) to numDisparities. The search range can then be
     shifted by changing the minimum disparity.</dd>
<dd><code>blockSize</code> - the linear size of the blocks compared by the algorithm. The size should be odd
     (as the block is centered at the current pixel). Larger block size implies smoother, though less
     accurate disparity map. Smaller block size gives more detailed disparity map, but there is higher
     chance for algorithm to find a wrong correspondence.

     The function create StereoBM object. You can then call StereoBM::compute() to compute disparity for
     a specific stereo pair.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getPreFilterCap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPreFilterCap</h4>
<pre>public&nbsp;int&nbsp;getPreFilterCap()</pre>
</li>
</ul>
<a name="getPreFilterSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPreFilterSize</h4>
<pre>public&nbsp;int&nbsp;getPreFilterSize()</pre>
</li>
</ul>
<a name="getPreFilterType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPreFilterType</h4>
<pre>public&nbsp;int&nbsp;getPreFilterType()</pre>
</li>
</ul>
<a name="getROI1--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getROI1</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;getROI1()</pre>
</li>
</ul>
<a name="getROI2--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getROI2</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;getROI2()</pre>
</li>
</ul>
<a name="getSmallerBlockSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSmallerBlockSize</h4>
<pre>public&nbsp;int&nbsp;getSmallerBlockSize()</pre>
</li>
</ul>
<a name="getTextureThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTextureThreshold</h4>
<pre>public&nbsp;int&nbsp;getTextureThreshold()</pre>
</li>
</ul>
<a name="getUniquenessRatio--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUniquenessRatio</h4>
<pre>public&nbsp;int&nbsp;getUniquenessRatio()</pre>
</li>
</ul>
<a name="setPreFilterCap-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPreFilterCap</h4>
<pre>public&nbsp;void&nbsp;setPreFilterCap(int&nbsp;preFilterCap)</pre>
</li>
</ul>
<a name="setPreFilterSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPreFilterSize</h4>
<pre>public&nbsp;void&nbsp;setPreFilterSize(int&nbsp;preFilterSize)</pre>
</li>
</ul>
<a name="setPreFilterType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPreFilterType</h4>
<pre>public&nbsp;void&nbsp;setPreFilterType(int&nbsp;preFilterType)</pre>
</li>
</ul>
<a name="setROI1-org.opencv.core.Rect-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setROI1</h4>
<pre>public&nbsp;void&nbsp;setROI1(<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi1)</pre>
</li>
</ul>
<a name="setROI2-org.opencv.core.Rect-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setROI2</h4>
<pre>public&nbsp;void&nbsp;setROI2(<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi2)</pre>
</li>
</ul>
<a name="setSmallerBlockSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSmallerBlockSize</h4>
<pre>public&nbsp;void&nbsp;setSmallerBlockSize(int&nbsp;blockSize)</pre>
</li>
</ul>
<a name="setTextureThreshold-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTextureThreshold</h4>
<pre>public&nbsp;void&nbsp;setTextureThreshold(int&nbsp;textureThreshold)</pre>
</li>
</ul>
<a name="setUniquenessRatio-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setUniquenessRatio</h4>
<pre>public&nbsp;void&nbsp;setUniquenessRatio(int&nbsp;uniquenessRatio)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/calib3d/Calib3d.html" title="class in org.opencv.calib3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/calib3d/StereoBM.html" target="_top">Frames</a></li>
<li><a href="StereoBM.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
