<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:25 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.opencv.android Class Hierarchy (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.opencv.android Class Hierarchy (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li><a href="../../../org/opencv/calib3d/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/android/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package org.opencv.android</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/BaseLoaderCallback.html" title="class in org.opencv.android"><span class="typeNameLink">BaseLoaderCallback</span></a> (implements org.opencv.android.<a href="../../../org/opencv/android/LoaderCallbackInterface.html" title="interface in org.opencv.android">LoaderCallbackInterface</a>)</li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/CameraGLRendererBase.html" title="class in org.opencv.android"><span class="typeNameLink">CameraGLRendererBase</span></a> (implements android.opengl.GLSurfaceView.Renderer, android.graphics.SurfaceTexture.OnFrameAvailableListener)
<ul>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/Camera2Renderer.html" title="class in org.opencv.android"><span class="typeNameLink">Camera2Renderer</span></a></li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/CameraRenderer.html" title="class in org.opencv.android"><span class="typeNameLink">CameraRenderer</span></a></li>
</ul>
</li>
<li type="circle">android.content.Context
<ul>
<li type="circle">android.content.ContextWrapper
<ul>
<li type="circle">android.view.ContextThemeWrapper
<ul>
<li type="circle">android.app.Activity (implements android.content.ComponentCallbacks2, android.view.KeyEvent.Callback, android.view.LayoutInflater.Factory2, android.view.View.OnCreateContextMenuListener, android.view.Window.Callback)
<ul>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/CameraActivity.html" title="class in org.opencv.android"><span class="typeNameLink">CameraActivity</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/FpsMeter.html" title="class in org.opencv.android"><span class="typeNameLink">FpsMeter</span></a></li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/JavaCamera2View.JavaCameraSizeAccessor.html" title="class in org.opencv.android"><span class="typeNameLink">JavaCamera2View.JavaCameraSizeAccessor</span></a> (implements org.opencv.android.<a href="../../../org/opencv/android/CameraBridgeViewBase.ListItemAccessor.html" title="interface in org.opencv.android">CameraBridgeViewBase.ListItemAccessor</a>)</li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/JavaCameraView.JavaCameraSizeAccessor.html" title="class in org.opencv.android"><span class="typeNameLink">JavaCameraView.JavaCameraSizeAccessor</span></a> (implements org.opencv.android.<a href="../../../org/opencv/android/CameraBridgeViewBase.ListItemAccessor.html" title="interface in org.opencv.android">CameraBridgeViewBase.ListItemAccessor</a>)</li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/OpenCVLoader.html" title="class in org.opencv.android"><span class="typeNameLink">OpenCVLoader</span></a></li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/Utils.html" title="class in org.opencv.android"><span class="typeNameLink">Utils</span></a></li>
<li type="circle">android.view.View (implements android.view.accessibility.AccessibilityEventSource, android.graphics.drawable.Drawable.Callback, android.view.KeyEvent.Callback)
<ul>
<li type="circle">android.view.SurfaceView
<ul>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/CameraBridgeViewBase.html" title="class in org.opencv.android"><span class="typeNameLink">CameraBridgeViewBase</span></a> (implements android.view.SurfaceHolder.Callback)
<ul>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/JavaCamera2View.html" title="class in org.opencv.android"><span class="typeNameLink">JavaCamera2View</span></a></li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/JavaCameraView.html" title="class in org.opencv.android"><span class="typeNameLink">JavaCameraView</span></a> (implements android.hardware.Camera.PreviewCallback)</li>
</ul>
</li>
<li type="circle">android.opengl.GLSurfaceView (implements android.view.SurfaceHolder.Callback2)
<ul>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/CameraGLSurfaceView.html" title="class in org.opencv.android"><span class="typeNameLink">CameraGLSurfaceView</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/CameraBridgeViewBase.CvCameraViewFrame.html" title="interface in org.opencv.android"><span class="typeNameLink">CameraBridgeViewBase.CvCameraViewFrame</span></a></li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/CameraBridgeViewBase.CvCameraViewListener.html" title="interface in org.opencv.android"><span class="typeNameLink">CameraBridgeViewBase.CvCameraViewListener</span></a></li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/CameraBridgeViewBase.CvCameraViewListener2.html" title="interface in org.opencv.android"><span class="typeNameLink">CameraBridgeViewBase.CvCameraViewListener2</span></a></li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/CameraBridgeViewBase.ListItemAccessor.html" title="interface in org.opencv.android"><span class="typeNameLink">CameraBridgeViewBase.ListItemAccessor</span></a></li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/CameraGLSurfaceView.CameraTextureListener.html" title="interface in org.opencv.android"><span class="typeNameLink">CameraGLSurfaceView.CameraTextureListener</span></a></li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/InstallCallbackInterface.html" title="interface in org.opencv.android"><span class="typeNameLink">InstallCallbackInterface</span></a></li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/LoaderCallbackInterface.html" title="interface in org.opencv.android"><span class="typeNameLink">LoaderCallbackInterface</span></a></li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li><a href="../../../org/opencv/calib3d/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/android/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
