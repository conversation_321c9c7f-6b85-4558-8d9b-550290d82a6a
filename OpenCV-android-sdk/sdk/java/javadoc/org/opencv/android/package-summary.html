<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:25 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.opencv.android (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.opencv.android (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Package</li>
<li><a href="../../../org/opencv/calib3d/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/android/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;org.opencv.android</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/android/CameraBridgeViewBase.CvCameraViewFrame.html" title="interface in org.opencv.android">CameraBridgeViewBase.CvCameraViewFrame</a></td>
<td class="colLast">
<div class="block">This class interface is abstract representation of single frame from camera for onCameraFrame callback
 Attention: Do not use objects, that represents this interface out of onCameraFrame callback!</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/android/CameraBridgeViewBase.CvCameraViewListener.html" title="interface in org.opencv.android">CameraBridgeViewBase.CvCameraViewListener</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/android/CameraBridgeViewBase.CvCameraViewListener2.html" title="interface in org.opencv.android">CameraBridgeViewBase.CvCameraViewListener2</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/android/CameraBridgeViewBase.ListItemAccessor.html" title="interface in org.opencv.android">CameraBridgeViewBase.ListItemAccessor</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/android/CameraGLSurfaceView.CameraTextureListener.html" title="interface in org.opencv.android">CameraGLSurfaceView.CameraTextureListener</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/android/InstallCallbackInterface.html" title="interface in org.opencv.android">InstallCallbackInterface</a></td>
<td class="colLast">
<div class="block">Installation callback interface.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/android/LoaderCallbackInterface.html" title="interface in org.opencv.android">LoaderCallbackInterface</a></td>
<td class="colLast">
<div class="block">Interface for callback object in case of asynchronous initialization of OpenCV.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/android/BaseLoaderCallback.html" title="class in org.opencv.android">BaseLoaderCallback</a></td>
<td class="colLast">
<div class="block">Basic implementation of LoaderCallbackInterface.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/android/Camera2Renderer.html" title="class in org.opencv.android">Camera2Renderer</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/android/CameraActivity.html" title="class in org.opencv.android">CameraActivity</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/android/CameraBridgeViewBase.html" title="class in org.opencv.android">CameraBridgeViewBase</a></td>
<td class="colLast">
<div class="block">This is a basic class, implementing the interaction with Camera and OpenCV library.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/android/CameraGLRendererBase.html" title="class in org.opencv.android">CameraGLRendererBase</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/android/CameraGLSurfaceView.html" title="class in org.opencv.android">CameraGLSurfaceView</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/android/CameraRenderer.html" title="class in org.opencv.android">CameraRenderer</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/android/FpsMeter.html" title="class in org.opencv.android">FpsMeter</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/android/JavaCamera2View.html" title="class in org.opencv.android">JavaCamera2View</a></td>
<td class="colLast">
<div class="block">This class is an implementation of the Bridge View between OpenCV and Java Camera.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/android/JavaCamera2View.JavaCameraSizeAccessor.html" title="class in org.opencv.android">JavaCamera2View.JavaCameraSizeAccessor</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/android/JavaCameraView.html" title="class in org.opencv.android">JavaCameraView</a></td>
<td class="colLast">
<div class="block">This class is an implementation of the Bridge View between OpenCV and Java Camera.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/android/JavaCameraView.JavaCameraSizeAccessor.html" title="class in org.opencv.android">JavaCameraView.JavaCameraSizeAccessor</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/android/OpenCVLoader.html" title="class in org.opencv.android">OpenCVLoader</a></td>
<td class="colLast">
<div class="block">Helper class provides common initialization methods for OpenCV library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/android/Utils.html" title="class in org.opencv.android">Utils</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Package</li>
<li><a href="../../../org/opencv/calib3d/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/android/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
