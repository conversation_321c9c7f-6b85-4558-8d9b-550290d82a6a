<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:23 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Utils (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Utils (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/android/OpenCVLoader.html" title="class in org.opencv.android"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/android/Utils.html" target="_top">Frames</a></li>
<li><a href="Utils.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.android</div>
<h2 title="Class Utils" class="title">Class Utils</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.android.Utils</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Utils</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/android/Utils.html#Utils--">Utils</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/Utils.html#bitmapToMat-android.graphics.Bitmap-org.opencv.core.Mat-">bitmapToMat</a></span>(android.graphics.Bitmap&nbsp;bmp,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mat)</code>
<div class="block">Short form of the bitmapToMat(bmp, mat, unPremultiplyAlpha=false).</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/Utils.html#bitmapToMat-android.graphics.Bitmap-org.opencv.core.Mat-boolean-">bitmapToMat</a></span>(android.graphics.Bitmap&nbsp;bmp,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mat,
           boolean&nbsp;unPremultiplyAlpha)</code>
<div class="block">Converts Android Bitmap to OpenCV Mat.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/Utils.html#exportResource-android.content.Context-int-">exportResource</a></span>(android.content.Context&nbsp;context,
              int&nbsp;resourceId)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/Utils.html#exportResource-android.content.Context-int-java.lang.String-">exportResource</a></span>(android.content.Context&nbsp;context,
              int&nbsp;resourceId,
              java.lang.String&nbsp;dirname)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/Utils.html#loadResource-android.content.Context-int-">loadResource</a></span>(android.content.Context&nbsp;context,
            int&nbsp;resourceId)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/Utils.html#loadResource-android.content.Context-int-int-">loadResource</a></span>(android.content.Context&nbsp;context,
            int&nbsp;resourceId,
            int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/Utils.html#matToBitmap-org.opencv.core.Mat-android.graphics.Bitmap-">matToBitmap</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mat,
           android.graphics.Bitmap&nbsp;bmp)</code>
<div class="block">Short form of the <b>matToBitmap(mat, bmp, premultiplyAlpha=false)</b></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/Utils.html#matToBitmap-org.opencv.core.Mat-android.graphics.Bitmap-boolean-">matToBitmap</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mat,
           android.graphics.Bitmap&nbsp;bmp,
           boolean&nbsp;premultiplyAlpha)</code>
<div class="block">Converts OpenCV Mat to Android Bitmap.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Utils--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Utils</h4>
<pre>public&nbsp;Utils()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="bitmapToMat-android.graphics.Bitmap-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bitmapToMat</h4>
<pre>public static&nbsp;void&nbsp;bitmapToMat(android.graphics.Bitmap&nbsp;bmp,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mat)</pre>
<div class="block">Short form of the bitmapToMat(bmp, mat, unPremultiplyAlpha=false).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bmp</code> - is a valid input Bitmap object of the type 'ARGB_8888' or 'RGB_565'.</dd>
<dd><code>mat</code> - is a valid output Mat object, it will be reallocated if needed, so Mat may be empty.</dd>
</dl>
</li>
</ul>
<a name="bitmapToMat-android.graphics.Bitmap-org.opencv.core.Mat-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bitmapToMat</h4>
<pre>public static&nbsp;void&nbsp;bitmapToMat(android.graphics.Bitmap&nbsp;bmp,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mat,
                               boolean&nbsp;unPremultiplyAlpha)</pre>
<div class="block">Converts Android Bitmap to OpenCV Mat.
 <p>
 This function converts an Android Bitmap image to the OpenCV Mat.
 <br>'ARGB_8888' and 'RGB_565' input Bitmap formats are supported.
 <br>The output Mat is always created of the same size as the input Bitmap and of the 'CV_8UC4' type,
 it keeps the image in RGBA format.
 <br>This function throws an exception if the conversion fails.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bmp</code> - is a valid input Bitmap object of the type 'ARGB_8888' or 'RGB_565'.</dd>
<dd><code>mat</code> - is a valid output Mat object, it will be reallocated if needed, so it may be empty.</dd>
<dd><code>unPremultiplyAlpha</code> - is a flag, that determines, whether the bitmap needs to be converted from alpha premultiplied format (like Android keeps 'ARGB_8888' ones) to regular one; this flag is ignored for 'RGB_565' bitmaps.</dd>
</dl>
</li>
</ul>
<a name="exportResource-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exportResource</h4>
<pre>public static&nbsp;java.lang.String&nbsp;exportResource(android.content.Context&nbsp;context,
                                              int&nbsp;resourceId)</pre>
</li>
</ul>
<a name="exportResource-android.content.Context-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exportResource</h4>
<pre>public static&nbsp;java.lang.String&nbsp;exportResource(android.content.Context&nbsp;context,
                                              int&nbsp;resourceId,
                                              java.lang.String&nbsp;dirname)</pre>
</li>
</ul>
<a name="loadResource-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>loadResource</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;loadResource(android.content.Context&nbsp;context,
                               int&nbsp;resourceId)
                        throws java.io.IOException</pre>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="loadResource-android.content.Context-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>loadResource</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;loadResource(android.content.Context&nbsp;context,
                               int&nbsp;resourceId,
                               int&nbsp;flags)
                        throws java.io.IOException</pre>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="matToBitmap-org.opencv.core.Mat-android.graphics.Bitmap-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>matToBitmap</h4>
<pre>public static&nbsp;void&nbsp;matToBitmap(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mat,
                               android.graphics.Bitmap&nbsp;bmp)</pre>
<div class="block">Short form of the <b>matToBitmap(mat, bmp, premultiplyAlpha=false)</b></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mat</code> - is a valid input Mat object of the types 'CV_8UC1', 'CV_8UC3' or 'CV_8UC4'.</dd>
<dd><code>bmp</code> - is a valid Bitmap object of the same size as the Mat and of type 'ARGB_8888' or 'RGB_565'.</dd>
</dl>
</li>
</ul>
<a name="matToBitmap-org.opencv.core.Mat-android.graphics.Bitmap-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>matToBitmap</h4>
<pre>public static&nbsp;void&nbsp;matToBitmap(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mat,
                               android.graphics.Bitmap&nbsp;bmp,
                               boolean&nbsp;premultiplyAlpha)</pre>
<div class="block">Converts OpenCV Mat to Android Bitmap.
 <p>
 <br>This function converts an image in the OpenCV Mat representation to the Android Bitmap.
 <br>The input Mat object has to be of the types 'CV_8UC1' (gray-scale), 'CV_8UC3' (RGB) or 'CV_8UC4' (RGBA).
 <br>The output Bitmap object has to be of the same size as the input Mat and of the types 'ARGB_8888' or 'RGB_565'.
 <br>This function throws an exception if the conversion fails.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mat</code> - is a valid input Mat object of types 'CV_8UC1', 'CV_8UC3' or 'CV_8UC4'.</dd>
<dd><code>bmp</code> - is a valid Bitmap object of the same size as the Mat and of type 'ARGB_8888' or 'RGB_565'.</dd>
<dd><code>premultiplyAlpha</code> - is a flag, that determines, whether the Mat needs to be converted to alpha premultiplied format (like Android keeps 'ARGB_8888' bitmaps); the flag is ignored for 'RGB_565' bitmaps.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/android/OpenCVLoader.html" title="class in org.opencv.android"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/android/Utils.html" target="_top">Frames</a></li>
<li><a href="Utils.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
