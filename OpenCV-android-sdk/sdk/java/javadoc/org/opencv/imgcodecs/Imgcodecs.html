<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:24 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Imgcodecs (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Imgcodecs (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/imgcodecs/Imgcodecs.html" target="_top">Frames</a></li>
<li><a href="Imgcodecs.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.imgcodecs</div>
<h2 title="Class Imgcodecs" class="title">Class Imgcodecs</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.imgcodecs.Imgcodecs</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Imgcodecs</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_ANYCOLOR">IMREAD_ANYCOLOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_ANYDEPTH">IMREAD_ANYDEPTH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_COLOR">IMREAD_COLOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_GRAYSCALE">IMREAD_GRAYSCALE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_IGNORE_ORIENTATION">IMREAD_IGNORE_ORIENTATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_LOAD_GDAL">IMREAD_LOAD_GDAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_REDUCED_COLOR_2">IMREAD_REDUCED_COLOR_2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_REDUCED_COLOR_4">IMREAD_REDUCED_COLOR_4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_REDUCED_COLOR_8">IMREAD_REDUCED_COLOR_8</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_REDUCED_GRAYSCALE_2">IMREAD_REDUCED_GRAYSCALE_2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_REDUCED_GRAYSCALE_4">IMREAD_REDUCED_GRAYSCALE_4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_REDUCED_GRAYSCALE_8">IMREAD_REDUCED_GRAYSCALE_8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_UNCHANGED">IMREAD_UNCHANGED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_AVIF_DEPTH">IMWRITE_AVIF_DEPTH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_AVIF_QUALITY">IMWRITE_AVIF_QUALITY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_AVIF_SPEED">IMWRITE_AVIF_SPEED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION">IMWRITE_EXR_COMPRESSION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_B44">IMWRITE_EXR_COMPRESSION_B44</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_B44A">IMWRITE_EXR_COMPRESSION_B44A</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_DWAA">IMWRITE_EXR_COMPRESSION_DWAA</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_DWAB">IMWRITE_EXR_COMPRESSION_DWAB</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_NO">IMWRITE_EXR_COMPRESSION_NO</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_PIZ">IMWRITE_EXR_COMPRESSION_PIZ</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_PXR24">IMWRITE_EXR_COMPRESSION_PXR24</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_RLE">IMWRITE_EXR_COMPRESSION_RLE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_ZIP">IMWRITE_EXR_COMPRESSION_ZIP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_ZIPS">IMWRITE_EXR_COMPRESSION_ZIPS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_DWA_COMPRESSION_LEVEL">IMWRITE_EXR_DWA_COMPRESSION_LEVEL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_TYPE">IMWRITE_EXR_TYPE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_TYPE_FLOAT">IMWRITE_EXR_TYPE_FLOAT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_TYPE_HALF">IMWRITE_EXR_TYPE_HALF</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_HDR_COMPRESSION">IMWRITE_HDR_COMPRESSION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_HDR_COMPRESSION_NONE">IMWRITE_HDR_COMPRESSION_NONE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_HDR_COMPRESSION_RLE">IMWRITE_HDR_COMPRESSION_RLE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_CHROMA_QUALITY">IMWRITE_JPEG_CHROMA_QUALITY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_LUMA_QUALITY">IMWRITE_JPEG_LUMA_QUALITY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_OPTIMIZE">IMWRITE_JPEG_OPTIMIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_PROGRESSIVE">IMWRITE_JPEG_PROGRESSIVE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_QUALITY">IMWRITE_JPEG_QUALITY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_RST_INTERVAL">IMWRITE_JPEG_RST_INTERVAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_SAMPLING_FACTOR">IMWRITE_JPEG_SAMPLING_FACTOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_SAMPLING_FACTOR_411">IMWRITE_JPEG_SAMPLING_FACTOR_411</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_SAMPLING_FACTOR_420">IMWRITE_JPEG_SAMPLING_FACTOR_420</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_SAMPLING_FACTOR_422">IMWRITE_JPEG_SAMPLING_FACTOR_422</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_SAMPLING_FACTOR_440">IMWRITE_JPEG_SAMPLING_FACTOR_440</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_SAMPLING_FACTOR_444">IMWRITE_JPEG_SAMPLING_FACTOR_444</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG2000_COMPRESSION_X1000">IMWRITE_JPEG2000_COMPRESSION_X1000</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PAM_FORMAT_BLACKANDWHITE">IMWRITE_PAM_FORMAT_BLACKANDWHITE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PAM_FORMAT_GRAYSCALE">IMWRITE_PAM_FORMAT_GRAYSCALE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PAM_FORMAT_GRAYSCALE_ALPHA">IMWRITE_PAM_FORMAT_GRAYSCALE_ALPHA</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PAM_FORMAT_NULL">IMWRITE_PAM_FORMAT_NULL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PAM_FORMAT_RGB">IMWRITE_PAM_FORMAT_RGB</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PAM_FORMAT_RGB_ALPHA">IMWRITE_PAM_FORMAT_RGB_ALPHA</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PAM_TUPLETYPE">IMWRITE_PAM_TUPLETYPE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_BILEVEL">IMWRITE_PNG_BILEVEL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_COMPRESSION">IMWRITE_PNG_COMPRESSION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_STRATEGY">IMWRITE_PNG_STRATEGY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_STRATEGY_DEFAULT">IMWRITE_PNG_STRATEGY_DEFAULT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_STRATEGY_FILTERED">IMWRITE_PNG_STRATEGY_FILTERED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_STRATEGY_FIXED">IMWRITE_PNG_STRATEGY_FIXED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY">IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_STRATEGY_RLE">IMWRITE_PNG_STRATEGY_RLE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PXM_BINARY">IMWRITE_PXM_BINARY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_TIFF_COMPRESSION">IMWRITE_TIFF_COMPRESSION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_TIFF_RESUNIT">IMWRITE_TIFF_RESUNIT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_TIFF_XDPI">IMWRITE_TIFF_XDPI</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_TIFF_YDPI">IMWRITE_TIFF_YDPI</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_WEBP_QUALITY">IMWRITE_WEBP_QUALITY</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#Imgcodecs--">Imgcodecs</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#haveImageReader-java.lang.String-">haveImageReader</a></span>(java.lang.String&nbsp;filename)</code>
<div class="block">Returns true if the specified image can be decoded by OpenCV</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#haveImageWriter-java.lang.String-">haveImageWriter</a></span>(java.lang.String&nbsp;filename)</code>
<div class="block">Returns true if an image with the specified filename can be encoded by OpenCV</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imcount-java.lang.String-">imcount</a></span>(java.lang.String&nbsp;filename)</code>
<div class="block">Returns the number of images inside the give file

 The function imcount will return the number of pages in a multi-page image, or 1 for single-page images</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imcount-java.lang.String-int-">imcount</a></span>(java.lang.String&nbsp;filename,
       int&nbsp;flags)</code>
<div class="block">Returns the number of images inside the give file

 The function imcount will return the number of pages in a multi-page image, or 1 for single-page images</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imdecode-org.opencv.core.Mat-int-">imdecode</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;buf,
        int&nbsp;flags)</code>
<div class="block">Reads an image from a buffer in memory.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imdecodemulti-org.opencv.core.Mat-int-java.util.List-">imdecodemulti</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;buf,
             int&nbsp;flags,
             java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</code>
<div class="block">Reads a multi-page image from a buffer in memory.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imencode-java.lang.String-org.opencv.core.Mat-org.opencv.core.MatOfByte-">imencode</a></span>(java.lang.String&nbsp;ext,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
        <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buf)</code>
<div class="block">Encodes an image into a memory buffer.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imencode-java.lang.String-org.opencv.core.Mat-org.opencv.core.MatOfByte-org.opencv.core.MatOfInt-">imencode</a></span>(java.lang.String&nbsp;ext,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
        <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buf,
        <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code>
<div class="block">Encodes an image into a memory buffer.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imread-java.lang.String-">imread</a></span>(java.lang.String&nbsp;filename)</code>
<div class="block">Loads an image from a file.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imread-java.lang.String-int-">imread</a></span>(java.lang.String&nbsp;filename,
      int&nbsp;flags)</code>
<div class="block">Loads an image from a file.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imreadmulti-java.lang.String-java.util.List-">imreadmulti</a></span>(java.lang.String&nbsp;filename,
           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</code>
<div class="block">Loads a multi-page image from a file.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imreadmulti-java.lang.String-java.util.List-int-">imreadmulti</a></span>(java.lang.String&nbsp;filename,
           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats,
           int&nbsp;flags)</code>
<div class="block">Loads a multi-page image from a file.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imreadmulti-java.lang.String-java.util.List-int-int-">imreadmulti</a></span>(java.lang.String&nbsp;filename,
           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats,
           int&nbsp;start,
           int&nbsp;count)</code>
<div class="block">Loads a of images of a multi-page image from a file.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imreadmulti-java.lang.String-java.util.List-int-int-int-">imreadmulti</a></span>(java.lang.String&nbsp;filename,
           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats,
           int&nbsp;start,
           int&nbsp;count,
           int&nbsp;flags)</code>
<div class="block">Loads a of images of a multi-page image from a file.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imwrite-java.lang.String-org.opencv.core.Mat-">imwrite</a></span>(java.lang.String&nbsp;filename,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</code>
<div class="block">Saves an image to a specified file.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imwrite-java.lang.String-org.opencv.core.Mat-org.opencv.core.MatOfInt-">imwrite</a></span>(java.lang.String&nbsp;filename,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
       <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code>
<div class="block">Saves an image to a specified file.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imwritemulti-java.lang.String-java.util.List-">imwritemulti</a></span>(java.lang.String&nbsp;filename,
            java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;img)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imwritemulti-java.lang.String-java.util.List-org.opencv.core.MatOfInt-">imwritemulti</a></span>(java.lang.String&nbsp;filename,
            java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;img,
            <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="IMREAD_ANYCOLOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_ANYCOLOR</h4>
<pre>public static final&nbsp;int IMREAD_ANYCOLOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_ANYCOLOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_ANYDEPTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_ANYDEPTH</h4>
<pre>public static final&nbsp;int IMREAD_ANYDEPTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_ANYDEPTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_COLOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_COLOR</h4>
<pre>public static final&nbsp;int IMREAD_COLOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_COLOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_GRAYSCALE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_GRAYSCALE</h4>
<pre>public static final&nbsp;int IMREAD_GRAYSCALE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_GRAYSCALE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_IGNORE_ORIENTATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_IGNORE_ORIENTATION</h4>
<pre>public static final&nbsp;int IMREAD_IGNORE_ORIENTATION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_IGNORE_ORIENTATION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_LOAD_GDAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_LOAD_GDAL</h4>
<pre>public static final&nbsp;int IMREAD_LOAD_GDAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_LOAD_GDAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_REDUCED_COLOR_2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_REDUCED_COLOR_2</h4>
<pre>public static final&nbsp;int IMREAD_REDUCED_COLOR_2</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_COLOR_2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_REDUCED_COLOR_4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_REDUCED_COLOR_4</h4>
<pre>public static final&nbsp;int IMREAD_REDUCED_COLOR_4</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_COLOR_4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_REDUCED_COLOR_8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_REDUCED_COLOR_8</h4>
<pre>public static final&nbsp;int IMREAD_REDUCED_COLOR_8</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_COLOR_8">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_REDUCED_GRAYSCALE_2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_REDUCED_GRAYSCALE_2</h4>
<pre>public static final&nbsp;int IMREAD_REDUCED_GRAYSCALE_2</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_GRAYSCALE_2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_REDUCED_GRAYSCALE_4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_REDUCED_GRAYSCALE_4</h4>
<pre>public static final&nbsp;int IMREAD_REDUCED_GRAYSCALE_4</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_GRAYSCALE_4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_REDUCED_GRAYSCALE_8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_REDUCED_GRAYSCALE_8</h4>
<pre>public static final&nbsp;int IMREAD_REDUCED_GRAYSCALE_8</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_GRAYSCALE_8">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_UNCHANGED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_UNCHANGED</h4>
<pre>public static final&nbsp;int IMREAD_UNCHANGED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_UNCHANGED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_AVIF_DEPTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_AVIF_DEPTH</h4>
<pre>public static final&nbsp;int IMWRITE_AVIF_DEPTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_AVIF_DEPTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_AVIF_QUALITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_AVIF_QUALITY</h4>
<pre>public static final&nbsp;int IMWRITE_AVIF_QUALITY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_AVIF_QUALITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_AVIF_SPEED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_AVIF_SPEED</h4>
<pre>public static final&nbsp;int IMWRITE_AVIF_SPEED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_AVIF_SPEED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_EXR_COMPRESSION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_EXR_COMPRESSION</h4>
<pre>public static final&nbsp;int IMWRITE_EXR_COMPRESSION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_EXR_COMPRESSION_B44">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_EXR_COMPRESSION_B44</h4>
<pre>public static final&nbsp;int IMWRITE_EXR_COMPRESSION_B44</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_B44">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_EXR_COMPRESSION_B44A">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_EXR_COMPRESSION_B44A</h4>
<pre>public static final&nbsp;int IMWRITE_EXR_COMPRESSION_B44A</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_B44A">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_EXR_COMPRESSION_DWAA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_EXR_COMPRESSION_DWAA</h4>
<pre>public static final&nbsp;int IMWRITE_EXR_COMPRESSION_DWAA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_DWAA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_EXR_COMPRESSION_DWAB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_EXR_COMPRESSION_DWAB</h4>
<pre>public static final&nbsp;int IMWRITE_EXR_COMPRESSION_DWAB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_DWAB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_EXR_COMPRESSION_NO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_EXR_COMPRESSION_NO</h4>
<pre>public static final&nbsp;int IMWRITE_EXR_COMPRESSION_NO</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_NO">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_EXR_COMPRESSION_PIZ">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_EXR_COMPRESSION_PIZ</h4>
<pre>public static final&nbsp;int IMWRITE_EXR_COMPRESSION_PIZ</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_PIZ">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_EXR_COMPRESSION_PXR24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_EXR_COMPRESSION_PXR24</h4>
<pre>public static final&nbsp;int IMWRITE_EXR_COMPRESSION_PXR24</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_PXR24">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_EXR_COMPRESSION_RLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_EXR_COMPRESSION_RLE</h4>
<pre>public static final&nbsp;int IMWRITE_EXR_COMPRESSION_RLE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_RLE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_EXR_COMPRESSION_ZIP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_EXR_COMPRESSION_ZIP</h4>
<pre>public static final&nbsp;int IMWRITE_EXR_COMPRESSION_ZIP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_ZIP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_EXR_COMPRESSION_ZIPS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_EXR_COMPRESSION_ZIPS</h4>
<pre>public static final&nbsp;int IMWRITE_EXR_COMPRESSION_ZIPS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_ZIPS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_EXR_DWA_COMPRESSION_LEVEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_EXR_DWA_COMPRESSION_LEVEL</h4>
<pre>public static final&nbsp;int IMWRITE_EXR_DWA_COMPRESSION_LEVEL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_DWA_COMPRESSION_LEVEL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_EXR_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_EXR_TYPE</h4>
<pre>public static final&nbsp;int IMWRITE_EXR_TYPE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_TYPE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_EXR_TYPE_FLOAT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_EXR_TYPE_FLOAT</h4>
<pre>public static final&nbsp;int IMWRITE_EXR_TYPE_FLOAT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_TYPE_FLOAT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_EXR_TYPE_HALF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_EXR_TYPE_HALF</h4>
<pre>public static final&nbsp;int IMWRITE_EXR_TYPE_HALF</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_TYPE_HALF">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_HDR_COMPRESSION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_HDR_COMPRESSION</h4>
<pre>public static final&nbsp;int IMWRITE_HDR_COMPRESSION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_HDR_COMPRESSION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_HDR_COMPRESSION_NONE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_HDR_COMPRESSION_NONE</h4>
<pre>public static final&nbsp;int IMWRITE_HDR_COMPRESSION_NONE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_HDR_COMPRESSION_NONE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_HDR_COMPRESSION_RLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_HDR_COMPRESSION_RLE</h4>
<pre>public static final&nbsp;int IMWRITE_HDR_COMPRESSION_RLE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_HDR_COMPRESSION_RLE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_JPEG_CHROMA_QUALITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_JPEG_CHROMA_QUALITY</h4>
<pre>public static final&nbsp;int IMWRITE_JPEG_CHROMA_QUALITY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_CHROMA_QUALITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_JPEG_LUMA_QUALITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_JPEG_LUMA_QUALITY</h4>
<pre>public static final&nbsp;int IMWRITE_JPEG_LUMA_QUALITY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_LUMA_QUALITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_JPEG_OPTIMIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_JPEG_OPTIMIZE</h4>
<pre>public static final&nbsp;int IMWRITE_JPEG_OPTIMIZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_OPTIMIZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_JPEG_PROGRESSIVE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_JPEG_PROGRESSIVE</h4>
<pre>public static final&nbsp;int IMWRITE_JPEG_PROGRESSIVE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_PROGRESSIVE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_JPEG_QUALITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_JPEG_QUALITY</h4>
<pre>public static final&nbsp;int IMWRITE_JPEG_QUALITY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_QUALITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_JPEG_RST_INTERVAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_JPEG_RST_INTERVAL</h4>
<pre>public static final&nbsp;int IMWRITE_JPEG_RST_INTERVAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_RST_INTERVAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_JPEG_SAMPLING_FACTOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_JPEG_SAMPLING_FACTOR</h4>
<pre>public static final&nbsp;int IMWRITE_JPEG_SAMPLING_FACTOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_SAMPLING_FACTOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_JPEG_SAMPLING_FACTOR_411">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_JPEG_SAMPLING_FACTOR_411</h4>
<pre>public static final&nbsp;int IMWRITE_JPEG_SAMPLING_FACTOR_411</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_SAMPLING_FACTOR_411">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_JPEG_SAMPLING_FACTOR_420">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_JPEG_SAMPLING_FACTOR_420</h4>
<pre>public static final&nbsp;int IMWRITE_JPEG_SAMPLING_FACTOR_420</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_SAMPLING_FACTOR_420">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_JPEG_SAMPLING_FACTOR_422">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_JPEG_SAMPLING_FACTOR_422</h4>
<pre>public static final&nbsp;int IMWRITE_JPEG_SAMPLING_FACTOR_422</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_SAMPLING_FACTOR_422">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_JPEG_SAMPLING_FACTOR_440">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_JPEG_SAMPLING_FACTOR_440</h4>
<pre>public static final&nbsp;int IMWRITE_JPEG_SAMPLING_FACTOR_440</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_SAMPLING_FACTOR_440">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_JPEG_SAMPLING_FACTOR_444">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_JPEG_SAMPLING_FACTOR_444</h4>
<pre>public static final&nbsp;int IMWRITE_JPEG_SAMPLING_FACTOR_444</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_SAMPLING_FACTOR_444">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_JPEG2000_COMPRESSION_X1000">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_JPEG2000_COMPRESSION_X1000</h4>
<pre>public static final&nbsp;int IMWRITE_JPEG2000_COMPRESSION_X1000</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG2000_COMPRESSION_X1000">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PAM_FORMAT_BLACKANDWHITE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PAM_FORMAT_BLACKANDWHITE</h4>
<pre>public static final&nbsp;int IMWRITE_PAM_FORMAT_BLACKANDWHITE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_FORMAT_BLACKANDWHITE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PAM_FORMAT_GRAYSCALE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PAM_FORMAT_GRAYSCALE</h4>
<pre>public static final&nbsp;int IMWRITE_PAM_FORMAT_GRAYSCALE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_FORMAT_GRAYSCALE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PAM_FORMAT_GRAYSCALE_ALPHA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PAM_FORMAT_GRAYSCALE_ALPHA</h4>
<pre>public static final&nbsp;int IMWRITE_PAM_FORMAT_GRAYSCALE_ALPHA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_FORMAT_GRAYSCALE_ALPHA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PAM_FORMAT_NULL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PAM_FORMAT_NULL</h4>
<pre>public static final&nbsp;int IMWRITE_PAM_FORMAT_NULL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_FORMAT_NULL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PAM_FORMAT_RGB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PAM_FORMAT_RGB</h4>
<pre>public static final&nbsp;int IMWRITE_PAM_FORMAT_RGB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_FORMAT_RGB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PAM_FORMAT_RGB_ALPHA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PAM_FORMAT_RGB_ALPHA</h4>
<pre>public static final&nbsp;int IMWRITE_PAM_FORMAT_RGB_ALPHA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_FORMAT_RGB_ALPHA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PAM_TUPLETYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PAM_TUPLETYPE</h4>
<pre>public static final&nbsp;int IMWRITE_PAM_TUPLETYPE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_TUPLETYPE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PNG_BILEVEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PNG_BILEVEL</h4>
<pre>public static final&nbsp;int IMWRITE_PNG_BILEVEL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_BILEVEL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PNG_COMPRESSION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PNG_COMPRESSION</h4>
<pre>public static final&nbsp;int IMWRITE_PNG_COMPRESSION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_COMPRESSION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PNG_STRATEGY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PNG_STRATEGY</h4>
<pre>public static final&nbsp;int IMWRITE_PNG_STRATEGY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PNG_STRATEGY_DEFAULT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PNG_STRATEGY_DEFAULT</h4>
<pre>public static final&nbsp;int IMWRITE_PNG_STRATEGY_DEFAULT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_DEFAULT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PNG_STRATEGY_FILTERED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PNG_STRATEGY_FILTERED</h4>
<pre>public static final&nbsp;int IMWRITE_PNG_STRATEGY_FILTERED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_FILTERED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PNG_STRATEGY_FIXED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PNG_STRATEGY_FIXED</h4>
<pre>public static final&nbsp;int IMWRITE_PNG_STRATEGY_FIXED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_FIXED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY</h4>
<pre>public static final&nbsp;int IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PNG_STRATEGY_RLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PNG_STRATEGY_RLE</h4>
<pre>public static final&nbsp;int IMWRITE_PNG_STRATEGY_RLE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_RLE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PXM_BINARY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PXM_BINARY</h4>
<pre>public static final&nbsp;int IMWRITE_PXM_BINARY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PXM_BINARY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_TIFF_COMPRESSION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_TIFF_COMPRESSION</h4>
<pre>public static final&nbsp;int IMWRITE_TIFF_COMPRESSION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_TIFF_RESUNIT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_TIFF_RESUNIT</h4>
<pre>public static final&nbsp;int IMWRITE_TIFF_RESUNIT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_RESUNIT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_TIFF_XDPI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_TIFF_XDPI</h4>
<pre>public static final&nbsp;int IMWRITE_TIFF_XDPI</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_XDPI">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_TIFF_YDPI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_TIFF_YDPI</h4>
<pre>public static final&nbsp;int IMWRITE_TIFF_YDPI</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_YDPI">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_WEBP_QUALITY">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>IMWRITE_WEBP_QUALITY</h4>
<pre>public static final&nbsp;int IMWRITE_WEBP_QUALITY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_WEBP_QUALITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Imgcodecs--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Imgcodecs</h4>
<pre>public&nbsp;Imgcodecs()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="haveImageReader-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>haveImageReader</h4>
<pre>public static&nbsp;boolean&nbsp;haveImageReader(java.lang.String&nbsp;filename)</pre>
<div class="block">Returns true if the specified image can be decoded by OpenCV</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - File name of the image</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="haveImageWriter-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>haveImageWriter</h4>
<pre>public static&nbsp;boolean&nbsp;haveImageWriter(java.lang.String&nbsp;filename)</pre>
<div class="block">Returns true if an image with the specified filename can be encoded by OpenCV</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - File name of the image</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="imcount-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imcount</h4>
<pre>public static&nbsp;long&nbsp;imcount(java.lang.String&nbsp;filename)</pre>
<div class="block">Returns the number of images inside the give file

 The function imcount will return the number of pages in a multi-page image, or 1 for single-page images</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - Name of file to be loaded.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="imcount-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imcount</h4>
<pre>public static&nbsp;long&nbsp;imcount(java.lang.String&nbsp;filename,
                           int&nbsp;flags)</pre>
<div class="block">Returns the number of images inside the give file

 The function imcount will return the number of pages in a multi-page image, or 1 for single-page images</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - Name of file to be loaded.</dd>
<dd><code>flags</code> - Flag that can take values of cv::ImreadModes, default with cv::IMREAD_ANYCOLOR.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="imdecode-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imdecode</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;imdecode(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;buf,
                           int&nbsp;flags)</pre>
<div class="block">Reads an image from a buffer in memory.

 The function imdecode reads an image from the specified buffer in the memory. If the buffer is too short or
 contains invalid data, the function returns an empty matrix ( Mat::data==NULL ).

 See cv::imread for the list of supported formats and flags description.

 <b>Note:</b> In the case of color images, the decoded images will have the channels stored in <b>B G R</b> order.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>buf</code> - Input array or vector of bytes.</dd>
<dd><code>flags</code> - The same flags as in cv::imread, see cv::ImreadModes.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="imdecodemulti-org.opencv.core.Mat-int-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imdecodemulti</h4>
<pre>public static&nbsp;boolean&nbsp;imdecodemulti(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;buf,
                                    int&nbsp;flags,
                                    java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</pre>
<div class="block">Reads a multi-page image from a buffer in memory.

 The function imdecodemulti reads a multi-page image from the specified buffer in the memory. If the buffer is too short or
 contains invalid data, the function returns false.

 See cv::imreadmulti for the list of supported formats and flags description.

 <b>Note:</b> In the case of color images, the decoded images will have the channels stored in <b>B G R</b> order.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>buf</code> - Input array or vector of bytes.</dd>
<dd><code>flags</code> - The same flags as in cv::imread, see cv::ImreadModes.</dd>
<dd><code>mats</code> - A vector of Mat objects holding each page, if more than one.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="imencode-java.lang.String-org.opencv.core.Mat-org.opencv.core.MatOfByte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imencode</h4>
<pre>public static&nbsp;boolean&nbsp;imencode(java.lang.String&nbsp;ext,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                               <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buf)</pre>
<div class="block">Encodes an image into a memory buffer.

 The function imencode compresses the image and stores it in the memory buffer that is resized to fit the
 result. See cv::imwrite for the list of supported formats and flags description.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>ext</code> - File extension that defines the output format. Must include a leading period.</dd>
<dd><code>img</code> - Image to be written.</dd>
<dd><code>buf</code> - Output buffer resized to fit the compressed image.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="imencode-java.lang.String-org.opencv.core.Mat-org.opencv.core.MatOfByte-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imencode</h4>
<pre>public static&nbsp;boolean&nbsp;imencode(java.lang.String&nbsp;ext,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                               <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buf,
                               <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</pre>
<div class="block">Encodes an image into a memory buffer.

 The function imencode compresses the image and stores it in the memory buffer that is resized to fit the
 result. See cv::imwrite for the list of supported formats and flags description.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>ext</code> - File extension that defines the output format. Must include a leading period.</dd>
<dd><code>img</code> - Image to be written.</dd>
<dd><code>buf</code> - Output buffer resized to fit the compressed image.</dd>
<dd><code>params</code> - Format-specific parameters. See cv::imwrite and cv::ImwriteFlags.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="imread-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imread</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;imread(java.lang.String&nbsp;filename)</pre>
<div class="block">Loads an image from a file.

  imread

 The function imread loads an image from the specified file and returns it. If the image cannot be
 read (because of missing file, improper permissions, unsupported or invalid format), the function
 returns an empty matrix ( Mat::data==NULL ).

 Currently, the following file formats are supported:

 <ul>
   <li>
    Windows bitmaps - \*.bmp, \*.dib (always supported)
   </li>
   <li>
    JPEG files - \*.jpeg, \*.jpg, \*.jpe (see the *Note* section)
   </li>
   <li>
    JPEG 2000 files - \*.jp2 (see the *Note* section)
   </li>
   <li>
    Portable Network Graphics - \*.png (see the *Note* section)
   </li>
   <li>
    WebP - \*.webp (see the *Note* section)
   </li>
   <li>
    AVIF - \*.avif (see the *Note* section)
   </li>
   <li>
    Portable image format - \*.pbm, \*.pgm, \*.ppm \*.pxm, \*.pnm (always supported)
   </li>
   <li>
    PFM files - \*.pfm (see the *Note* section)
   </li>
   <li>
    Sun rasters - \*.sr, \*.ras (always supported)
   </li>
   <li>
    TIFF files - \*.tiff, \*.tif (see the *Note* section)
   </li>
   <li>
    OpenEXR Image files - \*.exr (see the *Note* section)
   </li>
   <li>
    Radiance HDR - \*.hdr, \*.pic (always supported)
   </li>
   <li>
    Raster and Vector geospatial data supported by GDAL (see the *Note* section)
   </li>
 </ul>

 <b>Note:</b>
 <ul>
   <li>
    The function determines the type of an image by the content, not by the file extension.
   </li>
   <li>
    In the case of color images, the decoded images will have the channels stored in <b>B G R</b> order.
   </li>
   <li>
    When using IMREAD_GRAYSCALE, the codec's internal grayscale conversion will be used, if available.
     Results may differ to the output of cvtColor()
   </li>
   <li>
    On Microsoft Windows\* OS and MacOSX\*, the codecs shipped with an OpenCV image (libjpeg,
     libpng, libtiff, and libjasper) are used by default. So, OpenCV can always read JPEGs, PNGs,
     and TIFFs. On MacOSX, there is also an option to use native MacOSX image readers. But beware
     that currently these native image loaders give images with different pixel values because of
     the color management embedded into MacOSX.
   </li>
   <li>
    On Linux\*, BSD flavors and other Unix-like open-source operating systems, OpenCV looks for
     codecs supplied with an OS image. Install the relevant packages (do not forget the development
     files, for example, "libjpeg-dev", in Debian\* and Ubuntu\*) to get the codec support or turn
     on the OPENCV_BUILD_3RDPARTY_LIBS flag in CMake.
   </li>
   <li>
    In the case you set *WITH_GDAL* flag to true in CMake and REF: IMREAD_LOAD_GDAL to load the image,
     then the [GDAL](http://www.gdal.org) driver will be used in order to decode the image, supporting
     the following formats: [Raster](http://www.gdal.org/formats_list.html),
     [Vector](http://www.gdal.org/ogr_formats.html).
   </li>
   <li>
    If EXIF information is embedded in the image file, the EXIF orientation will be taken into account
     and thus the image will be rotated accordingly except if the flags REF: IMREAD_IGNORE_ORIENTATION
     or REF: IMREAD_UNCHANGED are passed.
   </li>
   <li>
    Use the IMREAD_UNCHANGED flag to keep the floating point values from PFM image.
   </li>
   <li>
    By default number of pixels must be less than 2^30. Limit can be set using system
     variable OPENCV_IO_MAX_IMAGE_PIXELS
   </li>
 </ul></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - Name of file to be loaded.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="imread-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imread</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;imread(java.lang.String&nbsp;filename,
                         int&nbsp;flags)</pre>
<div class="block">Loads an image from a file.

  imread

 The function imread loads an image from the specified file and returns it. If the image cannot be
 read (because of missing file, improper permissions, unsupported or invalid format), the function
 returns an empty matrix ( Mat::data==NULL ).

 Currently, the following file formats are supported:

 <ul>
   <li>
    Windows bitmaps - \*.bmp, \*.dib (always supported)
   </li>
   <li>
    JPEG files - \*.jpeg, \*.jpg, \*.jpe (see the *Note* section)
   </li>
   <li>
    JPEG 2000 files - \*.jp2 (see the *Note* section)
   </li>
   <li>
    Portable Network Graphics - \*.png (see the *Note* section)
   </li>
   <li>
    WebP - \*.webp (see the *Note* section)
   </li>
   <li>
    AVIF - \*.avif (see the *Note* section)
   </li>
   <li>
    Portable image format - \*.pbm, \*.pgm, \*.ppm \*.pxm, \*.pnm (always supported)
   </li>
   <li>
    PFM files - \*.pfm (see the *Note* section)
   </li>
   <li>
    Sun rasters - \*.sr, \*.ras (always supported)
   </li>
   <li>
    TIFF files - \*.tiff, \*.tif (see the *Note* section)
   </li>
   <li>
    OpenEXR Image files - \*.exr (see the *Note* section)
   </li>
   <li>
    Radiance HDR - \*.hdr, \*.pic (always supported)
   </li>
   <li>
    Raster and Vector geospatial data supported by GDAL (see the *Note* section)
   </li>
 </ul>

 <b>Note:</b>
 <ul>
   <li>
    The function determines the type of an image by the content, not by the file extension.
   </li>
   <li>
    In the case of color images, the decoded images will have the channels stored in <b>B G R</b> order.
   </li>
   <li>
    When using IMREAD_GRAYSCALE, the codec's internal grayscale conversion will be used, if available.
     Results may differ to the output of cvtColor()
   </li>
   <li>
    On Microsoft Windows\* OS and MacOSX\*, the codecs shipped with an OpenCV image (libjpeg,
     libpng, libtiff, and libjasper) are used by default. So, OpenCV can always read JPEGs, PNGs,
     and TIFFs. On MacOSX, there is also an option to use native MacOSX image readers. But beware
     that currently these native image loaders give images with different pixel values because of
     the color management embedded into MacOSX.
   </li>
   <li>
    On Linux\*, BSD flavors and other Unix-like open-source operating systems, OpenCV looks for
     codecs supplied with an OS image. Install the relevant packages (do not forget the development
     files, for example, "libjpeg-dev", in Debian\* and Ubuntu\*) to get the codec support or turn
     on the OPENCV_BUILD_3RDPARTY_LIBS flag in CMake.
   </li>
   <li>
    In the case you set *WITH_GDAL* flag to true in CMake and REF: IMREAD_LOAD_GDAL to load the image,
     then the [GDAL](http://www.gdal.org) driver will be used in order to decode the image, supporting
     the following formats: [Raster](http://www.gdal.org/formats_list.html),
     [Vector](http://www.gdal.org/ogr_formats.html).
   </li>
   <li>
    If EXIF information is embedded in the image file, the EXIF orientation will be taken into account
     and thus the image will be rotated accordingly except if the flags REF: IMREAD_IGNORE_ORIENTATION
     or REF: IMREAD_UNCHANGED are passed.
   </li>
   <li>
    Use the IMREAD_UNCHANGED flag to keep the floating point values from PFM image.
   </li>
   <li>
    By default number of pixels must be less than 2^30. Limit can be set using system
     variable OPENCV_IO_MAX_IMAGE_PIXELS
   </li>
 </ul></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - Name of file to be loaded.</dd>
<dd><code>flags</code> - Flag that can take values of cv::ImreadModes</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="imreadmulti-java.lang.String-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imreadmulti</h4>
<pre>public static&nbsp;boolean&nbsp;imreadmulti(java.lang.String&nbsp;filename,
                                  java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</pre>
<div class="block">Loads a multi-page image from a file.

 The function imreadmulti loads a multi-page image from the specified file into a vector of Mat objects.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - Name of file to be loaded.</dd>
<dd><code>mats</code> - A vector of Mat objects holding each page.
 SEE: cv::imread</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="imreadmulti-java.lang.String-java.util.List-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imreadmulti</h4>
<pre>public static&nbsp;boolean&nbsp;imreadmulti(java.lang.String&nbsp;filename,
                                  java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats,
                                  int&nbsp;flags)</pre>
<div class="block">Loads a multi-page image from a file.

 The function imreadmulti loads a multi-page image from the specified file into a vector of Mat objects.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - Name of file to be loaded.</dd>
<dd><code>mats</code> - A vector of Mat objects holding each page.</dd>
<dd><code>flags</code> - Flag that can take values of cv::ImreadModes, default with cv::IMREAD_ANYCOLOR.
 SEE: cv::imread</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="imreadmulti-java.lang.String-java.util.List-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imreadmulti</h4>
<pre>public static&nbsp;boolean&nbsp;imreadmulti(java.lang.String&nbsp;filename,
                                  java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats,
                                  int&nbsp;start,
                                  int&nbsp;count)</pre>
<div class="block">Loads a of images of a multi-page image from a file.

 The function imreadmulti loads a specified range from a multi-page image from the specified file into a vector of Mat objects.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - Name of file to be loaded.</dd>
<dd><code>mats</code> - A vector of Mat objects holding each page.</dd>
<dd><code>start</code> - Start index of the image to load</dd>
<dd><code>count</code> - Count number of images to load
 SEE: cv::imread</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="imreadmulti-java.lang.String-java.util.List-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imreadmulti</h4>
<pre>public static&nbsp;boolean&nbsp;imreadmulti(java.lang.String&nbsp;filename,
                                  java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats,
                                  int&nbsp;start,
                                  int&nbsp;count,
                                  int&nbsp;flags)</pre>
<div class="block">Loads a of images of a multi-page image from a file.

 The function imreadmulti loads a specified range from a multi-page image from the specified file into a vector of Mat objects.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - Name of file to be loaded.</dd>
<dd><code>mats</code> - A vector of Mat objects holding each page.</dd>
<dd><code>start</code> - Start index of the image to load</dd>
<dd><code>count</code> - Count number of images to load</dd>
<dd><code>flags</code> - Flag that can take values of cv::ImreadModes, default with cv::IMREAD_ANYCOLOR.
 SEE: cv::imread</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="imwrite-java.lang.String-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imwrite</h4>
<pre>public static&nbsp;boolean&nbsp;imwrite(java.lang.String&nbsp;filename,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</pre>
<div class="block">Saves an image to a specified file.

 The function imwrite saves the image to the specified file. The image format is chosen based on the
 filename extension (see cv::imread for the list of extensions). In general, only 8-bit unsigned (CV_8U)
 single-channel or 3-channel (with 'BGR' channel order) images
 can be saved using this function, with these exceptions:

 <ul>
   <li>
  With OpenEXR encoder, only 32-bit float (CV_32F) images can be saved.
   <ul>
     <li>
    8-bit unsigned (CV_8U) images are not supported.
     </li>
   </ul>
   <li>
  With Radiance HDR encoder, non 64-bit float (CV_64F) images can be saved.
   <ul>
     <li>
    All images will be converted to 32-bit float (CV_32F).
     </li>
   </ul>
   <li>
  With JPEG 2000 encoder, 8-bit unsigned (CV_8U) and 16-bit unsigned (CV_16U) images can be saved.
   </li>
   <li>
  With PAM encoder, 8-bit unsigned (CV_8U) and 16-bit unsigned (CV_16U) images can be saved.
   </li>
   <li>
  With PNG encoder, 8-bit unsigned (CV_8U) and 16-bit unsigned (CV_16U) images can be saved.
   <ul>
     <li>
    PNG images with an alpha channel can be saved using this function. To do this, create
     8-bit (or 16-bit) 4-channel image BGRA, where the alpha channel goes last. Fully transparent pixels
     should have alpha set to 0, fully opaque pixels should have alpha set to 255/65535 (see the code sample below).
     </li>
   </ul>
   <li>
  With PGM/PPM encoder, 8-bit unsigned (CV_8U) and 16-bit unsigned (CV_16U) images can be saved.
   </li>
   <li>
  With TIFF encoder, 8-bit unsigned (CV_8U), 16-bit unsigned (CV_16U),
                      32-bit float (CV_32F) and 64-bit float (CV_64F) images can be saved.
   <ul>
     <li>
    Multiple images (vector of Mat) can be saved in TIFF format (see the code sample below).
     </li>
     <li>
    32-bit float 3-channel (CV_32FC3) TIFF images will be saved
     using the LogLuv high dynamic range encoding (4 bytes per pixel)
     </li>
   </ul>

 If the image format is not supported, the image will be converted to 8-bit unsigned (CV_8U) and saved that way.
   </li>
 </ul>

 If the format, depth or channel order is different, use
 Mat::convertTo and cv::cvtColor to convert it before saving. Or, use the universal FileStorage I/O
 functions to save the image to XML or YAML format.

 The sample below shows how to create a BGRA image, how to set custom compression parameters and save it to a PNG file.
 It also demonstrates how to save multiple images in a TIFF file:
 INCLUDE: snippets/imgcodecs_imwrite.cpp</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - Name of the file.</dd>
<dd><code>img</code> - (Mat or vector of Mat) Image or Images to be saved.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="imwrite-java.lang.String-org.opencv.core.Mat-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imwrite</h4>
<pre>public static&nbsp;boolean&nbsp;imwrite(java.lang.String&nbsp;filename,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                              <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</pre>
<div class="block">Saves an image to a specified file.

 The function imwrite saves the image to the specified file. The image format is chosen based on the
 filename extension (see cv::imread for the list of extensions). In general, only 8-bit unsigned (CV_8U)
 single-channel or 3-channel (with 'BGR' channel order) images
 can be saved using this function, with these exceptions:

 <ul>
   <li>
  With OpenEXR encoder, only 32-bit float (CV_32F) images can be saved.
   <ul>
     <li>
    8-bit unsigned (CV_8U) images are not supported.
     </li>
   </ul>
   <li>
  With Radiance HDR encoder, non 64-bit float (CV_64F) images can be saved.
   <ul>
     <li>
    All images will be converted to 32-bit float (CV_32F).
     </li>
   </ul>
   <li>
  With JPEG 2000 encoder, 8-bit unsigned (CV_8U) and 16-bit unsigned (CV_16U) images can be saved.
   </li>
   <li>
  With PAM encoder, 8-bit unsigned (CV_8U) and 16-bit unsigned (CV_16U) images can be saved.
   </li>
   <li>
  With PNG encoder, 8-bit unsigned (CV_8U) and 16-bit unsigned (CV_16U) images can be saved.
   <ul>
     <li>
    PNG images with an alpha channel can be saved using this function. To do this, create
     8-bit (or 16-bit) 4-channel image BGRA, where the alpha channel goes last. Fully transparent pixels
     should have alpha set to 0, fully opaque pixels should have alpha set to 255/65535 (see the code sample below).
     </li>
   </ul>
   <li>
  With PGM/PPM encoder, 8-bit unsigned (CV_8U) and 16-bit unsigned (CV_16U) images can be saved.
   </li>
   <li>
  With TIFF encoder, 8-bit unsigned (CV_8U), 16-bit unsigned (CV_16U),
                      32-bit float (CV_32F) and 64-bit float (CV_64F) images can be saved.
   <ul>
     <li>
    Multiple images (vector of Mat) can be saved in TIFF format (see the code sample below).
     </li>
     <li>
    32-bit float 3-channel (CV_32FC3) TIFF images will be saved
     using the LogLuv high dynamic range encoding (4 bytes per pixel)
     </li>
   </ul>

 If the image format is not supported, the image will be converted to 8-bit unsigned (CV_8U) and saved that way.
   </li>
 </ul>

 If the format, depth or channel order is different, use
 Mat::convertTo and cv::cvtColor to convert it before saving. Or, use the universal FileStorage I/O
 functions to save the image to XML or YAML format.

 The sample below shows how to create a BGRA image, how to set custom compression parameters and save it to a PNG file.
 It also demonstrates how to save multiple images in a TIFF file:
 INCLUDE: snippets/imgcodecs_imwrite.cpp</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - Name of the file.</dd>
<dd><code>img</code> - (Mat or vector of Mat) Image or Images to be saved.</dd>
<dd><code>params</code> - Format-specific parameters encoded as pairs (paramId_1, paramValue_1, paramId_2, paramValue_2, ... .) see cv::ImwriteFlags</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="imwritemulti-java.lang.String-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imwritemulti</h4>
<pre>public static&nbsp;boolean&nbsp;imwritemulti(java.lang.String&nbsp;filename,
                                   java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;img)</pre>
</li>
</ul>
<a name="imwritemulti-java.lang.String-java.util.List-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>imwritemulti</h4>
<pre>public static&nbsp;boolean&nbsp;imwritemulti(java.lang.String&nbsp;filename,
                                   java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;img,
                                   <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/imgcodecs/Imgcodecs.html" target="_top">Frames</a></li>
<li><a href="Imgcodecs.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
