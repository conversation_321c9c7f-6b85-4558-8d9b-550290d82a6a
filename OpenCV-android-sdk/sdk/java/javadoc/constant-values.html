<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_312) on Wed Jun 28 12:47:25 UTC 2023 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Constant Field Values (OpenCV 4.8.0 Java documentation)</title>
<meta name="date" content="2023-06-28">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Constant Field Values (OpenCV 4.8.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">Frames</a></li>
<li><a href="constant-values.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Constant Field Values" class="title">Constant Field Values</h1>
<h2 title="Contents">Contents</h2>
<ul>
<li><a href="#org.opencv">org.opencv.*</a></li>
</ul>
</div>
<div class="constantValuesContainer"><a name="org.opencv">
<!--   -->
</a>
<h2 title="org.opencv">org.opencv.*</h2>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.android.<a href="org/opencv/android/CameraBridgeViewBase.html" title="class in org.opencv.android">CameraBridgeViewBase</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.android.CameraBridgeViewBase.CAMERA_ID_ANY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/android/CameraBridgeViewBase.html#CAMERA_ID_ANY">CAMERA_ID_ANY</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.android.CameraBridgeViewBase.CAMERA_ID_BACK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/android/CameraBridgeViewBase.html#CAMERA_ID_BACK">CAMERA_ID_BACK</a></code></td>
<td class="colLast"><code>99</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.android.CameraBridgeViewBase.CAMERA_ID_FRONT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/android/CameraBridgeViewBase.html#CAMERA_ID_FRONT">CAMERA_ID_FRONT</a></code></td>
<td class="colLast"><code>98</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.android.CameraBridgeViewBase.GRAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/android/CameraBridgeViewBase.html#GRAY">GRAY</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.android.CameraBridgeViewBase.RGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/android/CameraBridgeViewBase.html#RGBA">RGBA</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.android.<a href="org/opencv/android/CameraRenderer.html" title="class in org.opencv.android">CameraRenderer</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.android.CameraRenderer.LOGTAG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="org/opencv/android/CameraRenderer.html#LOGTAG">LOGTAG</a></code></td>
<td class="colLast"><code>"CameraRenderer"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.android.<a href="org/opencv/android/InstallCallbackInterface.html" title="interface in org.opencv.android">InstallCallbackInterface</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.android.InstallCallbackInterface.INSTALLATION_PROGRESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/android/InstallCallbackInterface.html#INSTALLATION_PROGRESS">INSTALLATION_PROGRESS</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.android.InstallCallbackInterface.NEW_INSTALLATION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/android/InstallCallbackInterface.html#NEW_INSTALLATION">NEW_INSTALLATION</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.android.<a href="org/opencv/android/LoaderCallbackInterface.html" title="interface in org.opencv.android">LoaderCallbackInterface</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.android.LoaderCallbackInterface.INCOMPATIBLE_MANAGER_VERSION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/android/LoaderCallbackInterface.html#INCOMPATIBLE_MANAGER_VERSION">INCOMPATIBLE_MANAGER_VERSION</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.android.LoaderCallbackInterface.INIT_FAILED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/android/LoaderCallbackInterface.html#INIT_FAILED">INIT_FAILED</a></code></td>
<td class="colLast"><code>255</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.android.LoaderCallbackInterface.INSTALL_CANCELED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/android/LoaderCallbackInterface.html#INSTALL_CANCELED">INSTALL_CANCELED</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.android.LoaderCallbackInterface.MARKET_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/android/LoaderCallbackInterface.html#MARKET_ERROR">MARKET_ERROR</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.android.LoaderCallbackInterface.SUCCESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/android/LoaderCallbackInterface.html#SUCCESS">SUCCESS</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.android.<a href="org/opencv/android/OpenCVLoader.html" title="class in org.opencv.android">OpenCVLoader</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.android.OpenCVLoader.OPENCV_VERSION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="org/opencv/android/OpenCVLoader.html#OPENCV_VERSION">OPENCV_VERSION</a></code></td>
<td class="colLast"><code>"4.8.0"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_10">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_10">OPENCV_VERSION_2_4_10</a></code></td>
<td class="colLast"><code>"2.4.10"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_11">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_11">OPENCV_VERSION_2_4_11</a></code></td>
<td class="colLast"><code>"2.4.11"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_12">OPENCV_VERSION_2_4_12</a></code></td>
<td class="colLast"><code>"2.4.12"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_13">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_13">OPENCV_VERSION_2_4_13</a></code></td>
<td class="colLast"><code>"2.4.13"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_2">OPENCV_VERSION_2_4_2</a></code></td>
<td class="colLast"><code>"2.4.2"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_3">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_3">OPENCV_VERSION_2_4_3</a></code></td>
<td class="colLast"><code>"2.4.3"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_4">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_4">OPENCV_VERSION_2_4_4</a></code></td>
<td class="colLast"><code>"2.4.4"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_5">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_5">OPENCV_VERSION_2_4_5</a></code></td>
<td class="colLast"><code>"2.4.5"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_6">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_6">OPENCV_VERSION_2_4_6</a></code></td>
<td class="colLast"><code>"2.4.6"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_7">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_7">OPENCV_VERSION_2_4_7</a></code></td>
<td class="colLast"><code>"2.4.7"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_8">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_8">OPENCV_VERSION_2_4_8</a></code></td>
<td class="colLast"><code>"2.4.8"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.android.OpenCVLoader.OPENCV_VERSION_2_4_9">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_2_4_9">OPENCV_VERSION_2_4_9</a></code></td>
<td class="colLast"><code>"2.4.9"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.android.OpenCVLoader.OPENCV_VERSION_3_0_0">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_3_0_0">OPENCV_VERSION_3_0_0</a></code></td>
<td class="colLast"><code>"3.0.0"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.android.OpenCVLoader.OPENCV_VERSION_3_1_0">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_3_1_0">OPENCV_VERSION_3_1_0</a></code></td>
<td class="colLast"><code>"3.1.0"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.android.OpenCVLoader.OPENCV_VERSION_3_2_0">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_3_2_0">OPENCV_VERSION_3_2_0</a></code></td>
<td class="colLast"><code>"3.2.0"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.android.OpenCVLoader.OPENCV_VERSION_3_3_0">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_3_3_0">OPENCV_VERSION_3_3_0</a></code></td>
<td class="colLast"><code>"3.3.0"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.android.OpenCVLoader.OPENCV_VERSION_3_4_0">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="org/opencv/android/OpenCVLoader.html#OPENCV_VERSION_3_4_0">OPENCV_VERSION_3_4_0</a></code></td>
<td class="colLast"><code>"3.4.0"</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.calib3d.<a href="org/opencv/calib3d/Calib3d.html" title="class in org.opencv.calib3d">Calib3d</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_CB_ACCURACY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_CB_ACCURACY">CALIB_CB_ACCURACY</a></code></td>
<td class="colLast"><code>32</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_CB_ADAPTIVE_THRESH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_CB_ADAPTIVE_THRESH">CALIB_CB_ADAPTIVE_THRESH</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_CB_ASYMMETRIC_GRID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_CB_ASYMMETRIC_GRID">CALIB_CB_ASYMMETRIC_GRID</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_CB_CLUSTERING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_CB_CLUSTERING">CALIB_CB_CLUSTERING</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_CB_EXHAUSTIVE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_CB_EXHAUSTIVE">CALIB_CB_EXHAUSTIVE</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_CB_FAST_CHECK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_CB_FAST_CHECK">CALIB_CB_FAST_CHECK</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_CB_FILTER_QUADS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_CB_FILTER_QUADS">CALIB_CB_FILTER_QUADS</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_CB_LARGER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_CB_LARGER">CALIB_CB_LARGER</a></code></td>
<td class="colLast"><code>64</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_CB_MARKER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_CB_MARKER">CALIB_CB_MARKER</a></code></td>
<td class="colLast"><code>128</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_CB_NORMALIZE_IMAGE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_CB_NORMALIZE_IMAGE">CALIB_CB_NORMALIZE_IMAGE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_CB_SYMMETRIC_GRID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_CB_SYMMETRIC_GRID">CALIB_CB_SYMMETRIC_GRID</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_FIX_ASPECT_RATIO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_FIX_ASPECT_RATIO">CALIB_FIX_ASPECT_RATIO</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_FIX_FOCAL_LENGTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_FIX_FOCAL_LENGTH">CALIB_FIX_FOCAL_LENGTH</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_FIX_INTRINSIC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_FIX_INTRINSIC">CALIB_FIX_INTRINSIC</a></code></td>
<td class="colLast"><code>256</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_FIX_K1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_FIX_K1">CALIB_FIX_K1</a></code></td>
<td class="colLast"><code>32</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_FIX_K2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_FIX_K2">CALIB_FIX_K2</a></code></td>
<td class="colLast"><code>64</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_FIX_K3">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_FIX_K3">CALIB_FIX_K3</a></code></td>
<td class="colLast"><code>128</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_FIX_K4">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_FIX_K4">CALIB_FIX_K4</a></code></td>
<td class="colLast"><code>2048</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_FIX_K5">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_FIX_K5">CALIB_FIX_K5</a></code></td>
<td class="colLast"><code>4096</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_FIX_K6">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_FIX_K6">CALIB_FIX_K6</a></code></td>
<td class="colLast"><code>8192</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_FIX_PRINCIPAL_POINT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_FIX_PRINCIPAL_POINT">CALIB_FIX_PRINCIPAL_POINT</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_FIX_S1_S2_S3_S4">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_FIX_S1_S2_S3_S4">CALIB_FIX_S1_S2_S3_S4</a></code></td>
<td class="colLast"><code>65536</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_FIX_TANGENT_DIST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_FIX_TANGENT_DIST">CALIB_FIX_TANGENT_DIST</a></code></td>
<td class="colLast"><code>2097152</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_FIX_TAUX_TAUY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_FIX_TAUX_TAUY">CALIB_FIX_TAUX_TAUY</a></code></td>
<td class="colLast"><code>524288</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_HAND_EYE_ANDREFF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_HAND_EYE_ANDREFF">CALIB_HAND_EYE_ANDREFF</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_HAND_EYE_DANIILIDIS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_HAND_EYE_DANIILIDIS">CALIB_HAND_EYE_DANIILIDIS</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_HAND_EYE_HORAUD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_HAND_EYE_HORAUD">CALIB_HAND_EYE_HORAUD</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_HAND_EYE_PARK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_HAND_EYE_PARK">CALIB_HAND_EYE_PARK</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_HAND_EYE_TSAI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_HAND_EYE_TSAI">CALIB_HAND_EYE_TSAI</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_NINTRINSIC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_NINTRINSIC">CALIB_NINTRINSIC</a></code></td>
<td class="colLast"><code>18</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_RATIONAL_MODEL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_RATIONAL_MODEL">CALIB_RATIONAL_MODEL</a></code></td>
<td class="colLast"><code>16384</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_ROBOT_WORLD_HAND_EYE_LI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_ROBOT_WORLD_HAND_EYE_LI">CALIB_ROBOT_WORLD_HAND_EYE_LI</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_ROBOT_WORLD_HAND_EYE_SHAH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_ROBOT_WORLD_HAND_EYE_SHAH">CALIB_ROBOT_WORLD_HAND_EYE_SHAH</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_SAME_FOCAL_LENGTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_SAME_FOCAL_LENGTH">CALIB_SAME_FOCAL_LENGTH</a></code></td>
<td class="colLast"><code>512</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_THIN_PRISM_MODEL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_THIN_PRISM_MODEL">CALIB_THIN_PRISM_MODEL</a></code></td>
<td class="colLast"><code>32768</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_TILTED_MODEL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_TILTED_MODEL">CALIB_TILTED_MODEL</a></code></td>
<td class="colLast"><code>262144</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_USE_EXTRINSIC_GUESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_USE_EXTRINSIC_GUESS">CALIB_USE_EXTRINSIC_GUESS</a></code></td>
<td class="colLast"><code>4194304</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_USE_INTRINSIC_GUESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_USE_INTRINSIC_GUESS">CALIB_USE_INTRINSIC_GUESS</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_USE_LU">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_USE_LU">CALIB_USE_LU</a></code></td>
<td class="colLast"><code>131072</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_USE_QR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_USE_QR">CALIB_USE_QR</a></code></td>
<td class="colLast"><code>1048576</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_ZERO_DISPARITY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_ZERO_DISPARITY">CALIB_ZERO_DISPARITY</a></code></td>
<td class="colLast"><code>1024</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CALIB_ZERO_TANGENT_DIST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CALIB_ZERO_TANGENT_DIST">CALIB_ZERO_TANGENT_DIST</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CirclesGridFinderParameters_ASYMMETRIC_GRID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CirclesGridFinderParameters_ASYMMETRIC_GRID">CirclesGridFinderParameters_ASYMMETRIC_GRID</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CirclesGridFinderParameters_SYMMETRIC_GRID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CirclesGridFinderParameters_SYMMETRIC_GRID">CirclesGridFinderParameters_SYMMETRIC_GRID</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.COV_POLISHER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#COV_POLISHER">COV_POLISHER</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CV_DLS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CV_DLS">CV_DLS</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CV_EPNP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CV_EPNP">CV_EPNP</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CV_ITERATIVE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CV_ITERATIVE">CV_ITERATIVE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CV_P3P">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CV_P3P">CV_P3P</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CvLevMarq_CALC_J">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CvLevMarq_CALC_J">CvLevMarq_CALC_J</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CvLevMarq_CHECK_ERR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CvLevMarq_CHECK_ERR">CvLevMarq_CHECK_ERR</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CvLevMarq_DONE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CvLevMarq_DONE">CvLevMarq_DONE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.CvLevMarq_STARTED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#CvLevMarq_STARTED">CvLevMarq_STARTED</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.fisheye_CALIB_CHECK_COND">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#fisheye_CALIB_CHECK_COND">fisheye_CALIB_CHECK_COND</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.fisheye_CALIB_FIX_FOCAL_LENGTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#fisheye_CALIB_FIX_FOCAL_LENGTH">fisheye_CALIB_FIX_FOCAL_LENGTH</a></code></td>
<td class="colLast"><code>2048</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.fisheye_CALIB_FIX_INTRINSIC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#fisheye_CALIB_FIX_INTRINSIC">fisheye_CALIB_FIX_INTRINSIC</a></code></td>
<td class="colLast"><code>256</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.fisheye_CALIB_FIX_K1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#fisheye_CALIB_FIX_K1">fisheye_CALIB_FIX_K1</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.fisheye_CALIB_FIX_K2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#fisheye_CALIB_FIX_K2">fisheye_CALIB_FIX_K2</a></code></td>
<td class="colLast"><code>32</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.fisheye_CALIB_FIX_K3">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#fisheye_CALIB_FIX_K3">fisheye_CALIB_FIX_K3</a></code></td>
<td class="colLast"><code>64</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.fisheye_CALIB_FIX_K4">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#fisheye_CALIB_FIX_K4">fisheye_CALIB_FIX_K4</a></code></td>
<td class="colLast"><code>128</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.fisheye_CALIB_FIX_PRINCIPAL_POINT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#fisheye_CALIB_FIX_PRINCIPAL_POINT">fisheye_CALIB_FIX_PRINCIPAL_POINT</a></code></td>
<td class="colLast"><code>512</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.fisheye_CALIB_FIX_SKEW">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#fisheye_CALIB_FIX_SKEW">fisheye_CALIB_FIX_SKEW</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.fisheye_CALIB_RECOMPUTE_EXTRINSIC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#fisheye_CALIB_RECOMPUTE_EXTRINSIC">fisheye_CALIB_RECOMPUTE_EXTRINSIC</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.fisheye_CALIB_USE_INTRINSIC_GUESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#fisheye_CALIB_USE_INTRINSIC_GUESS">fisheye_CALIB_USE_INTRINSIC_GUESS</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.fisheye_CALIB_ZERO_DISPARITY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#fisheye_CALIB_ZERO_DISPARITY">fisheye_CALIB_ZERO_DISPARITY</a></code></td>
<td class="colLast"><code>1024</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.FM_7POINT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#FM_7POINT">FM_7POINT</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.FM_8POINT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#FM_8POINT">FM_8POINT</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.FM_LMEDS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#FM_LMEDS">FM_LMEDS</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.FM_RANSAC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#FM_RANSAC">FM_RANSAC</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.LMEDS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#LMEDS">LMEDS</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.LOCAL_OPTIM_GC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#LOCAL_OPTIM_GC">LOCAL_OPTIM_GC</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.LOCAL_OPTIM_INNER_AND_ITER_LO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#LOCAL_OPTIM_INNER_AND_ITER_LO">LOCAL_OPTIM_INNER_AND_ITER_LO</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.LOCAL_OPTIM_INNER_LO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#LOCAL_OPTIM_INNER_LO">LOCAL_OPTIM_INNER_LO</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.LOCAL_OPTIM_NULL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#LOCAL_OPTIM_NULL">LOCAL_OPTIM_NULL</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.LOCAL_OPTIM_SIGMA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#LOCAL_OPTIM_SIGMA">LOCAL_OPTIM_SIGMA</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.LSQ_POLISHER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#LSQ_POLISHER">LSQ_POLISHER</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.MAGSAC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#MAGSAC">MAGSAC</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.NEIGH_FLANN_KNN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#NEIGH_FLANN_KNN">NEIGH_FLANN_KNN</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.NEIGH_FLANN_RADIUS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#NEIGH_FLANN_RADIUS">NEIGH_FLANN_RADIUS</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.NEIGH_GRID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#NEIGH_GRID">NEIGH_GRID</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.NONE_POLISHER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#NONE_POLISHER">NONE_POLISHER</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.PROJ_SPHERICAL_EQRECT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#PROJ_SPHERICAL_EQRECT">PROJ_SPHERICAL_EQRECT</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.PROJ_SPHERICAL_ORTHO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#PROJ_SPHERICAL_ORTHO">PROJ_SPHERICAL_ORTHO</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.RANSAC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#RANSAC">RANSAC</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.RHO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#RHO">RHO</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.SAMPLING_NAPSAC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#SAMPLING_NAPSAC">SAMPLING_NAPSAC</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.SAMPLING_PROGRESSIVE_NAPSAC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#SAMPLING_PROGRESSIVE_NAPSAC">SAMPLING_PROGRESSIVE_NAPSAC</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.SAMPLING_PROSAC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#SAMPLING_PROSAC">SAMPLING_PROSAC</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.SAMPLING_UNIFORM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#SAMPLING_UNIFORM">SAMPLING_UNIFORM</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.SCORE_METHOD_LMEDS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#SCORE_METHOD_LMEDS">SCORE_METHOD_LMEDS</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.SCORE_METHOD_MAGSAC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#SCORE_METHOD_MAGSAC">SCORE_METHOD_MAGSAC</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.SCORE_METHOD_MSAC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#SCORE_METHOD_MSAC">SCORE_METHOD_MSAC</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.SCORE_METHOD_RANSAC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#SCORE_METHOD_RANSAC">SCORE_METHOD_RANSAC</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.SOLVEPNP_AP3P">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#SOLVEPNP_AP3P">SOLVEPNP_AP3P</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.SOLVEPNP_DLS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#SOLVEPNP_DLS">SOLVEPNP_DLS</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.SOLVEPNP_EPNP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#SOLVEPNP_EPNP">SOLVEPNP_EPNP</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.SOLVEPNP_IPPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#SOLVEPNP_IPPE">SOLVEPNP_IPPE</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.SOLVEPNP_IPPE_SQUARE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#SOLVEPNP_IPPE_SQUARE">SOLVEPNP_IPPE_SQUARE</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.SOLVEPNP_ITERATIVE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#SOLVEPNP_ITERATIVE">SOLVEPNP_ITERATIVE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.SOLVEPNP_MAX_COUNT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#SOLVEPNP_MAX_COUNT">SOLVEPNP_MAX_COUNT</a></code></td>
<td class="colLast"><code>9</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.SOLVEPNP_P3P">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#SOLVEPNP_P3P">SOLVEPNP_P3P</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.SOLVEPNP_SQPNP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#SOLVEPNP_SQPNP">SOLVEPNP_SQPNP</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.SOLVEPNP_UPNP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#SOLVEPNP_UPNP">SOLVEPNP_UPNP</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.USAC_ACCURATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#USAC_ACCURATE">USAC_ACCURATE</a></code></td>
<td class="colLast"><code>36</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.USAC_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#USAC_DEFAULT">USAC_DEFAULT</a></code></td>
<td class="colLast"><code>32</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.USAC_FAST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#USAC_FAST">USAC_FAST</a></code></td>
<td class="colLast"><code>35</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.USAC_FM_8PTS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#USAC_FM_8PTS">USAC_FM_8PTS</a></code></td>
<td class="colLast"><code>34</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.USAC_MAGSAC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#USAC_MAGSAC">USAC_MAGSAC</a></code></td>
<td class="colLast"><code>38</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.USAC_PARALLEL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#USAC_PARALLEL">USAC_PARALLEL</a></code></td>
<td class="colLast"><code>33</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.Calib3d.USAC_PROSAC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/Calib3d.html#USAC_PROSAC">USAC_PROSAC</a></code></td>
<td class="colLast"><code>37</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.calib3d.<a href="org/opencv/calib3d/StereoBM.html" title="class in org.opencv.calib3d">StereoBM</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.StereoBM.PREFILTER_NORMALIZED_RESPONSE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/StereoBM.html#PREFILTER_NORMALIZED_RESPONSE">PREFILTER_NORMALIZED_RESPONSE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.StereoBM.PREFILTER_XSOBEL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/StereoBM.html#PREFILTER_XSOBEL">PREFILTER_XSOBEL</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.calib3d.<a href="org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d">StereoMatcher</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.StereoMatcher.DISP_SCALE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/StereoMatcher.html#DISP_SCALE">DISP_SCALE</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.StereoMatcher.DISP_SHIFT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/StereoMatcher.html#DISP_SHIFT">DISP_SHIFT</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.calib3d.<a href="org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.StereoSGBM.MODE_HH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/StereoSGBM.html#MODE_HH">MODE_HH</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.StereoSGBM.MODE_HH4">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/StereoSGBM.html#MODE_HH4">MODE_HH4</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.calib3d.StereoSGBM.MODE_SGBM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/StereoSGBM.html#MODE_SGBM">MODE_SGBM</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.calib3d.StereoSGBM.MODE_SGBM_3WAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/calib3d/StereoSGBM.html#MODE_SGBM_3WAY">MODE_SGBM_3WAY</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.core.<a href="org/opencv/core/Core.html" title="class in org.opencv.core">Core</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.BadAlign">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BadAlign">BadAlign</a></code></td>
<td class="colLast"><code>-21</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.BadAlphaChannel">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BadAlphaChannel">BadAlphaChannel</a></code></td>
<td class="colLast"><code>-18</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.BadCallBack">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BadCallBack">BadCallBack</a></code></td>
<td class="colLast"><code>-22</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.BadCOI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BadCOI">BadCOI</a></code></td>
<td class="colLast"><code>-24</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.BadDataPtr">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BadDataPtr">BadDataPtr</a></code></td>
<td class="colLast"><code>-12</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.BadDepth">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BadDepth">BadDepth</a></code></td>
<td class="colLast"><code>-17</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.BadImageSize">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BadImageSize">BadImageSize</a></code></td>
<td class="colLast"><code>-10</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.BadModelOrChSeq">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BadModelOrChSeq">BadModelOrChSeq</a></code></td>
<td class="colLast"><code>-14</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.BadNumChannel1U">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BadNumChannel1U">BadNumChannel1U</a></code></td>
<td class="colLast"><code>-16</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.BadNumChannels">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BadNumChannels">BadNumChannels</a></code></td>
<td class="colLast"><code>-15</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.BadOffset">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BadOffset">BadOffset</a></code></td>
<td class="colLast"><code>-11</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.BadOrder">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BadOrder">BadOrder</a></code></td>
<td class="colLast"><code>-19</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.BadOrigin">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BadOrigin">BadOrigin</a></code></td>
<td class="colLast"><code>-20</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.BadROISize">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BadROISize">BadROISize</a></code></td>
<td class="colLast"><code>-25</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.BadStep">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BadStep">BadStep</a></code></td>
<td class="colLast"><code>-13</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.BadTileSize">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BadTileSize">BadTileSize</a></code></td>
<td class="colLast"><code>-23</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.BORDER_CONSTANT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BORDER_CONSTANT">BORDER_CONSTANT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.BORDER_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BORDER_DEFAULT">BORDER_DEFAULT</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.BORDER_ISOLATED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BORDER_ISOLATED">BORDER_ISOLATED</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.BORDER_REFLECT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BORDER_REFLECT">BORDER_REFLECT</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.BORDER_REFLECT_101">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BORDER_REFLECT_101">BORDER_REFLECT_101</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.BORDER_REFLECT101">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BORDER_REFLECT101">BORDER_REFLECT101</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.BORDER_REPLICATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BORDER_REPLICATE">BORDER_REPLICATE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.BORDER_TRANSPARENT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BORDER_TRANSPARENT">BORDER_TRANSPARENT</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.BORDER_WRAP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#BORDER_WRAP">BORDER_WRAP</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.CMP_EQ">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#CMP_EQ">CMP_EQ</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.CMP_GE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#CMP_GE">CMP_GE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.CMP_GT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#CMP_GT">CMP_GT</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.CMP_LE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#CMP_LE">CMP_LE</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.CMP_LT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#CMP_LT">CMP_LT</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.CMP_NE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#CMP_NE">CMP_NE</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.COVAR_COLS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#COVAR_COLS">COVAR_COLS</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.COVAR_NORMAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#COVAR_NORMAL">COVAR_NORMAL</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.COVAR_ROWS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#COVAR_ROWS">COVAR_ROWS</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.COVAR_SCALE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#COVAR_SCALE">COVAR_SCALE</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.COVAR_SCRAMBLED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#COVAR_SCRAMBLED">COVAR_SCRAMBLED</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.COVAR_USE_AVG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#COVAR_USE_AVG">COVAR_USE_AVG</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.DCT_INVERSE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#DCT_INVERSE">DCT_INVERSE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.DCT_ROWS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#DCT_ROWS">DCT_ROWS</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.DECOMP_CHOLESKY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#DECOMP_CHOLESKY">DECOMP_CHOLESKY</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.DECOMP_EIG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#DECOMP_EIG">DECOMP_EIG</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.DECOMP_LU">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#DECOMP_LU">DECOMP_LU</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.DECOMP_NORMAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#DECOMP_NORMAL">DECOMP_NORMAL</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.DECOMP_QR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#DECOMP_QR">DECOMP_QR</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.DECOMP_SVD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#DECOMP_SVD">DECOMP_SVD</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.DFT_COMPLEX_INPUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#DFT_COMPLEX_INPUT">DFT_COMPLEX_INPUT</a></code></td>
<td class="colLast"><code>64</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.DFT_COMPLEX_OUTPUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#DFT_COMPLEX_OUTPUT">DFT_COMPLEX_OUTPUT</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.DFT_INVERSE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#DFT_INVERSE">DFT_INVERSE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.DFT_REAL_OUTPUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#DFT_REAL_OUTPUT">DFT_REAL_OUTPUT</a></code></td>
<td class="colLast"><code>32</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.DFT_ROWS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#DFT_ROWS">DFT_ROWS</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.DFT_SCALE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#DFT_SCALE">DFT_SCALE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.FILLED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#FILLED">FILLED</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.Formatter_FMT_C">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#Formatter_FMT_C">Formatter_FMT_C</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.Formatter_FMT_CSV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#Formatter_FMT_CSV">Formatter_FMT_CSV</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.Formatter_FMT_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#Formatter_FMT_DEFAULT">Formatter_FMT_DEFAULT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.Formatter_FMT_MATLAB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#Formatter_FMT_MATLAB">Formatter_FMT_MATLAB</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.Formatter_FMT_NUMPY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#Formatter_FMT_NUMPY">Formatter_FMT_NUMPY</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.Formatter_FMT_PYTHON">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#Formatter_FMT_PYTHON">Formatter_FMT_PYTHON</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.GEMM_1_T">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#GEMM_1_T">GEMM_1_T</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.GEMM_2_T">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#GEMM_2_T">GEMM_2_T</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.GEMM_3_T">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#GEMM_3_T">GEMM_3_T</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.GpuApiCallError">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#GpuApiCallError">GpuApiCallError</a></code></td>
<td class="colLast"><code>-217</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.GpuNotSupported">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#GpuNotSupported">GpuNotSupported</a></code></td>
<td class="colLast"><code>-216</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.HeaderIsNull">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#HeaderIsNull">HeaderIsNull</a></code></td>
<td class="colLast"><code>-9</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.KMEANS_PP_CENTERS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#KMEANS_PP_CENTERS">KMEANS_PP_CENTERS</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.KMEANS_RANDOM_CENTERS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#KMEANS_RANDOM_CENTERS">KMEANS_RANDOM_CENTERS</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.KMEANS_USE_INITIAL_LABELS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#KMEANS_USE_INITIAL_LABELS">KMEANS_USE_INITIAL_LABELS</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.MaskIsTiled">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#MaskIsTiled">MaskIsTiled</a></code></td>
<td class="colLast"><code>-26</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.NORM_HAMMING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#NORM_HAMMING">NORM_HAMMING</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.NORM_HAMMING2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#NORM_HAMMING2">NORM_HAMMING2</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.NORM_INF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#NORM_INF">NORM_INF</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.NORM_L1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#NORM_L1">NORM_L1</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.NORM_L2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#NORM_L2">NORM_L2</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.NORM_L2SQR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#NORM_L2SQR">NORM_L2SQR</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.NORM_MINMAX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#NORM_MINMAX">NORM_MINMAX</a></code></td>
<td class="colLast"><code>32</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.NORM_RELATIVE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#NORM_RELATIVE">NORM_RELATIVE</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.NORM_TYPE_MASK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#NORM_TYPE_MASK">NORM_TYPE_MASK</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.OpenCLApiCallError">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#OpenCLApiCallError">OpenCLApiCallError</a></code></td>
<td class="colLast"><code>-220</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.OpenCLDoubleNotSupported">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#OpenCLDoubleNotSupported">OpenCLDoubleNotSupported</a></code></td>
<td class="colLast"><code>-221</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.OpenCLInitError">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#OpenCLInitError">OpenCLInitError</a></code></td>
<td class="colLast"><code>-222</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.OpenCLNoAMDBlasFft">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#OpenCLNoAMDBlasFft">OpenCLNoAMDBlasFft</a></code></td>
<td class="colLast"><code>-223</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.OpenGlApiCallError">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#OpenGlApiCallError">OpenGlApiCallError</a></code></td>
<td class="colLast"><code>-219</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.OpenGlNotSupported">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#OpenGlNotSupported">OpenGlNotSupported</a></code></td>
<td class="colLast"><code>-218</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.Param_ALGORITHM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#Param_ALGORITHM">Param_ALGORITHM</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.Param_BOOLEAN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#Param_BOOLEAN">Param_BOOLEAN</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.Param_FLOAT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#Param_FLOAT">Param_FLOAT</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.Param_INT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#Param_INT">Param_INT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.Param_MAT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#Param_MAT">Param_MAT</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.Param_MAT_VECTOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#Param_MAT_VECTOR">Param_MAT_VECTOR</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.Param_REAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#Param_REAL">Param_REAL</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.Param_SCALAR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#Param_SCALAR">Param_SCALAR</a></code></td>
<td class="colLast"><code>12</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.Param_STRING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#Param_STRING">Param_STRING</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.Param_UCHAR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#Param_UCHAR">Param_UCHAR</a></code></td>
<td class="colLast"><code>11</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.Param_UINT64">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#Param_UINT64">Param_UINT64</a></code></td>
<td class="colLast"><code>9</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.Param_UNSIGNED_INT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#Param_UNSIGNED_INT">Param_UNSIGNED_INT</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.PCA_DATA_AS_COL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#PCA_DATA_AS_COL">PCA_DATA_AS_COL</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.PCA_DATA_AS_ROW">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#PCA_DATA_AS_ROW">PCA_DATA_AS_ROW</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.PCA_USE_AVG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#PCA_USE_AVG">PCA_USE_AVG</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.REDUCE_AVG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#REDUCE_AVG">REDUCE_AVG</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.REDUCE_MAX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#REDUCE_MAX">REDUCE_MAX</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.REDUCE_MIN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#REDUCE_MIN">REDUCE_MIN</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.REDUCE_SUM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#REDUCE_SUM">REDUCE_SUM</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.REDUCE_SUM2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#REDUCE_SUM2">REDUCE_SUM2</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.RNG_NORMAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#RNG_NORMAL">RNG_NORMAL</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.RNG_UNIFORM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#RNG_UNIFORM">RNG_UNIFORM</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.ROTATE_180">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#ROTATE_180">ROTATE_180</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.ROTATE_90_CLOCKWISE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#ROTATE_90_CLOCKWISE">ROTATE_90_CLOCKWISE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.ROTATE_90_COUNTERCLOCKWISE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#ROTATE_90_COUNTERCLOCKWISE">ROTATE_90_COUNTERCLOCKWISE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.SORT_ASCENDING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#SORT_ASCENDING">SORT_ASCENDING</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.SORT_DESCENDING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#SORT_DESCENDING">SORT_DESCENDING</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.SORT_EVERY_COLUMN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#SORT_EVERY_COLUMN">SORT_EVERY_COLUMN</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.SORT_EVERY_ROW">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#SORT_EVERY_ROW">SORT_EVERY_ROW</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsAssert">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsAssert">StsAssert</a></code></td>
<td class="colLast"><code>-215</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsAutoTrace">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsAutoTrace">StsAutoTrace</a></code></td>
<td class="colLast"><code>-8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsBackTrace">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsBackTrace">StsBackTrace</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsBadArg">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsBadArg">StsBadArg</a></code></td>
<td class="colLast"><code>-5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsBadFlag">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsBadFlag">StsBadFlag</a></code></td>
<td class="colLast"><code>-206</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsBadFunc">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsBadFunc">StsBadFunc</a></code></td>
<td class="colLast"><code>-6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsBadMask">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsBadMask">StsBadMask</a></code></td>
<td class="colLast"><code>-208</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsBadMemBlock">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsBadMemBlock">StsBadMemBlock</a></code></td>
<td class="colLast"><code>-214</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsBadPoint">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsBadPoint">StsBadPoint</a></code></td>
<td class="colLast"><code>-207</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsBadSize">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsBadSize">StsBadSize</a></code></td>
<td class="colLast"><code>-201</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsDivByZero">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsDivByZero">StsDivByZero</a></code></td>
<td class="colLast"><code>-202</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsError">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsError">StsError</a></code></td>
<td class="colLast"><code>-2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsFilterOffsetErr">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsFilterOffsetErr">StsFilterOffsetErr</a></code></td>
<td class="colLast"><code>-31</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsFilterStructContentErr">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsFilterStructContentErr">StsFilterStructContentErr</a></code></td>
<td class="colLast"><code>-29</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsInplaceNotSupported">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsInplaceNotSupported">StsInplaceNotSupported</a></code></td>
<td class="colLast"><code>-203</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsInternal">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsInternal">StsInternal</a></code></td>
<td class="colLast"><code>-3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsKernelStructContentErr">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsKernelStructContentErr">StsKernelStructContentErr</a></code></td>
<td class="colLast"><code>-30</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsNoConv">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsNoConv">StsNoConv</a></code></td>
<td class="colLast"><code>-7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsNoMem">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsNoMem">StsNoMem</a></code></td>
<td class="colLast"><code>-4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsNotImplemented">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsNotImplemented">StsNotImplemented</a></code></td>
<td class="colLast"><code>-213</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsNullPtr">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsNullPtr">StsNullPtr</a></code></td>
<td class="colLast"><code>-27</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsObjectNotFound">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsObjectNotFound">StsObjectNotFound</a></code></td>
<td class="colLast"><code>-204</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsOk">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsOk">StsOk</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsOutOfRange">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsOutOfRange">StsOutOfRange</a></code></td>
<td class="colLast"><code>-211</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsParseError">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsParseError">StsParseError</a></code></td>
<td class="colLast"><code>-212</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsUnmatchedFormats">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsUnmatchedFormats">StsUnmatchedFormats</a></code></td>
<td class="colLast"><code>-205</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsUnmatchedSizes">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsUnmatchedSizes">StsUnmatchedSizes</a></code></td>
<td class="colLast"><code>-209</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsUnsupportedFormat">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsUnsupportedFormat">StsUnsupportedFormat</a></code></td>
<td class="colLast"><code>-210</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.StsVecLengthErr">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#StsVecLengthErr">StsVecLengthErr</a></code></td>
<td class="colLast"><code>-28</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.SVD_FULL_UV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#SVD_FULL_UV">SVD_FULL_UV</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.Core.SVD_MODIFY_A">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#SVD_MODIFY_A">SVD_MODIFY_A</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.Core.SVD_NO_UV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/Core.html#SVD_NO_UV">SVD_NO_UV</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.core.<a href="org/opencv/core/CvType.html" title="class in org.opencv.core">CvType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.CvType.CV_16F">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/CvType.html#CV_16F">CV_16F</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.CvType.CV_16S">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/CvType.html#CV_16S">CV_16S</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.CvType.CV_16U">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/CvType.html#CV_16U">CV_16U</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.CvType.CV_32F">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/CvType.html#CV_32F">CV_32F</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.CvType.CV_32S">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/CvType.html#CV_32S">CV_32S</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.CvType.CV_64F">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/CvType.html#CV_64F">CV_64F</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.CvType.CV_8S">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/CvType.html#CV_8S">CV_8S</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.CvType.CV_8U">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/CvType.html#CV_8U">CV_8U</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.core.<a href="org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.TermCriteria.COUNT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/TermCriteria.html#COUNT">COUNT</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.core.TermCriteria.EPS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/TermCriteria.html#EPS">EPS</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.core.TermCriteria.MAX_ITER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/core/TermCriteria.html#MAX_ITER">MAX_ITER</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.dnn.<a href="org/opencv/dnn/Dnn.html" title="class in org.opencv.dnn">Dnn</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_BACKEND_CANN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_BACKEND_CANN">DNN_BACKEND_CANN</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_BACKEND_CUDA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_BACKEND_CUDA">DNN_BACKEND_CUDA</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_BACKEND_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_BACKEND_DEFAULT">DNN_BACKEND_DEFAULT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_BACKEND_HALIDE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_BACKEND_HALIDE">DNN_BACKEND_HALIDE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_BACKEND_INFERENCE_ENGINE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_BACKEND_INFERENCE_ENGINE">DNN_BACKEND_INFERENCE_ENGINE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_BACKEND_OPENCV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_BACKEND_OPENCV">DNN_BACKEND_OPENCV</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_BACKEND_TIMVX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_BACKEND_TIMVX">DNN_BACKEND_TIMVX</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_BACKEND_VKCOM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_BACKEND_VKCOM">DNN_BACKEND_VKCOM</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_BACKEND_WEBNN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_BACKEND_WEBNN">DNN_BACKEND_WEBNN</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_LAYOUT_NCDHW">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_LAYOUT_NCDHW">DNN_LAYOUT_NCDHW</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_LAYOUT_NCHW">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_LAYOUT_NCHW">DNN_LAYOUT_NCHW</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_LAYOUT_ND">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_LAYOUT_ND">DNN_LAYOUT_ND</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_LAYOUT_NDHWC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_LAYOUT_NDHWC">DNN_LAYOUT_NDHWC</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_LAYOUT_NHWC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_LAYOUT_NHWC">DNN_LAYOUT_NHWC</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_LAYOUT_PLANAR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_LAYOUT_PLANAR">DNN_LAYOUT_PLANAR</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_LAYOUT_UNKNOWN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_LAYOUT_UNKNOWN">DNN_LAYOUT_UNKNOWN</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_PMODE_CROP_CENTER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_PMODE_CROP_CENTER">DNN_PMODE_CROP_CENTER</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_PMODE_LETTERBOX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_PMODE_LETTERBOX">DNN_PMODE_LETTERBOX</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_PMODE_NULL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_PMODE_NULL">DNN_PMODE_NULL</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_TARGET_CPU">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_TARGET_CPU">DNN_TARGET_CPU</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_TARGET_CPU_FP16">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_TARGET_CPU_FP16">DNN_TARGET_CPU_FP16</a></code></td>
<td class="colLast"><code>10</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_TARGET_CUDA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_TARGET_CUDA">DNN_TARGET_CUDA</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_TARGET_CUDA_FP16">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_TARGET_CUDA_FP16">DNN_TARGET_CUDA_FP16</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_TARGET_FPGA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_TARGET_FPGA">DNN_TARGET_FPGA</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_TARGET_HDDL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_TARGET_HDDL">DNN_TARGET_HDDL</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_TARGET_MYRIAD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_TARGET_MYRIAD">DNN_TARGET_MYRIAD</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_TARGET_NPU">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_TARGET_NPU">DNN_TARGET_NPU</a></code></td>
<td class="colLast"><code>9</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_TARGET_OPENCL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_TARGET_OPENCL">DNN_TARGET_OPENCL</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_TARGET_OPENCL_FP16">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_TARGET_OPENCL_FP16">DNN_TARGET_OPENCL_FP16</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.DNN_TARGET_VULKAN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#DNN_TARGET_VULKAN">DNN_TARGET_VULKAN</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.SoftNMSMethod_SOFTNMS_GAUSSIAN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#SoftNMSMethod_SOFTNMS_GAUSSIAN">SoftNMSMethod_SOFTNMS_GAUSSIAN</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.dnn.Dnn.SoftNMSMethod_SOFTNMS_LINEAR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/dnn/Dnn.html#SoftNMSMethod_SOFTNMS_LINEAR">SoftNMSMethod_SOFTNMS_LINEAR</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.features2d.<a href="org/opencv/features2d/AgastFeatureDetector.html" title="class in org.opencv.features2d">AgastFeatureDetector</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.features2d.AgastFeatureDetector.AGAST_5_8">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/AgastFeatureDetector.html#AGAST_5_8">AGAST_5_8</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.features2d.AgastFeatureDetector.AGAST_7_12d">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/AgastFeatureDetector.html#AGAST_7_12d">AGAST_7_12d</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.features2d.AgastFeatureDetector.AGAST_7_12s">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/AgastFeatureDetector.html#AGAST_7_12s">AGAST_7_12s</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.features2d.AgastFeatureDetector.NONMAX_SUPPRESSION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/AgastFeatureDetector.html#NONMAX_SUPPRESSION">NONMAX_SUPPRESSION</a></code></td>
<td class="colLast"><code>10001</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.features2d.AgastFeatureDetector.OAST_9_16">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/AgastFeatureDetector.html#OAST_9_16">OAST_9_16</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.features2d.AgastFeatureDetector.THRESHOLD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/AgastFeatureDetector.html#THRESHOLD">THRESHOLD</a></code></td>
<td class="colLast"><code>10000</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.features2d.<a href="org/opencv/features2d/AKAZE.html" title="class in org.opencv.features2d">AKAZE</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.features2d.AKAZE.DESCRIPTOR_KAZE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/AKAZE.html#DESCRIPTOR_KAZE">DESCRIPTOR_KAZE</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.features2d.AKAZE.DESCRIPTOR_KAZE_UPRIGHT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/AKAZE.html#DESCRIPTOR_KAZE_UPRIGHT">DESCRIPTOR_KAZE_UPRIGHT</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.features2d.AKAZE.DESCRIPTOR_MLDB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/AKAZE.html#DESCRIPTOR_MLDB">DESCRIPTOR_MLDB</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.features2d.AKAZE.DESCRIPTOR_MLDB_UPRIGHT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/AKAZE.html#DESCRIPTOR_MLDB_UPRIGHT">DESCRIPTOR_MLDB_UPRIGHT</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.features2d.<a href="org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.features2d.DescriptorMatcher.BRUTEFORCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE">BRUTEFORCE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.features2d.DescriptorMatcher.BRUTEFORCE_HAMMING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE_HAMMING">BRUTEFORCE_HAMMING</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.features2d.DescriptorMatcher.BRUTEFORCE_HAMMINGLUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE_HAMMINGLUT">BRUTEFORCE_HAMMINGLUT</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.features2d.DescriptorMatcher.BRUTEFORCE_L1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE_L1">BRUTEFORCE_L1</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.features2d.DescriptorMatcher.BRUTEFORCE_SL2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE_SL2">BRUTEFORCE_SL2</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.features2d.DescriptorMatcher.FLANNBASED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/DescriptorMatcher.html#FLANNBASED">FLANNBASED</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.features2d.<a href="org/opencv/features2d/FastFeatureDetector.html" title="class in org.opencv.features2d">FastFeatureDetector</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.features2d.FastFeatureDetector.FAST_N">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/FastFeatureDetector.html#FAST_N">FAST_N</a></code></td>
<td class="colLast"><code>10002</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.features2d.FastFeatureDetector.NONMAX_SUPPRESSION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/FastFeatureDetector.html#NONMAX_SUPPRESSION">NONMAX_SUPPRESSION</a></code></td>
<td class="colLast"><code>10001</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.features2d.FastFeatureDetector.THRESHOLD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/FastFeatureDetector.html#THRESHOLD">THRESHOLD</a></code></td>
<td class="colLast"><code>10000</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.features2d.FastFeatureDetector.TYPE_5_8">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/FastFeatureDetector.html#TYPE_5_8">TYPE_5_8</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.features2d.FastFeatureDetector.TYPE_7_12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/FastFeatureDetector.html#TYPE_7_12">TYPE_7_12</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.features2d.FastFeatureDetector.TYPE_9_16">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/FastFeatureDetector.html#TYPE_9_16">TYPE_9_16</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.features2d.<a href="org/opencv/features2d/Features2d.html" title="class in org.opencv.features2d">Features2d</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.features2d.Features2d.DrawMatchesFlags_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/Features2d.html#DrawMatchesFlags_DEFAULT">DrawMatchesFlags_DEFAULT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.features2d.Features2d.DrawMatchesFlags_DRAW_OVER_OUTIMG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/Features2d.html#DrawMatchesFlags_DRAW_OVER_OUTIMG">DrawMatchesFlags_DRAW_OVER_OUTIMG</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.features2d.Features2d.DrawMatchesFlags_DRAW_RICH_KEYPOINTS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/Features2d.html#DrawMatchesFlags_DRAW_RICH_KEYPOINTS">DrawMatchesFlags_DRAW_RICH_KEYPOINTS</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.features2d.Features2d.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/Features2d.html#DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS">DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.features2d.<a href="org/opencv/features2d/KAZE.html" title="class in org.opencv.features2d">KAZE</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.features2d.KAZE.DIFF_CHARBONNIER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/KAZE.html#DIFF_CHARBONNIER">DIFF_CHARBONNIER</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.features2d.KAZE.DIFF_PM_G1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/KAZE.html#DIFF_PM_G1">DIFF_PM_G1</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.features2d.KAZE.DIFF_PM_G2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/KAZE.html#DIFF_PM_G2">DIFF_PM_G2</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.features2d.KAZE.DIFF_WEICKERT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/KAZE.html#DIFF_WEICKERT">DIFF_WEICKERT</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.features2d.<a href="org/opencv/features2d/ORB.html" title="class in org.opencv.features2d">ORB</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.features2d.ORB.FAST_SCORE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/ORB.html#FAST_SCORE">FAST_SCORE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.features2d.ORB.HARRIS_SCORE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/features2d/ORB.html#HARRIS_SCORE">HARRIS_SCORE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.imgcodecs.<a href="org/opencv/imgcodecs/Imgcodecs.html" title="class in org.opencv.imgcodecs">Imgcodecs</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMREAD_ANYCOLOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMREAD_ANYCOLOR">IMREAD_ANYCOLOR</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMREAD_ANYDEPTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMREAD_ANYDEPTH">IMREAD_ANYDEPTH</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMREAD_COLOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMREAD_COLOR">IMREAD_COLOR</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMREAD_GRAYSCALE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMREAD_GRAYSCALE">IMREAD_GRAYSCALE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMREAD_IGNORE_ORIENTATION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMREAD_IGNORE_ORIENTATION">IMREAD_IGNORE_ORIENTATION</a></code></td>
<td class="colLast"><code>128</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMREAD_LOAD_GDAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMREAD_LOAD_GDAL">IMREAD_LOAD_GDAL</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_COLOR_2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMREAD_REDUCED_COLOR_2">IMREAD_REDUCED_COLOR_2</a></code></td>
<td class="colLast"><code>17</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_COLOR_4">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMREAD_REDUCED_COLOR_4">IMREAD_REDUCED_COLOR_4</a></code></td>
<td class="colLast"><code>33</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_COLOR_8">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMREAD_REDUCED_COLOR_8">IMREAD_REDUCED_COLOR_8</a></code></td>
<td class="colLast"><code>65</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_GRAYSCALE_2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMREAD_REDUCED_GRAYSCALE_2">IMREAD_REDUCED_GRAYSCALE_2</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_GRAYSCALE_4">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMREAD_REDUCED_GRAYSCALE_4">IMREAD_REDUCED_GRAYSCALE_4</a></code></td>
<td class="colLast"><code>32</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_GRAYSCALE_8">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMREAD_REDUCED_GRAYSCALE_8">IMREAD_REDUCED_GRAYSCALE_8</a></code></td>
<td class="colLast"><code>64</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMREAD_UNCHANGED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMREAD_UNCHANGED">IMREAD_UNCHANGED</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_AVIF_DEPTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_AVIF_DEPTH">IMWRITE_AVIF_DEPTH</a></code></td>
<td class="colLast"><code>513</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_AVIF_QUALITY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_AVIF_QUALITY">IMWRITE_AVIF_QUALITY</a></code></td>
<td class="colLast"><code>512</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_AVIF_SPEED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_AVIF_SPEED">IMWRITE_AVIF_SPEED</a></code></td>
<td class="colLast"><code>514</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION">IMWRITE_EXR_COMPRESSION</a></code></td>
<td class="colLast"><code>49</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_B44">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_B44">IMWRITE_EXR_COMPRESSION_B44</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_B44A">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_B44A">IMWRITE_EXR_COMPRESSION_B44A</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_DWAA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_DWAA">IMWRITE_EXR_COMPRESSION_DWAA</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_DWAB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_DWAB">IMWRITE_EXR_COMPRESSION_DWAB</a></code></td>
<td class="colLast"><code>9</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_NO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_NO">IMWRITE_EXR_COMPRESSION_NO</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_PIZ">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_PIZ">IMWRITE_EXR_COMPRESSION_PIZ</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_PXR24">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_PXR24">IMWRITE_EXR_COMPRESSION_PXR24</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_RLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_RLE">IMWRITE_EXR_COMPRESSION_RLE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_ZIP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_ZIP">IMWRITE_EXR_COMPRESSION_ZIP</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_COMPRESSION_ZIPS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_COMPRESSION_ZIPS">IMWRITE_EXR_COMPRESSION_ZIPS</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_DWA_COMPRESSION_LEVEL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_DWA_COMPRESSION_LEVEL">IMWRITE_EXR_DWA_COMPRESSION_LEVEL</a></code></td>
<td class="colLast"><code>50</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_TYPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_TYPE">IMWRITE_EXR_TYPE</a></code></td>
<td class="colLast"><code>48</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_TYPE_FLOAT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_TYPE_FLOAT">IMWRITE_EXR_TYPE_FLOAT</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_EXR_TYPE_HALF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_EXR_TYPE_HALF">IMWRITE_EXR_TYPE_HALF</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_HDR_COMPRESSION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_HDR_COMPRESSION">IMWRITE_HDR_COMPRESSION</a></code></td>
<td class="colLast"><code>80</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_HDR_COMPRESSION_NONE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_HDR_COMPRESSION_NONE">IMWRITE_HDR_COMPRESSION_NONE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_HDR_COMPRESSION_RLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_HDR_COMPRESSION_RLE">IMWRITE_HDR_COMPRESSION_RLE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_CHROMA_QUALITY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_CHROMA_QUALITY">IMWRITE_JPEG_CHROMA_QUALITY</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_LUMA_QUALITY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_LUMA_QUALITY">IMWRITE_JPEG_LUMA_QUALITY</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_OPTIMIZE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_OPTIMIZE">IMWRITE_JPEG_OPTIMIZE</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_PROGRESSIVE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_PROGRESSIVE">IMWRITE_JPEG_PROGRESSIVE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_QUALITY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_QUALITY">IMWRITE_JPEG_QUALITY</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_RST_INTERVAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_RST_INTERVAL">IMWRITE_JPEG_RST_INTERVAL</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_SAMPLING_FACTOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_SAMPLING_FACTOR">IMWRITE_JPEG_SAMPLING_FACTOR</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_SAMPLING_FACTOR_411">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_SAMPLING_FACTOR_411">IMWRITE_JPEG_SAMPLING_FACTOR_411</a></code></td>
<td class="colLast"><code>4264209</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_SAMPLING_FACTOR_420">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_SAMPLING_FACTOR_420">IMWRITE_JPEG_SAMPLING_FACTOR_420</a></code></td>
<td class="colLast"><code>2232593</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_SAMPLING_FACTOR_422">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_SAMPLING_FACTOR_422">IMWRITE_JPEG_SAMPLING_FACTOR_422</a></code></td>
<td class="colLast"><code>2167057</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_SAMPLING_FACTOR_440">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_SAMPLING_FACTOR_440">IMWRITE_JPEG_SAMPLING_FACTOR_440</a></code></td>
<td class="colLast"><code>1184017</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_SAMPLING_FACTOR_444">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_SAMPLING_FACTOR_444">IMWRITE_JPEG_SAMPLING_FACTOR_444</a></code></td>
<td class="colLast"><code>1118481</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG2000_COMPRESSION_X1000">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG2000_COMPRESSION_X1000">IMWRITE_JPEG2000_COMPRESSION_X1000</a></code></td>
<td class="colLast"><code>272</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_FORMAT_BLACKANDWHITE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PAM_FORMAT_BLACKANDWHITE">IMWRITE_PAM_FORMAT_BLACKANDWHITE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_FORMAT_GRAYSCALE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PAM_FORMAT_GRAYSCALE">IMWRITE_PAM_FORMAT_GRAYSCALE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_FORMAT_GRAYSCALE_ALPHA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PAM_FORMAT_GRAYSCALE_ALPHA">IMWRITE_PAM_FORMAT_GRAYSCALE_ALPHA</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_FORMAT_NULL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PAM_FORMAT_NULL">IMWRITE_PAM_FORMAT_NULL</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_FORMAT_RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PAM_FORMAT_RGB">IMWRITE_PAM_FORMAT_RGB</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_FORMAT_RGB_ALPHA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PAM_FORMAT_RGB_ALPHA">IMWRITE_PAM_FORMAT_RGB_ALPHA</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_PAM_TUPLETYPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PAM_TUPLETYPE">IMWRITE_PAM_TUPLETYPE</a></code></td>
<td class="colLast"><code>128</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_BILEVEL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_BILEVEL">IMWRITE_PNG_BILEVEL</a></code></td>
<td class="colLast"><code>18</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_COMPRESSION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_COMPRESSION">IMWRITE_PNG_COMPRESSION</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_STRATEGY">IMWRITE_PNG_STRATEGY</a></code></td>
<td class="colLast"><code>17</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_STRATEGY_DEFAULT">IMWRITE_PNG_STRATEGY_DEFAULT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_FILTERED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_STRATEGY_FILTERED">IMWRITE_PNG_STRATEGY_FILTERED</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_FIXED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_STRATEGY_FIXED">IMWRITE_PNG_STRATEGY_FIXED</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY">IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_RLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_STRATEGY_RLE">IMWRITE_PNG_STRATEGY_RLE</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_PXM_BINARY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PXM_BINARY">IMWRITE_PXM_BINARY</a></code></td>
<td class="colLast"><code>32</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_COMPRESSION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_TIFF_COMPRESSION">IMWRITE_TIFF_COMPRESSION</a></code></td>
<td class="colLast"><code>259</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_RESUNIT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_TIFF_RESUNIT">IMWRITE_TIFF_RESUNIT</a></code></td>
<td class="colLast"><code>256</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_XDPI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_TIFF_XDPI">IMWRITE_TIFF_XDPI</a></code></td>
<td class="colLast"><code>257</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_TIFF_YDPI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_TIFF_YDPI">IMWRITE_TIFF_YDPI</a></code></td>
<td class="colLast"><code>258</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgcodecs.Imgcodecs.IMWRITE_WEBP_QUALITY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_WEBP_QUALITY">IMWRITE_WEBP_QUALITY</a></code></td>
<td class="colLast"><code>64</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.imgproc.<a href="org/opencv/imgproc/Imgproc.html" title="class in org.opencv.imgproc">Imgproc</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.ADAPTIVE_THRESH_GAUSSIAN_C">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#ADAPTIVE_THRESH_GAUSSIAN_C">ADAPTIVE_THRESH_GAUSSIAN_C</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.ADAPTIVE_THRESH_MEAN_C">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#ADAPTIVE_THRESH_MEAN_C">ADAPTIVE_THRESH_MEAN_C</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CC_STAT_AREA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CC_STAT_AREA">CC_STAT_AREA</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CC_STAT_HEIGHT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CC_STAT_HEIGHT">CC_STAT_HEIGHT</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CC_STAT_LEFT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CC_STAT_LEFT">CC_STAT_LEFT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CC_STAT_MAX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CC_STAT_MAX">CC_STAT_MAX</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CC_STAT_TOP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CC_STAT_TOP">CC_STAT_TOP</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CC_STAT_WIDTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CC_STAT_WIDTH">CC_STAT_WIDTH</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CCL_BBDT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CCL_BBDT">CCL_BBDT</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CCL_BOLELLI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CCL_BOLELLI">CCL_BOLELLI</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CCL_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CCL_DEFAULT">CCL_DEFAULT</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CCL_GRANA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CCL_GRANA">CCL_GRANA</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CCL_SAUF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CCL_SAUF">CCL_SAUF</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CCL_SPAGHETTI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CCL_SPAGHETTI">CCL_SPAGHETTI</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CCL_WU">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CCL_WU">CCL_WU</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CHAIN_APPROX_NONE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CHAIN_APPROX_NONE">CHAIN_APPROX_NONE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CHAIN_APPROX_SIMPLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CHAIN_APPROX_SIMPLE">CHAIN_APPROX_SIMPLE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CHAIN_APPROX_TC89_KCOS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CHAIN_APPROX_TC89_KCOS">CHAIN_APPROX_TC89_KCOS</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CHAIN_APPROX_TC89_L1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CHAIN_APPROX_TC89_L1">CHAIN_APPROX_TC89_L1</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerBG2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerBG2BGR">COLOR_BayerBG2BGR</a></code></td>
<td class="colLast"><code>46</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerBG2BGR_EA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerBG2BGR_EA">COLOR_BayerBG2BGR_EA</a></code></td>
<td class="colLast"><code>135</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerBG2BGR_VNG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerBG2BGR_VNG">COLOR_BayerBG2BGR_VNG</a></code></td>
<td class="colLast"><code>62</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerBG2BGRA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerBG2BGRA">COLOR_BayerBG2BGRA</a></code></td>
<td class="colLast"><code>139</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerBG2GRAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerBG2GRAY">COLOR_BayerBG2GRAY</a></code></td>
<td class="colLast"><code>86</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerBG2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerBG2RGB">COLOR_BayerBG2RGB</a></code></td>
<td class="colLast"><code>48</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerBG2RGB_EA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerBG2RGB_EA">COLOR_BayerBG2RGB_EA</a></code></td>
<td class="colLast"><code>137</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerBG2RGB_VNG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerBG2RGB_VNG">COLOR_BayerBG2RGB_VNG</a></code></td>
<td class="colLast"><code>64</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerBG2RGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerBG2RGBA">COLOR_BayerBG2RGBA</a></code></td>
<td class="colLast"><code>141</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerBGGR2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerBGGR2BGR">COLOR_BayerBGGR2BGR</a></code></td>
<td class="colLast"><code>48</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerBGGR2BGR_EA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerBGGR2BGR_EA">COLOR_BayerBGGR2BGR_EA</a></code></td>
<td class="colLast"><code>137</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerBGGR2BGR_VNG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerBGGR2BGR_VNG">COLOR_BayerBGGR2BGR_VNG</a></code></td>
<td class="colLast"><code>64</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerBGGR2BGRA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerBGGR2BGRA">COLOR_BayerBGGR2BGRA</a></code></td>
<td class="colLast"><code>141</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerBGGR2GRAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerBGGR2GRAY">COLOR_BayerBGGR2GRAY</a></code></td>
<td class="colLast"><code>88</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerBGGR2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerBGGR2RGB">COLOR_BayerBGGR2RGB</a></code></td>
<td class="colLast"><code>46</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerBGGR2RGB_EA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerBGGR2RGB_EA">COLOR_BayerBGGR2RGB_EA</a></code></td>
<td class="colLast"><code>135</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerBGGR2RGB_VNG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerBGGR2RGB_VNG">COLOR_BayerBGGR2RGB_VNG</a></code></td>
<td class="colLast"><code>62</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerBGGR2RGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerBGGR2RGBA">COLOR_BayerBGGR2RGBA</a></code></td>
<td class="colLast"><code>139</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGB2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGB2BGR">COLOR_BayerGB2BGR</a></code></td>
<td class="colLast"><code>47</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGB2BGR_EA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGB2BGR_EA">COLOR_BayerGB2BGR_EA</a></code></td>
<td class="colLast"><code>136</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGB2BGR_VNG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGB2BGR_VNG">COLOR_BayerGB2BGR_VNG</a></code></td>
<td class="colLast"><code>63</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGB2BGRA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGB2BGRA">COLOR_BayerGB2BGRA</a></code></td>
<td class="colLast"><code>140</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGB2GRAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGB2GRAY">COLOR_BayerGB2GRAY</a></code></td>
<td class="colLast"><code>87</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGB2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGB2RGB">COLOR_BayerGB2RGB</a></code></td>
<td class="colLast"><code>49</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGB2RGB_EA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGB2RGB_EA">COLOR_BayerGB2RGB_EA</a></code></td>
<td class="colLast"><code>138</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGB2RGB_VNG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGB2RGB_VNG">COLOR_BayerGB2RGB_VNG</a></code></td>
<td class="colLast"><code>65</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGB2RGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGB2RGBA">COLOR_BayerGB2RGBA</a></code></td>
<td class="colLast"><code>142</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGBRG2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGBRG2BGR">COLOR_BayerGBRG2BGR</a></code></td>
<td class="colLast"><code>49</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGBRG2BGR_EA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGBRG2BGR_EA">COLOR_BayerGBRG2BGR_EA</a></code></td>
<td class="colLast"><code>138</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGBRG2BGR_VNG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGBRG2BGR_VNG">COLOR_BayerGBRG2BGR_VNG</a></code></td>
<td class="colLast"><code>65</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGBRG2BGRA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGBRG2BGRA">COLOR_BayerGBRG2BGRA</a></code></td>
<td class="colLast"><code>142</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGBRG2GRAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGBRG2GRAY">COLOR_BayerGBRG2GRAY</a></code></td>
<td class="colLast"><code>89</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGBRG2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGBRG2RGB">COLOR_BayerGBRG2RGB</a></code></td>
<td class="colLast"><code>47</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGBRG2RGB_EA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGBRG2RGB_EA">COLOR_BayerGBRG2RGB_EA</a></code></td>
<td class="colLast"><code>136</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGBRG2RGB_VNG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGBRG2RGB_VNG">COLOR_BayerGBRG2RGB_VNG</a></code></td>
<td class="colLast"><code>63</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGBRG2RGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGBRG2RGBA">COLOR_BayerGBRG2RGBA</a></code></td>
<td class="colLast"><code>140</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGR2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGR2BGR">COLOR_BayerGR2BGR</a></code></td>
<td class="colLast"><code>49</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGR2BGR_EA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGR2BGR_EA">COLOR_BayerGR2BGR_EA</a></code></td>
<td class="colLast"><code>138</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGR2BGR_VNG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGR2BGR_VNG">COLOR_BayerGR2BGR_VNG</a></code></td>
<td class="colLast"><code>65</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGR2BGRA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGR2BGRA">COLOR_BayerGR2BGRA</a></code></td>
<td class="colLast"><code>142</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGR2GRAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGR2GRAY">COLOR_BayerGR2GRAY</a></code></td>
<td class="colLast"><code>89</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGR2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGR2RGB">COLOR_BayerGR2RGB</a></code></td>
<td class="colLast"><code>47</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGR2RGB_EA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGR2RGB_EA">COLOR_BayerGR2RGB_EA</a></code></td>
<td class="colLast"><code>136</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGR2RGB_VNG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGR2RGB_VNG">COLOR_BayerGR2RGB_VNG</a></code></td>
<td class="colLast"><code>63</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGR2RGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGR2RGBA">COLOR_BayerGR2RGBA</a></code></td>
<td class="colLast"><code>140</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGRBG2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGRBG2BGR">COLOR_BayerGRBG2BGR</a></code></td>
<td class="colLast"><code>47</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGRBG2BGR_EA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGRBG2BGR_EA">COLOR_BayerGRBG2BGR_EA</a></code></td>
<td class="colLast"><code>136</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGRBG2BGR_VNG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGRBG2BGR_VNG">COLOR_BayerGRBG2BGR_VNG</a></code></td>
<td class="colLast"><code>63</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGRBG2BGRA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGRBG2BGRA">COLOR_BayerGRBG2BGRA</a></code></td>
<td class="colLast"><code>140</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGRBG2GRAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGRBG2GRAY">COLOR_BayerGRBG2GRAY</a></code></td>
<td class="colLast"><code>87</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGRBG2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGRBG2RGB">COLOR_BayerGRBG2RGB</a></code></td>
<td class="colLast"><code>49</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGRBG2RGB_EA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGRBG2RGB_EA">COLOR_BayerGRBG2RGB_EA</a></code></td>
<td class="colLast"><code>138</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGRBG2RGB_VNG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGRBG2RGB_VNG">COLOR_BayerGRBG2RGB_VNG</a></code></td>
<td class="colLast"><code>65</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerGRBG2RGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerGRBG2RGBA">COLOR_BayerGRBG2RGBA</a></code></td>
<td class="colLast"><code>142</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerRG2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerRG2BGR">COLOR_BayerRG2BGR</a></code></td>
<td class="colLast"><code>48</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerRG2BGR_EA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerRG2BGR_EA">COLOR_BayerRG2BGR_EA</a></code></td>
<td class="colLast"><code>137</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerRG2BGR_VNG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerRG2BGR_VNG">COLOR_BayerRG2BGR_VNG</a></code></td>
<td class="colLast"><code>64</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerRG2BGRA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerRG2BGRA">COLOR_BayerRG2BGRA</a></code></td>
<td class="colLast"><code>141</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerRG2GRAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerRG2GRAY">COLOR_BayerRG2GRAY</a></code></td>
<td class="colLast"><code>88</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerRG2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerRG2RGB">COLOR_BayerRG2RGB</a></code></td>
<td class="colLast"><code>46</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerRG2RGB_EA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerRG2RGB_EA">COLOR_BayerRG2RGB_EA</a></code></td>
<td class="colLast"><code>135</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerRG2RGB_VNG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerRG2RGB_VNG">COLOR_BayerRG2RGB_VNG</a></code></td>
<td class="colLast"><code>62</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerRG2RGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerRG2RGBA">COLOR_BayerRG2RGBA</a></code></td>
<td class="colLast"><code>139</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerRGGB2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerRGGB2BGR">COLOR_BayerRGGB2BGR</a></code></td>
<td class="colLast"><code>46</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerRGGB2BGR_EA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerRGGB2BGR_EA">COLOR_BayerRGGB2BGR_EA</a></code></td>
<td class="colLast"><code>135</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerRGGB2BGR_VNG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerRGGB2BGR_VNG">COLOR_BayerRGGB2BGR_VNG</a></code></td>
<td class="colLast"><code>62</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerRGGB2BGRA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerRGGB2BGRA">COLOR_BayerRGGB2BGRA</a></code></td>
<td class="colLast"><code>139</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerRGGB2GRAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerRGGB2GRAY">COLOR_BayerRGGB2GRAY</a></code></td>
<td class="colLast"><code>86</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerRGGB2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerRGGB2RGB">COLOR_BayerRGGB2RGB</a></code></td>
<td class="colLast"><code>48</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerRGGB2RGB_EA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerRGGB2RGB_EA">COLOR_BayerRGGB2RGB_EA</a></code></td>
<td class="colLast"><code>137</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerRGGB2RGB_VNG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerRGGB2RGB_VNG">COLOR_BayerRGGB2RGB_VNG</a></code></td>
<td class="colLast"><code>64</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BayerRGGB2RGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BayerRGGB2RGBA">COLOR_BayerRGGB2RGBA</a></code></td>
<td class="colLast"><code>141</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR2BGR555">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR2BGR555">COLOR_BGR2BGR555</a></code></td>
<td class="colLast"><code>22</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR2BGR565">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR2BGR565">COLOR_BGR2BGR565</a></code></td>
<td class="colLast"><code>12</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR2BGRA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR2BGRA">COLOR_BGR2BGRA</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR2GRAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR2GRAY">COLOR_BGR2GRAY</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR2HLS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR2HLS">COLOR_BGR2HLS</a></code></td>
<td class="colLast"><code>52</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR2HLS_FULL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR2HLS_FULL">COLOR_BGR2HLS_FULL</a></code></td>
<td class="colLast"><code>68</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR2HSV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR2HSV">COLOR_BGR2HSV</a></code></td>
<td class="colLast"><code>40</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR2HSV_FULL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR2HSV_FULL">COLOR_BGR2HSV_FULL</a></code></td>
<td class="colLast"><code>66</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR2Lab">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR2Lab">COLOR_BGR2Lab</a></code></td>
<td class="colLast"><code>44</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR2Luv">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR2Luv">COLOR_BGR2Luv</a></code></td>
<td class="colLast"><code>50</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR2RGB">COLOR_BGR2RGB</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR2RGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR2RGBA">COLOR_BGR2RGBA</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR2XYZ">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR2XYZ">COLOR_BGR2XYZ</a></code></td>
<td class="colLast"><code>32</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR2YCrCb">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR2YCrCb">COLOR_BGR2YCrCb</a></code></td>
<td class="colLast"><code>36</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR2YUV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR2YUV">COLOR_BGR2YUV</a></code></td>
<td class="colLast"><code>82</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR2YUV_I420">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR2YUV_I420">COLOR_BGR2YUV_I420</a></code></td>
<td class="colLast"><code>128</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR2YUV_IYUV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR2YUV_IYUV">COLOR_BGR2YUV_IYUV</a></code></td>
<td class="colLast"><code>128</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR2YUV_YV12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR2YUV_YV12">COLOR_BGR2YUV_YV12</a></code></td>
<td class="colLast"><code>132</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR5552BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR5552BGR">COLOR_BGR5552BGR</a></code></td>
<td class="colLast"><code>24</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR5552BGRA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR5552BGRA">COLOR_BGR5552BGRA</a></code></td>
<td class="colLast"><code>28</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR5552GRAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR5552GRAY">COLOR_BGR5552GRAY</a></code></td>
<td class="colLast"><code>31</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR5552RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR5552RGB">COLOR_BGR5552RGB</a></code></td>
<td class="colLast"><code>25</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR5552RGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR5552RGBA">COLOR_BGR5552RGBA</a></code></td>
<td class="colLast"><code>29</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR5652BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR5652BGR">COLOR_BGR5652BGR</a></code></td>
<td class="colLast"><code>14</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR5652BGRA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR5652BGRA">COLOR_BGR5652BGRA</a></code></td>
<td class="colLast"><code>18</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR5652GRAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR5652GRAY">COLOR_BGR5652GRAY</a></code></td>
<td class="colLast"><code>21</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR5652RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR5652RGB">COLOR_BGR5652RGB</a></code></td>
<td class="colLast"><code>15</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGR5652RGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGR5652RGBA">COLOR_BGR5652RGBA</a></code></td>
<td class="colLast"><code>19</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGRA2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGRA2BGR">COLOR_BGRA2BGR</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGRA2BGR555">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGRA2BGR555">COLOR_BGRA2BGR555</a></code></td>
<td class="colLast"><code>26</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGRA2BGR565">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGRA2BGR565">COLOR_BGRA2BGR565</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGRA2GRAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGRA2GRAY">COLOR_BGRA2GRAY</a></code></td>
<td class="colLast"><code>10</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGRA2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGRA2RGB">COLOR_BGRA2RGB</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGRA2RGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGRA2RGBA">COLOR_BGRA2RGBA</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGRA2YUV_I420">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGRA2YUV_I420">COLOR_BGRA2YUV_I420</a></code></td>
<td class="colLast"><code>130</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGRA2YUV_IYUV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGRA2YUV_IYUV">COLOR_BGRA2YUV_IYUV</a></code></td>
<td class="colLast"><code>130</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_BGRA2YUV_YV12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_BGRA2YUV_YV12">COLOR_BGRA2YUV_YV12</a></code></td>
<td class="colLast"><code>134</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_COLORCVT_MAX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_COLORCVT_MAX">COLOR_COLORCVT_MAX</a></code></td>
<td class="colLast"><code>143</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_GRAY2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_GRAY2BGR">COLOR_GRAY2BGR</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_GRAY2BGR555">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_GRAY2BGR555">COLOR_GRAY2BGR555</a></code></td>
<td class="colLast"><code>30</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_GRAY2BGR565">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_GRAY2BGR565">COLOR_GRAY2BGR565</a></code></td>
<td class="colLast"><code>20</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_GRAY2BGRA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_GRAY2BGRA">COLOR_GRAY2BGRA</a></code></td>
<td class="colLast"><code>9</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_GRAY2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_GRAY2RGB">COLOR_GRAY2RGB</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_GRAY2RGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_GRAY2RGBA">COLOR_GRAY2RGBA</a></code></td>
<td class="colLast"><code>9</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_HLS2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_HLS2BGR">COLOR_HLS2BGR</a></code></td>
<td class="colLast"><code>60</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_HLS2BGR_FULL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_HLS2BGR_FULL">COLOR_HLS2BGR_FULL</a></code></td>
<td class="colLast"><code>72</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_HLS2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_HLS2RGB">COLOR_HLS2RGB</a></code></td>
<td class="colLast"><code>61</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_HLS2RGB_FULL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_HLS2RGB_FULL">COLOR_HLS2RGB_FULL</a></code></td>
<td class="colLast"><code>73</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_HSV2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_HSV2BGR">COLOR_HSV2BGR</a></code></td>
<td class="colLast"><code>54</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_HSV2BGR_FULL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_HSV2BGR_FULL">COLOR_HSV2BGR_FULL</a></code></td>
<td class="colLast"><code>70</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_HSV2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_HSV2RGB">COLOR_HSV2RGB</a></code></td>
<td class="colLast"><code>55</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_HSV2RGB_FULL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_HSV2RGB_FULL">COLOR_HSV2RGB_FULL</a></code></td>
<td class="colLast"><code>71</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_Lab2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_Lab2BGR">COLOR_Lab2BGR</a></code></td>
<td class="colLast"><code>56</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_Lab2LBGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_Lab2LBGR">COLOR_Lab2LBGR</a></code></td>
<td class="colLast"><code>78</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_Lab2LRGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_Lab2LRGB">COLOR_Lab2LRGB</a></code></td>
<td class="colLast"><code>79</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_Lab2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_Lab2RGB">COLOR_Lab2RGB</a></code></td>
<td class="colLast"><code>57</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_LBGR2Lab">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_LBGR2Lab">COLOR_LBGR2Lab</a></code></td>
<td class="colLast"><code>74</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_LBGR2Luv">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_LBGR2Luv">COLOR_LBGR2Luv</a></code></td>
<td class="colLast"><code>76</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_LRGB2Lab">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_LRGB2Lab">COLOR_LRGB2Lab</a></code></td>
<td class="colLast"><code>75</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_LRGB2Luv">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_LRGB2Luv">COLOR_LRGB2Luv</a></code></td>
<td class="colLast"><code>77</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_Luv2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_Luv2BGR">COLOR_Luv2BGR</a></code></td>
<td class="colLast"><code>58</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_Luv2LBGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_Luv2LBGR">COLOR_Luv2LBGR</a></code></td>
<td class="colLast"><code>80</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_Luv2LRGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_Luv2LRGB">COLOR_Luv2LRGB</a></code></td>
<td class="colLast"><code>81</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_Luv2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_Luv2RGB">COLOR_Luv2RGB</a></code></td>
<td class="colLast"><code>59</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_mRGBA2RGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_mRGBA2RGBA">COLOR_mRGBA2RGBA</a></code></td>
<td class="colLast"><code>126</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGB2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGB2BGR">COLOR_RGB2BGR</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGB2BGR555">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGB2BGR555">COLOR_RGB2BGR555</a></code></td>
<td class="colLast"><code>23</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGB2BGR565">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGB2BGR565">COLOR_RGB2BGR565</a></code></td>
<td class="colLast"><code>13</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGB2BGRA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGB2BGRA">COLOR_RGB2BGRA</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGB2GRAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGB2GRAY">COLOR_RGB2GRAY</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGB2HLS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGB2HLS">COLOR_RGB2HLS</a></code></td>
<td class="colLast"><code>53</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGB2HLS_FULL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGB2HLS_FULL">COLOR_RGB2HLS_FULL</a></code></td>
<td class="colLast"><code>69</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGB2HSV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGB2HSV">COLOR_RGB2HSV</a></code></td>
<td class="colLast"><code>41</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGB2HSV_FULL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGB2HSV_FULL">COLOR_RGB2HSV_FULL</a></code></td>
<td class="colLast"><code>67</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGB2Lab">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGB2Lab">COLOR_RGB2Lab</a></code></td>
<td class="colLast"><code>45</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGB2Luv">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGB2Luv">COLOR_RGB2Luv</a></code></td>
<td class="colLast"><code>51</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGB2RGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGB2RGBA">COLOR_RGB2RGBA</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGB2XYZ">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGB2XYZ">COLOR_RGB2XYZ</a></code></td>
<td class="colLast"><code>33</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGB2YCrCb">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGB2YCrCb">COLOR_RGB2YCrCb</a></code></td>
<td class="colLast"><code>37</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGB2YUV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGB2YUV">COLOR_RGB2YUV</a></code></td>
<td class="colLast"><code>83</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGB2YUV_I420">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGB2YUV_I420">COLOR_RGB2YUV_I420</a></code></td>
<td class="colLast"><code>127</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGB2YUV_IYUV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGB2YUV_IYUV">COLOR_RGB2YUV_IYUV</a></code></td>
<td class="colLast"><code>127</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGB2YUV_YV12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGB2YUV_YV12">COLOR_RGB2YUV_YV12</a></code></td>
<td class="colLast"><code>131</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGBA2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGBA2BGR">COLOR_RGBA2BGR</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGBA2BGR555">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGBA2BGR555">COLOR_RGBA2BGR555</a></code></td>
<td class="colLast"><code>27</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGBA2BGR565">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGBA2BGR565">COLOR_RGBA2BGR565</a></code></td>
<td class="colLast"><code>17</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGBA2BGRA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGBA2BGRA">COLOR_RGBA2BGRA</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGBA2GRAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGBA2GRAY">COLOR_RGBA2GRAY</a></code></td>
<td class="colLast"><code>11</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGBA2mRGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGBA2mRGBA">COLOR_RGBA2mRGBA</a></code></td>
<td class="colLast"><code>125</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGBA2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGBA2RGB">COLOR_RGBA2RGB</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGBA2YUV_I420">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGBA2YUV_I420">COLOR_RGBA2YUV_I420</a></code></td>
<td class="colLast"><code>129</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGBA2YUV_IYUV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGBA2YUV_IYUV">COLOR_RGBA2YUV_IYUV</a></code></td>
<td class="colLast"><code>129</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_RGBA2YUV_YV12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_RGBA2YUV_YV12">COLOR_RGBA2YUV_YV12</a></code></td>
<td class="colLast"><code>133</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_XYZ2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_XYZ2BGR">COLOR_XYZ2BGR</a></code></td>
<td class="colLast"><code>34</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_XYZ2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_XYZ2RGB">COLOR_XYZ2RGB</a></code></td>
<td class="colLast"><code>35</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YCrCb2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YCrCb2BGR">COLOR_YCrCb2BGR</a></code></td>
<td class="colLast"><code>38</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YCrCb2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YCrCb2RGB">COLOR_YCrCb2RGB</a></code></td>
<td class="colLast"><code>39</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGR">COLOR_YUV2BGR</a></code></td>
<td class="colLast"><code>84</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGR_I420">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGR_I420">COLOR_YUV2BGR_I420</a></code></td>
<td class="colLast"><code>101</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGR_IYUV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGR_IYUV">COLOR_YUV2BGR_IYUV</a></code></td>
<td class="colLast"><code>101</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGR_NV12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGR_NV12">COLOR_YUV2BGR_NV12</a></code></td>
<td class="colLast"><code>91</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGR_NV21">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGR_NV21">COLOR_YUV2BGR_NV21</a></code></td>
<td class="colLast"><code>93</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGR_UYNV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGR_UYNV">COLOR_YUV2BGR_UYNV</a></code></td>
<td class="colLast"><code>108</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGR_UYVY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGR_UYVY">COLOR_YUV2BGR_UYVY</a></code></td>
<td class="colLast"><code>108</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGR_Y422">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGR_Y422">COLOR_YUV2BGR_Y422</a></code></td>
<td class="colLast"><code>108</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGR_YUNV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGR_YUNV">COLOR_YUV2BGR_YUNV</a></code></td>
<td class="colLast"><code>116</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGR_YUY2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGR_YUY2">COLOR_YUV2BGR_YUY2</a></code></td>
<td class="colLast"><code>116</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGR_YUYV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGR_YUYV">COLOR_YUV2BGR_YUYV</a></code></td>
<td class="colLast"><code>116</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGR_YV12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGR_YV12">COLOR_YUV2BGR_YV12</a></code></td>
<td class="colLast"><code>99</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGR_YVYU">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGR_YVYU">COLOR_YUV2BGR_YVYU</a></code></td>
<td class="colLast"><code>118</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGRA_I420">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGRA_I420">COLOR_YUV2BGRA_I420</a></code></td>
<td class="colLast"><code>105</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGRA_IYUV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGRA_IYUV">COLOR_YUV2BGRA_IYUV</a></code></td>
<td class="colLast"><code>105</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGRA_NV12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGRA_NV12">COLOR_YUV2BGRA_NV12</a></code></td>
<td class="colLast"><code>95</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGRA_NV21">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGRA_NV21">COLOR_YUV2BGRA_NV21</a></code></td>
<td class="colLast"><code>97</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGRA_UYNV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGRA_UYNV">COLOR_YUV2BGRA_UYNV</a></code></td>
<td class="colLast"><code>112</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGRA_UYVY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGRA_UYVY">COLOR_YUV2BGRA_UYVY</a></code></td>
<td class="colLast"><code>112</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGRA_Y422">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGRA_Y422">COLOR_YUV2BGRA_Y422</a></code></td>
<td class="colLast"><code>112</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGRA_YUNV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGRA_YUNV">COLOR_YUV2BGRA_YUNV</a></code></td>
<td class="colLast"><code>120</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGRA_YUY2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGRA_YUY2">COLOR_YUV2BGRA_YUY2</a></code></td>
<td class="colLast"><code>120</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGRA_YUYV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGRA_YUYV">COLOR_YUV2BGRA_YUYV</a></code></td>
<td class="colLast"><code>120</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGRA_YV12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGRA_YV12">COLOR_YUV2BGRA_YV12</a></code></td>
<td class="colLast"><code>103</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2BGRA_YVYU">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2BGRA_YVYU">COLOR_YUV2BGRA_YVYU</a></code></td>
<td class="colLast"><code>122</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2GRAY_420">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2GRAY_420">COLOR_YUV2GRAY_420</a></code></td>
<td class="colLast"><code>106</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2GRAY_I420">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2GRAY_I420">COLOR_YUV2GRAY_I420</a></code></td>
<td class="colLast"><code>106</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2GRAY_IYUV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2GRAY_IYUV">COLOR_YUV2GRAY_IYUV</a></code></td>
<td class="colLast"><code>106</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2GRAY_NV12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2GRAY_NV12">COLOR_YUV2GRAY_NV12</a></code></td>
<td class="colLast"><code>106</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2GRAY_NV21">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2GRAY_NV21">COLOR_YUV2GRAY_NV21</a></code></td>
<td class="colLast"><code>106</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2GRAY_UYNV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2GRAY_UYNV">COLOR_YUV2GRAY_UYNV</a></code></td>
<td class="colLast"><code>123</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2GRAY_UYVY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2GRAY_UYVY">COLOR_YUV2GRAY_UYVY</a></code></td>
<td class="colLast"><code>123</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2GRAY_Y422">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2GRAY_Y422">COLOR_YUV2GRAY_Y422</a></code></td>
<td class="colLast"><code>123</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2GRAY_YUNV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2GRAY_YUNV">COLOR_YUV2GRAY_YUNV</a></code></td>
<td class="colLast"><code>124</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2GRAY_YUY2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2GRAY_YUY2">COLOR_YUV2GRAY_YUY2</a></code></td>
<td class="colLast"><code>124</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2GRAY_YUYV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2GRAY_YUYV">COLOR_YUV2GRAY_YUYV</a></code></td>
<td class="colLast"><code>124</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2GRAY_YV12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2GRAY_YV12">COLOR_YUV2GRAY_YV12</a></code></td>
<td class="colLast"><code>106</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2GRAY_YVYU">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2GRAY_YVYU">COLOR_YUV2GRAY_YVYU</a></code></td>
<td class="colLast"><code>124</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGB">COLOR_YUV2RGB</a></code></td>
<td class="colLast"><code>85</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGB_I420">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGB_I420">COLOR_YUV2RGB_I420</a></code></td>
<td class="colLast"><code>100</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGB_IYUV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGB_IYUV">COLOR_YUV2RGB_IYUV</a></code></td>
<td class="colLast"><code>100</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGB_NV12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGB_NV12">COLOR_YUV2RGB_NV12</a></code></td>
<td class="colLast"><code>90</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGB_NV21">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGB_NV21">COLOR_YUV2RGB_NV21</a></code></td>
<td class="colLast"><code>92</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGB_UYNV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGB_UYNV">COLOR_YUV2RGB_UYNV</a></code></td>
<td class="colLast"><code>107</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGB_UYVY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGB_UYVY">COLOR_YUV2RGB_UYVY</a></code></td>
<td class="colLast"><code>107</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGB_Y422">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGB_Y422">COLOR_YUV2RGB_Y422</a></code></td>
<td class="colLast"><code>107</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGB_YUNV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGB_YUNV">COLOR_YUV2RGB_YUNV</a></code></td>
<td class="colLast"><code>115</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGB_YUY2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGB_YUY2">COLOR_YUV2RGB_YUY2</a></code></td>
<td class="colLast"><code>115</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGB_YUYV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGB_YUYV">COLOR_YUV2RGB_YUYV</a></code></td>
<td class="colLast"><code>115</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGB_YV12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGB_YV12">COLOR_YUV2RGB_YV12</a></code></td>
<td class="colLast"><code>98</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGB_YVYU">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGB_YVYU">COLOR_YUV2RGB_YVYU</a></code></td>
<td class="colLast"><code>117</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGBA_I420">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGBA_I420">COLOR_YUV2RGBA_I420</a></code></td>
<td class="colLast"><code>104</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGBA_IYUV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGBA_IYUV">COLOR_YUV2RGBA_IYUV</a></code></td>
<td class="colLast"><code>104</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGBA_NV12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGBA_NV12">COLOR_YUV2RGBA_NV12</a></code></td>
<td class="colLast"><code>94</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGBA_NV21">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGBA_NV21">COLOR_YUV2RGBA_NV21</a></code></td>
<td class="colLast"><code>96</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGBA_UYNV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGBA_UYNV">COLOR_YUV2RGBA_UYNV</a></code></td>
<td class="colLast"><code>111</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGBA_UYVY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGBA_UYVY">COLOR_YUV2RGBA_UYVY</a></code></td>
<td class="colLast"><code>111</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGBA_Y422">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGBA_Y422">COLOR_YUV2RGBA_Y422</a></code></td>
<td class="colLast"><code>111</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGBA_YUNV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGBA_YUNV">COLOR_YUV2RGBA_YUNV</a></code></td>
<td class="colLast"><code>119</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGBA_YUY2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGBA_YUY2">COLOR_YUV2RGBA_YUY2</a></code></td>
<td class="colLast"><code>119</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGBA_YUYV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGBA_YUYV">COLOR_YUV2RGBA_YUYV</a></code></td>
<td class="colLast"><code>119</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGBA_YV12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGBA_YV12">COLOR_YUV2RGBA_YV12</a></code></td>
<td class="colLast"><code>102</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV2RGBA_YVYU">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV2RGBA_YVYU">COLOR_YUV2RGBA_YVYU</a></code></td>
<td class="colLast"><code>121</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV420p2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV420p2BGR">COLOR_YUV420p2BGR</a></code></td>
<td class="colLast"><code>99</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV420p2BGRA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV420p2BGRA">COLOR_YUV420p2BGRA</a></code></td>
<td class="colLast"><code>103</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV420p2GRAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV420p2GRAY">COLOR_YUV420p2GRAY</a></code></td>
<td class="colLast"><code>106</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV420p2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV420p2RGB">COLOR_YUV420p2RGB</a></code></td>
<td class="colLast"><code>98</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV420p2RGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV420p2RGBA">COLOR_YUV420p2RGBA</a></code></td>
<td class="colLast"><code>102</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV420sp2BGR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV420sp2BGR">COLOR_YUV420sp2BGR</a></code></td>
<td class="colLast"><code>93</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV420sp2BGRA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV420sp2BGRA">COLOR_YUV420sp2BGRA</a></code></td>
<td class="colLast"><code>97</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV420sp2GRAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV420sp2GRAY">COLOR_YUV420sp2GRAY</a></code></td>
<td class="colLast"><code>106</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV420sp2RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV420sp2RGB">COLOR_YUV420sp2RGB</a></code></td>
<td class="colLast"><code>92</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLOR_YUV420sp2RGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLOR_YUV420sp2RGBA">COLOR_YUV420sp2RGBA</a></code></td>
<td class="colLast"><code>96</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_AUTUMN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_AUTUMN">COLORMAP_AUTUMN</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_BONE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_BONE">COLORMAP_BONE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_CIVIDIS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_CIVIDIS">COLORMAP_CIVIDIS</a></code></td>
<td class="colLast"><code>17</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_COOL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_COOL">COLORMAP_COOL</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_DEEPGREEN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_DEEPGREEN">COLORMAP_DEEPGREEN</a></code></td>
<td class="colLast"><code>21</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_HOT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_HOT">COLORMAP_HOT</a></code></td>
<td class="colLast"><code>11</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_HSV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_HSV">COLORMAP_HSV</a></code></td>
<td class="colLast"><code>9</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_INFERNO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_INFERNO">COLORMAP_INFERNO</a></code></td>
<td class="colLast"><code>14</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_JET">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_JET">COLORMAP_JET</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_MAGMA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_MAGMA">COLORMAP_MAGMA</a></code></td>
<td class="colLast"><code>13</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_OCEAN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_OCEAN">COLORMAP_OCEAN</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_PARULA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_PARULA">COLORMAP_PARULA</a></code></td>
<td class="colLast"><code>12</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_PINK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_PINK">COLORMAP_PINK</a></code></td>
<td class="colLast"><code>10</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_PLASMA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_PLASMA">COLORMAP_PLASMA</a></code></td>
<td class="colLast"><code>15</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_RAINBOW">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_RAINBOW">COLORMAP_RAINBOW</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_SPRING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_SPRING">COLORMAP_SPRING</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_SUMMER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_SUMMER">COLORMAP_SUMMER</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_TURBO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_TURBO">COLORMAP_TURBO</a></code></td>
<td class="colLast"><code>20</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_TWILIGHT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_TWILIGHT">COLORMAP_TWILIGHT</a></code></td>
<td class="colLast"><code>18</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_TWILIGHT_SHIFTED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_TWILIGHT_SHIFTED">COLORMAP_TWILIGHT_SHIFTED</a></code></td>
<td class="colLast"><code>19</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_VIRIDIS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_VIRIDIS">COLORMAP_VIRIDIS</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.COLORMAP_WINTER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#COLORMAP_WINTER">COLORMAP_WINTER</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CONTOURS_MATCH_I1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CONTOURS_MATCH_I1">CONTOURS_MATCH_I1</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CONTOURS_MATCH_I2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CONTOURS_MATCH_I2">CONTOURS_MATCH_I2</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CONTOURS_MATCH_I3">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CONTOURS_MATCH_I3">CONTOURS_MATCH_I3</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_BILATERAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_BILATERAL">CV_BILATERAL</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_BLUR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_BLUR">CV_BLUR</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_BLUR_NO_SCALE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_BLUR_NO_SCALE">CV_BLUR_NO_SCALE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_CANNY_L2_GRADIENT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_CANNY_L2_GRADIENT">CV_CANNY_L2_GRADIENT</a></code></td>
<td class="colLast"><code>-2147483648</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_CHAIN_CODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_CHAIN_CODE">CV_CHAIN_CODE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_CLOCKWISE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_CLOCKWISE">CV_CLOCKWISE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_COMP_BHATTACHARYYA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_COMP_BHATTACHARYYA">CV_COMP_BHATTACHARYYA</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_COMP_CHISQR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_COMP_CHISQR">CV_COMP_CHISQR</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_COMP_CHISQR_ALT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_COMP_CHISQR_ALT">CV_COMP_CHISQR_ALT</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_COMP_CORREL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_COMP_CORREL">CV_COMP_CORREL</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_COMP_HELLINGER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_COMP_HELLINGER">CV_COMP_HELLINGER</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_COMP_INTERSECT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_COMP_INTERSECT">CV_COMP_INTERSECT</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_COMP_KL_DIV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_COMP_KL_DIV">CV_COMP_KL_DIV</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_CONTOURS_MATCH_I1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_CONTOURS_MATCH_I1">CV_CONTOURS_MATCH_I1</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_CONTOURS_MATCH_I2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_CONTOURS_MATCH_I2">CV_CONTOURS_MATCH_I2</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_CONTOURS_MATCH_I3">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_CONTOURS_MATCH_I3">CV_CONTOURS_MATCH_I3</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_COUNTER_CLOCKWISE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_COUNTER_CLOCKWISE">CV_COUNTER_CLOCKWISE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_DIST_C">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_DIST_C">CV_DIST_C</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_DIST_FAIR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_DIST_FAIR">CV_DIST_FAIR</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_DIST_HUBER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_DIST_HUBER">CV_DIST_HUBER</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_DIST_L1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_DIST_L1">CV_DIST_L1</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_DIST_L12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_DIST_L12">CV_DIST_L12</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_DIST_L2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_DIST_L2">CV_DIST_L2</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_DIST_LABEL_CCOMP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_DIST_LABEL_CCOMP">CV_DIST_LABEL_CCOMP</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_DIST_LABEL_PIXEL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_DIST_LABEL_PIXEL">CV_DIST_LABEL_PIXEL</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_DIST_MASK_3">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_DIST_MASK_3">CV_DIST_MASK_3</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_DIST_MASK_5">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_DIST_MASK_5">CV_DIST_MASK_5</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_DIST_MASK_PRECISE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_DIST_MASK_PRECISE">CV_DIST_MASK_PRECISE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_DIST_USER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_DIST_USER">CV_DIST_USER</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_DIST_WELSCH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_DIST_WELSCH">CV_DIST_WELSCH</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_GAUSSIAN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_GAUSSIAN">CV_GAUSSIAN</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_GAUSSIAN_5x5">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_GAUSSIAN_5x5">CV_GAUSSIAN_5x5</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_HOUGH_GRADIENT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_HOUGH_GRADIENT">CV_HOUGH_GRADIENT</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_HOUGH_MULTI_SCALE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_HOUGH_MULTI_SCALE">CV_HOUGH_MULTI_SCALE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_HOUGH_PROBABILISTIC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_HOUGH_PROBABILISTIC">CV_HOUGH_PROBABILISTIC</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_HOUGH_STANDARD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_HOUGH_STANDARD">CV_HOUGH_STANDARD</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_LINK_RUNS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_LINK_RUNS">CV_LINK_RUNS</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_MAX_SOBEL_KSIZE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_MAX_SOBEL_KSIZE">CV_MAX_SOBEL_KSIZE</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_MEDIAN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_MEDIAN">CV_MEDIAN</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_mRGBA2RGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_mRGBA2RGBA">CV_mRGBA2RGBA</a></code></td>
<td class="colLast"><code>126</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_POLY_APPROX_DP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_POLY_APPROX_DP">CV_POLY_APPROX_DP</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_RGBA2mRGBA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_RGBA2mRGBA">CV_RGBA2mRGBA</a></code></td>
<td class="colLast"><code>125</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_SCHARR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_SCHARR">CV_SCHARR</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_SHAPE_CROSS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_SHAPE_CROSS">CV_SHAPE_CROSS</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_SHAPE_CUSTOM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_SHAPE_CUSTOM">CV_SHAPE_CUSTOM</a></code></td>
<td class="colLast"><code>100</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_SHAPE_ELLIPSE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_SHAPE_ELLIPSE">CV_SHAPE_ELLIPSE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_SHAPE_RECT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_SHAPE_RECT">CV_SHAPE_RECT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_WARP_FILL_OUTLIERS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_WARP_FILL_OUTLIERS">CV_WARP_FILL_OUTLIERS</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.CV_WARP_INVERSE_MAP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#CV_WARP_INVERSE_MAP">CV_WARP_INVERSE_MAP</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.DIST_C">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#DIST_C">DIST_C</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.DIST_FAIR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#DIST_FAIR">DIST_FAIR</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.DIST_HUBER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#DIST_HUBER">DIST_HUBER</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.DIST_L1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#DIST_L1">DIST_L1</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.DIST_L12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#DIST_L12">DIST_L12</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.DIST_L2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#DIST_L2">DIST_L2</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.DIST_LABEL_CCOMP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#DIST_LABEL_CCOMP">DIST_LABEL_CCOMP</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.DIST_LABEL_PIXEL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#DIST_LABEL_PIXEL">DIST_LABEL_PIXEL</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.DIST_MASK_3">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#DIST_MASK_3">DIST_MASK_3</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.DIST_MASK_5">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#DIST_MASK_5">DIST_MASK_5</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.DIST_MASK_PRECISE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#DIST_MASK_PRECISE">DIST_MASK_PRECISE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.DIST_USER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#DIST_USER">DIST_USER</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.DIST_WELSCH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#DIST_WELSCH">DIST_WELSCH</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.FILLED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#FILLED">FILLED</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.FILTER_SCHARR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#FILTER_SCHARR">FILTER_SCHARR</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.FLOODFILL_FIXED_RANGE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#FLOODFILL_FIXED_RANGE">FLOODFILL_FIXED_RANGE</a></code></td>
<td class="colLast"><code>65536</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.FLOODFILL_MASK_ONLY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#FLOODFILL_MASK_ONLY">FLOODFILL_MASK_ONLY</a></code></td>
<td class="colLast"><code>131072</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.FONT_HERSHEY_COMPLEX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#FONT_HERSHEY_COMPLEX">FONT_HERSHEY_COMPLEX</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.FONT_HERSHEY_COMPLEX_SMALL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#FONT_HERSHEY_COMPLEX_SMALL">FONT_HERSHEY_COMPLEX_SMALL</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.FONT_HERSHEY_DUPLEX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#FONT_HERSHEY_DUPLEX">FONT_HERSHEY_DUPLEX</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.FONT_HERSHEY_PLAIN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#FONT_HERSHEY_PLAIN">FONT_HERSHEY_PLAIN</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.FONT_HERSHEY_SCRIPT_COMPLEX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#FONT_HERSHEY_SCRIPT_COMPLEX">FONT_HERSHEY_SCRIPT_COMPLEX</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.FONT_HERSHEY_SCRIPT_SIMPLEX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#FONT_HERSHEY_SCRIPT_SIMPLEX">FONT_HERSHEY_SCRIPT_SIMPLEX</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.FONT_HERSHEY_SIMPLEX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#FONT_HERSHEY_SIMPLEX">FONT_HERSHEY_SIMPLEX</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.FONT_HERSHEY_TRIPLEX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#FONT_HERSHEY_TRIPLEX">FONT_HERSHEY_TRIPLEX</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.FONT_ITALIC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#FONT_ITALIC">FONT_ITALIC</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.GC_BGD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#GC_BGD">GC_BGD</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.GC_EVAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#GC_EVAL">GC_EVAL</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.GC_EVAL_FREEZE_MODEL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#GC_EVAL_FREEZE_MODEL">GC_EVAL_FREEZE_MODEL</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.GC_FGD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#GC_FGD">GC_FGD</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.GC_INIT_WITH_MASK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#GC_INIT_WITH_MASK">GC_INIT_WITH_MASK</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.GC_INIT_WITH_RECT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#GC_INIT_WITH_RECT">GC_INIT_WITH_RECT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.GC_PR_BGD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#GC_PR_BGD">GC_PR_BGD</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.GC_PR_FGD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#GC_PR_FGD">GC_PR_FGD</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.HISTCMP_BHATTACHARYYA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#HISTCMP_BHATTACHARYYA">HISTCMP_BHATTACHARYYA</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.HISTCMP_CHISQR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#HISTCMP_CHISQR">HISTCMP_CHISQR</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.HISTCMP_CHISQR_ALT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#HISTCMP_CHISQR_ALT">HISTCMP_CHISQR_ALT</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.HISTCMP_CORREL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#HISTCMP_CORREL">HISTCMP_CORREL</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.HISTCMP_HELLINGER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#HISTCMP_HELLINGER">HISTCMP_HELLINGER</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.HISTCMP_INTERSECT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#HISTCMP_INTERSECT">HISTCMP_INTERSECT</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.HISTCMP_KL_DIV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#HISTCMP_KL_DIV">HISTCMP_KL_DIV</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.HOUGH_GRADIENT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#HOUGH_GRADIENT">HOUGH_GRADIENT</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.HOUGH_GRADIENT_ALT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#HOUGH_GRADIENT_ALT">HOUGH_GRADIENT_ALT</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.HOUGH_MULTI_SCALE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#HOUGH_MULTI_SCALE">HOUGH_MULTI_SCALE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.HOUGH_PROBABILISTIC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#HOUGH_PROBABILISTIC">HOUGH_PROBABILISTIC</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.HOUGH_STANDARD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#HOUGH_STANDARD">HOUGH_STANDARD</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.INTER_AREA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#INTER_AREA">INTER_AREA</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.INTER_BITS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#INTER_BITS">INTER_BITS</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.INTER_BITS2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#INTER_BITS2">INTER_BITS2</a></code></td>
<td class="colLast"><code>10</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.INTER_CUBIC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#INTER_CUBIC">INTER_CUBIC</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.INTER_LANCZOS4">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#INTER_LANCZOS4">INTER_LANCZOS4</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.INTER_LINEAR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#INTER_LINEAR">INTER_LINEAR</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.INTER_LINEAR_EXACT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#INTER_LINEAR_EXACT">INTER_LINEAR_EXACT</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.INTER_MAX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#INTER_MAX">INTER_MAX</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.INTER_NEAREST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#INTER_NEAREST">INTER_NEAREST</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.INTER_NEAREST_EXACT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#INTER_NEAREST_EXACT">INTER_NEAREST_EXACT</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.INTER_TAB_SIZE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#INTER_TAB_SIZE">INTER_TAB_SIZE</a></code></td>
<td class="colLast"><code>32</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.INTER_TAB_SIZE2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#INTER_TAB_SIZE2">INTER_TAB_SIZE2</a></code></td>
<td class="colLast"><code>1024</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.INTERSECT_FULL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#INTERSECT_FULL">INTERSECT_FULL</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.INTERSECT_NONE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#INTERSECT_NONE">INTERSECT_NONE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.INTERSECT_PARTIAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#INTERSECT_PARTIAL">INTERSECT_PARTIAL</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.LINE_4">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#LINE_4">LINE_4</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.LINE_8">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#LINE_8">LINE_8</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.LINE_AA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#LINE_AA">LINE_AA</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.LSD_REFINE_ADV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#LSD_REFINE_ADV">LSD_REFINE_ADV</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.LSD_REFINE_NONE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#LSD_REFINE_NONE">LSD_REFINE_NONE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.LSD_REFINE_STD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#LSD_REFINE_STD">LSD_REFINE_STD</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.MARKER_CROSS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#MARKER_CROSS">MARKER_CROSS</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.MARKER_DIAMOND">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#MARKER_DIAMOND">MARKER_DIAMOND</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.MARKER_SQUARE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#MARKER_SQUARE">MARKER_SQUARE</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.MARKER_STAR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#MARKER_STAR">MARKER_STAR</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.MARKER_TILTED_CROSS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#MARKER_TILTED_CROSS">MARKER_TILTED_CROSS</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.MARKER_TRIANGLE_DOWN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#MARKER_TRIANGLE_DOWN">MARKER_TRIANGLE_DOWN</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.MARKER_TRIANGLE_UP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#MARKER_TRIANGLE_UP">MARKER_TRIANGLE_UP</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.MORPH_BLACKHAT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#MORPH_BLACKHAT">MORPH_BLACKHAT</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.MORPH_CLOSE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#MORPH_CLOSE">MORPH_CLOSE</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.MORPH_CROSS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#MORPH_CROSS">MORPH_CROSS</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.MORPH_DILATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#MORPH_DILATE">MORPH_DILATE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.MORPH_ELLIPSE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#MORPH_ELLIPSE">MORPH_ELLIPSE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.MORPH_ERODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#MORPH_ERODE">MORPH_ERODE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.MORPH_GRADIENT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#MORPH_GRADIENT">MORPH_GRADIENT</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.MORPH_HITMISS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#MORPH_HITMISS">MORPH_HITMISS</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.MORPH_OPEN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#MORPH_OPEN">MORPH_OPEN</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.MORPH_RECT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#MORPH_RECT">MORPH_RECT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.MORPH_TOPHAT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#MORPH_TOPHAT">MORPH_TOPHAT</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.RETR_CCOMP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#RETR_CCOMP">RETR_CCOMP</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.RETR_EXTERNAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#RETR_EXTERNAL">RETR_EXTERNAL</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.RETR_FLOODFILL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#RETR_FLOODFILL">RETR_FLOODFILL</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.RETR_LIST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#RETR_LIST">RETR_LIST</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.RETR_TREE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#RETR_TREE">RETR_TREE</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.THRESH_BINARY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#THRESH_BINARY">THRESH_BINARY</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.THRESH_BINARY_INV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#THRESH_BINARY_INV">THRESH_BINARY_INV</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.THRESH_MASK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#THRESH_MASK">THRESH_MASK</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.THRESH_OTSU">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#THRESH_OTSU">THRESH_OTSU</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.THRESH_TOZERO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#THRESH_TOZERO">THRESH_TOZERO</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.THRESH_TOZERO_INV">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#THRESH_TOZERO_INV">THRESH_TOZERO_INV</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.THRESH_TRIANGLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#THRESH_TRIANGLE">THRESH_TRIANGLE</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.THRESH_TRUNC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#THRESH_TRUNC">THRESH_TRUNC</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.TM_CCOEFF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#TM_CCOEFF">TM_CCOEFF</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.TM_CCOEFF_NORMED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#TM_CCOEFF_NORMED">TM_CCOEFF_NORMED</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.TM_CCORR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#TM_CCORR">TM_CCORR</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.TM_CCORR_NORMED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#TM_CCORR_NORMED">TM_CCORR_NORMED</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.TM_SQDIFF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#TM_SQDIFF">TM_SQDIFF</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.TM_SQDIFF_NORMED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#TM_SQDIFF_NORMED">TM_SQDIFF_NORMED</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.WARP_FILL_OUTLIERS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#WARP_FILL_OUTLIERS">WARP_FILL_OUTLIERS</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.WARP_INVERSE_MAP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#WARP_INVERSE_MAP">WARP_INVERSE_MAP</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.WARP_POLAR_LINEAR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#WARP_POLAR_LINEAR">WARP_POLAR_LINEAR</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Imgproc.WARP_POLAR_LOG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Imgproc.html#WARP_POLAR_LOG">WARP_POLAR_LOG</a></code></td>
<td class="colLast"><code>256</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.imgproc.<a href="org/opencv/imgproc/Subdiv2D.html" title="class in org.opencv.imgproc">Subdiv2D</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Subdiv2D.NEXT_AROUND_DST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Subdiv2D.html#NEXT_AROUND_DST">NEXT_AROUND_DST</a></code></td>
<td class="colLast"><code>34</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Subdiv2D.NEXT_AROUND_LEFT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Subdiv2D.html#NEXT_AROUND_LEFT">NEXT_AROUND_LEFT</a></code></td>
<td class="colLast"><code>19</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Subdiv2D.NEXT_AROUND_ORG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Subdiv2D.html#NEXT_AROUND_ORG">NEXT_AROUND_ORG</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Subdiv2D.NEXT_AROUND_RIGHT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Subdiv2D.html#NEXT_AROUND_RIGHT">NEXT_AROUND_RIGHT</a></code></td>
<td class="colLast"><code>49</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Subdiv2D.PREV_AROUND_DST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Subdiv2D.html#PREV_AROUND_DST">PREV_AROUND_DST</a></code></td>
<td class="colLast"><code>51</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Subdiv2D.PREV_AROUND_LEFT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Subdiv2D.html#PREV_AROUND_LEFT">PREV_AROUND_LEFT</a></code></td>
<td class="colLast"><code>32</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Subdiv2D.PREV_AROUND_ORG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Subdiv2D.html#PREV_AROUND_ORG">PREV_AROUND_ORG</a></code></td>
<td class="colLast"><code>17</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Subdiv2D.PREV_AROUND_RIGHT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Subdiv2D.html#PREV_AROUND_RIGHT">PREV_AROUND_RIGHT</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Subdiv2D.PTLOC_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Subdiv2D.html#PTLOC_ERROR">PTLOC_ERROR</a></code></td>
<td class="colLast"><code>-2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Subdiv2D.PTLOC_INSIDE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Subdiv2D.html#PTLOC_INSIDE">PTLOC_INSIDE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Subdiv2D.PTLOC_ON_EDGE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Subdiv2D.html#PTLOC_ON_EDGE">PTLOC_ON_EDGE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.imgproc.Subdiv2D.PTLOC_OUTSIDE_RECT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Subdiv2D.html#PTLOC_OUTSIDE_RECT">PTLOC_OUTSIDE_RECT</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.imgproc.Subdiv2D.PTLOC_VERTEX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/imgproc/Subdiv2D.html#PTLOC_VERTEX">PTLOC_VERTEX</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.ml.<a href="org/opencv/ml/ANN_MLP.html" title="class in org.opencv.ml">ANN_MLP</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.ANN_MLP.ANNEAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/ANN_MLP.html#ANNEAL">ANNEAL</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.ANN_MLP.BACKPROP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/ANN_MLP.html#BACKPROP">BACKPROP</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.ANN_MLP.GAUSSIAN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/ANN_MLP.html#GAUSSIAN">GAUSSIAN</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.ANN_MLP.IDENTITY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/ANN_MLP.html#IDENTITY">IDENTITY</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.ANN_MLP.LEAKYRELU">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/ANN_MLP.html#LEAKYRELU">LEAKYRELU</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.ANN_MLP.NO_INPUT_SCALE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/ANN_MLP.html#NO_INPUT_SCALE">NO_INPUT_SCALE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.ANN_MLP.NO_OUTPUT_SCALE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/ANN_MLP.html#NO_OUTPUT_SCALE">NO_OUTPUT_SCALE</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.ANN_MLP.RELU">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/ANN_MLP.html#RELU">RELU</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.ANN_MLP.RPROP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/ANN_MLP.html#RPROP">RPROP</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.ANN_MLP.SIGMOID_SYM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/ANN_MLP.html#SIGMOID_SYM">SIGMOID_SYM</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.ANN_MLP.UPDATE_WEIGHTS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/ANN_MLP.html#UPDATE_WEIGHTS">UPDATE_WEIGHTS</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.ml.<a href="org/opencv/ml/Boost.html" title="class in org.opencv.ml">Boost</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.Boost.DISCRETE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/Boost.html#DISCRETE">DISCRETE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.Boost.GENTLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/Boost.html#GENTLE">GENTLE</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.Boost.LOGIT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/Boost.html#LOGIT">LOGIT</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.Boost.REAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/Boost.html#REAL">REAL</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.ml.<a href="org/opencv/ml/DTrees.html" title="class in org.opencv.ml">DTrees</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.DTrees.PREDICT_AUTO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/DTrees.html#PREDICT_AUTO">PREDICT_AUTO</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.DTrees.PREDICT_MASK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/DTrees.html#PREDICT_MASK">PREDICT_MASK</a></code></td>
<td class="colLast"><code>768</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.DTrees.PREDICT_MAX_VOTE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/DTrees.html#PREDICT_MAX_VOTE">PREDICT_MAX_VOTE</a></code></td>
<td class="colLast"><code>512</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.DTrees.PREDICT_SUM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/DTrees.html#PREDICT_SUM">PREDICT_SUM</a></code></td>
<td class="colLast"><code>256</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.ml.<a href="org/opencv/ml/EM.html" title="class in org.opencv.ml">EM</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.EM.COV_MAT_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/EM.html#COV_MAT_DEFAULT">COV_MAT_DEFAULT</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.EM.COV_MAT_DIAGONAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/EM.html#COV_MAT_DIAGONAL">COV_MAT_DIAGONAL</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.EM.COV_MAT_GENERIC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/EM.html#COV_MAT_GENERIC">COV_MAT_GENERIC</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.EM.COV_MAT_SPHERICAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/EM.html#COV_MAT_SPHERICAL">COV_MAT_SPHERICAL</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.EM.DEFAULT_MAX_ITERS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/EM.html#DEFAULT_MAX_ITERS">DEFAULT_MAX_ITERS</a></code></td>
<td class="colLast"><code>100</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.EM.DEFAULT_NCLUSTERS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/EM.html#DEFAULT_NCLUSTERS">DEFAULT_NCLUSTERS</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.EM.START_AUTO_STEP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/EM.html#START_AUTO_STEP">START_AUTO_STEP</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.EM.START_E_STEP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/EM.html#START_E_STEP">START_E_STEP</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.EM.START_M_STEP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/EM.html#START_M_STEP">START_M_STEP</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.ml.<a href="org/opencv/ml/KNearest.html" title="class in org.opencv.ml">KNearest</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.KNearest.BRUTE_FORCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/KNearest.html#BRUTE_FORCE">BRUTE_FORCE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.KNearest.KDTREE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/KNearest.html#KDTREE">KDTREE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.ml.<a href="org/opencv/ml/LogisticRegression.html" title="class in org.opencv.ml">LogisticRegression</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.LogisticRegression.BATCH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/LogisticRegression.html#BATCH">BATCH</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.LogisticRegression.MINI_BATCH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/LogisticRegression.html#MINI_BATCH">MINI_BATCH</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.LogisticRegression.REG_DISABLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/LogisticRegression.html#REG_DISABLE">REG_DISABLE</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.LogisticRegression.REG_L1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/LogisticRegression.html#REG_L1">REG_L1</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.LogisticRegression.REG_L2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/LogisticRegression.html#REG_L2">REG_L2</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.ml.<a href="org/opencv/ml/Ml.html" title="class in org.opencv.ml">Ml</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.Ml.COL_SAMPLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/Ml.html#COL_SAMPLE">COL_SAMPLE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.Ml.ROW_SAMPLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/Ml.html#ROW_SAMPLE">ROW_SAMPLE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.Ml.TEST_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/Ml.html#TEST_ERROR">TEST_ERROR</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.Ml.TRAIN_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/Ml.html#TRAIN_ERROR">TRAIN_ERROR</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.Ml.VAR_CATEGORICAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/Ml.html#VAR_CATEGORICAL">VAR_CATEGORICAL</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.Ml.VAR_NUMERICAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/Ml.html#VAR_NUMERICAL">VAR_NUMERICAL</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.Ml.VAR_ORDERED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/Ml.html#VAR_ORDERED">VAR_ORDERED</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.ml.<a href="org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.StatModel.COMPRESSED_INPUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/StatModel.html#COMPRESSED_INPUT">COMPRESSED_INPUT</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.StatModel.PREPROCESSED_INPUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/StatModel.html#PREPROCESSED_INPUT">PREPROCESSED_INPUT</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.StatModel.RAW_OUTPUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/StatModel.html#RAW_OUTPUT">RAW_OUTPUT</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.StatModel.UPDATE_MODEL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/StatModel.html#UPDATE_MODEL">UPDATE_MODEL</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.ml.<a href="org/opencv/ml/SVM.html" title="class in org.opencv.ml">SVM</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.SVM.C">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVM.html#C">C</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.SVM.C_SVC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVM.html#C_SVC">C_SVC</a></code></td>
<td class="colLast"><code>100</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.SVM.CHI2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVM.html#CHI2">CHI2</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.SVM.COEF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVM.html#COEF">COEF</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.SVM.CUSTOM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVM.html#CUSTOM">CUSTOM</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.SVM.DEGREE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVM.html#DEGREE">DEGREE</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.SVM.EPS_SVR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVM.html#EPS_SVR">EPS_SVR</a></code></td>
<td class="colLast"><code>103</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.SVM.GAMMA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVM.html#GAMMA">GAMMA</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.SVM.INTER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVM.html#INTER">INTER</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.SVM.LINEAR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVM.html#LINEAR">LINEAR</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.SVM.NU">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVM.html#NU">NU</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.SVM.NU_SVC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVM.html#NU_SVC">NU_SVC</a></code></td>
<td class="colLast"><code>101</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.SVM.NU_SVR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVM.html#NU_SVR">NU_SVR</a></code></td>
<td class="colLast"><code>104</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.SVM.ONE_CLASS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVM.html#ONE_CLASS">ONE_CLASS</a></code></td>
<td class="colLast"><code>102</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.SVM.P">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVM.html#P">P</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.SVM.POLY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVM.html#POLY">POLY</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.SVM.RBF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVM.html#RBF">RBF</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.SVM.SIGMOID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVM.html#SIGMOID">SIGMOID</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.ml.<a href="org/opencv/ml/SVMSGD.html" title="class in org.opencv.ml">SVMSGD</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.SVMSGD.ASGD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVMSGD.html#ASGD">ASGD</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.SVMSGD.HARD_MARGIN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVMSGD.html#HARD_MARGIN">HARD_MARGIN</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.ml.SVMSGD.SGD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVMSGD.html#SGD">SGD</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.ml.SVMSGD.SOFT_MARGIN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/ml/SVMSGD.html#SOFT_MARGIN">SOFT_MARGIN</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.objdetect.<a href="org/opencv/objdetect/FaceRecognizerSF.html" title="class in org.opencv.objdetect">FaceRecognizerSF</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.FaceRecognizerSF.FR_COSINE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/FaceRecognizerSF.html#FR_COSINE">FR_COSINE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.FaceRecognizerSF.FR_NORM_L2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/FaceRecognizerSF.html#FR_NORM_L2">FR_NORM_L2</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.objdetect.<a href="org/opencv/objdetect/HOGDescriptor.html" title="class in org.opencv.objdetect">HOGDescriptor</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.HOGDescriptor.DEFAULT_NLEVELS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/HOGDescriptor.html#DEFAULT_NLEVELS">DEFAULT_NLEVELS</a></code></td>
<td class="colLast"><code>64</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.HOGDescriptor.DESCR_FORMAT_COL_BY_COL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/HOGDescriptor.html#DESCR_FORMAT_COL_BY_COL">DESCR_FORMAT_COL_BY_COL</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.HOGDescriptor.DESCR_FORMAT_ROW_BY_ROW">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/HOGDescriptor.html#DESCR_FORMAT_ROW_BY_ROW">DESCR_FORMAT_ROW_BY_ROW</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.HOGDescriptor.L2Hys">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/HOGDescriptor.html#L2Hys">L2Hys</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.objdetect.<a href="org/opencv/objdetect/Objdetect.html" title="class in org.opencv.objdetect">Objdetect</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.CASCADE_DO_CANNY_PRUNING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#CASCADE_DO_CANNY_PRUNING">CASCADE_DO_CANNY_PRUNING</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.CASCADE_DO_ROUGH_SEARCH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#CASCADE_DO_ROUGH_SEARCH">CASCADE_DO_ROUGH_SEARCH</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.CASCADE_FIND_BIGGEST_OBJECT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#CASCADE_FIND_BIGGEST_OBJECT">CASCADE_FIND_BIGGEST_OBJECT</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.CASCADE_SCALE_IMAGE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#CASCADE_SCALE_IMAGE">CASCADE_SCALE_IMAGE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.CORNER_REFINE_APRILTAG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#CORNER_REFINE_APRILTAG">CORNER_REFINE_APRILTAG</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.CORNER_REFINE_CONTOUR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#CORNER_REFINE_CONTOUR">CORNER_REFINE_CONTOUR</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.CORNER_REFINE_NONE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#CORNER_REFINE_NONE">CORNER_REFINE_NONE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.CORNER_REFINE_SUBPIX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#CORNER_REFINE_SUBPIX">CORNER_REFINE_SUBPIX</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DetectionBasedTracker_DETECTED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DetectionBasedTracker_DETECTED">DetectionBasedTracker_DETECTED</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DetectionBasedTracker_DETECTED_NOT_SHOWN_YET">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DetectionBasedTracker_DETECTED_NOT_SHOWN_YET">DetectionBasedTracker_DETECTED_NOT_SHOWN_YET</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DetectionBasedTracker_DETECTED_TEMPORARY_LOST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DetectionBasedTracker_DETECTED_TEMPORARY_LOST">DetectionBasedTracker_DETECTED_TEMPORARY_LOST</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DetectionBasedTracker_WRONG_OBJECT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DetectionBasedTracker_WRONG_OBJECT">DetectionBasedTracker_WRONG_OBJECT</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_4X4_100">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_4X4_100">DICT_4X4_100</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_4X4_1000">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_4X4_1000">DICT_4X4_1000</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_4X4_250">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_4X4_250">DICT_4X4_250</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_4X4_50">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_4X4_50">DICT_4X4_50</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_5X5_100">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_5X5_100">DICT_5X5_100</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_5X5_1000">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_5X5_1000">DICT_5X5_1000</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_5X5_250">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_5X5_250">DICT_5X5_250</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_5X5_50">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_5X5_50">DICT_5X5_50</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_6X6_100">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_6X6_100">DICT_6X6_100</a></code></td>
<td class="colLast"><code>9</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_6X6_1000">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_6X6_1000">DICT_6X6_1000</a></code></td>
<td class="colLast"><code>11</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_6X6_250">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_6X6_250">DICT_6X6_250</a></code></td>
<td class="colLast"><code>10</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_6X6_50">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_6X6_50">DICT_6X6_50</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_7X7_100">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_7X7_100">DICT_7X7_100</a></code></td>
<td class="colLast"><code>13</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_7X7_1000">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_7X7_1000">DICT_7X7_1000</a></code></td>
<td class="colLast"><code>15</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_7X7_250">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_7X7_250">DICT_7X7_250</a></code></td>
<td class="colLast"><code>14</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_7X7_50">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_7X7_50">DICT_7X7_50</a></code></td>
<td class="colLast"><code>12</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_APRILTAG_16h5">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_APRILTAG_16h5">DICT_APRILTAG_16h5</a></code></td>
<td class="colLast"><code>17</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_APRILTAG_25h9">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_APRILTAG_25h9">DICT_APRILTAG_25h9</a></code></td>
<td class="colLast"><code>18</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_APRILTAG_36h10">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_APRILTAG_36h10">DICT_APRILTAG_36h10</a></code></td>
<td class="colLast"><code>19</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_APRILTAG_36h11">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_APRILTAG_36h11">DICT_APRILTAG_36h11</a></code></td>
<td class="colLast"><code>20</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_ARUCO_MIP_36h12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_ARUCO_MIP_36h12">DICT_ARUCO_MIP_36h12</a></code></td>
<td class="colLast"><code>21</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.Objdetect.DICT_ARUCO_ORIGINAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/Objdetect.html#DICT_ARUCO_ORIGINAL">DICT_ARUCO_ORIGINAL</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.objdetect.<a href="org/opencv/objdetect/QRCodeEncoder.html" title="class in org.opencv.objdetect">QRCodeEncoder</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.QRCodeEncoder.CORRECT_LEVEL_H">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/QRCodeEncoder.html#CORRECT_LEVEL_H">CORRECT_LEVEL_H</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.QRCodeEncoder.CORRECT_LEVEL_L">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/QRCodeEncoder.html#CORRECT_LEVEL_L">CORRECT_LEVEL_L</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.QRCodeEncoder.CORRECT_LEVEL_M">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/QRCodeEncoder.html#CORRECT_LEVEL_M">CORRECT_LEVEL_M</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.QRCodeEncoder.CORRECT_LEVEL_Q">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/QRCodeEncoder.html#CORRECT_LEVEL_Q">CORRECT_LEVEL_Q</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.QRCodeEncoder.ECI_UTF8">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/QRCodeEncoder.html#ECI_UTF8">ECI_UTF8</a></code></td>
<td class="colLast"><code>26</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.QRCodeEncoder.MODE_ALPHANUMERIC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/QRCodeEncoder.html#MODE_ALPHANUMERIC">MODE_ALPHANUMERIC</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.QRCodeEncoder.MODE_AUTO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/QRCodeEncoder.html#MODE_AUTO">MODE_AUTO</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.QRCodeEncoder.MODE_BYTE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/QRCodeEncoder.html#MODE_BYTE">MODE_BYTE</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.QRCodeEncoder.MODE_ECI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/QRCodeEncoder.html#MODE_ECI">MODE_ECI</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.QRCodeEncoder.MODE_KANJI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/QRCodeEncoder.html#MODE_KANJI">MODE_KANJI</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.objdetect.QRCodeEncoder.MODE_NUMERIC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/QRCodeEncoder.html#MODE_NUMERIC">MODE_NUMERIC</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.objdetect.QRCodeEncoder.MODE_STRUCTURED_APPEND">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/objdetect/QRCodeEncoder.html#MODE_STRUCTURED_APPEND">MODE_STRUCTURED_APPEND</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.photo.<a href="org/opencv/photo/Photo.html" title="class in org.opencv.photo">Photo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.photo.Photo.INPAINT_NS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/photo/Photo.html#INPAINT_NS">INPAINT_NS</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.photo.Photo.INPAINT_TELEA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/photo/Photo.html#INPAINT_TELEA">INPAINT_TELEA</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.photo.Photo.LDR_SIZE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/photo/Photo.html#LDR_SIZE">LDR_SIZE</a></code></td>
<td class="colLast"><code>256</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.photo.Photo.MIXED_CLONE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/photo/Photo.html#MIXED_CLONE">MIXED_CLONE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.photo.Photo.MONOCHROME_TRANSFER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/photo/Photo.html#MONOCHROME_TRANSFER">MONOCHROME_TRANSFER</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.photo.Photo.NORMAL_CLONE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/photo/Photo.html#NORMAL_CLONE">NORMAL_CLONE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.photo.Photo.NORMCONV_FILTER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/photo/Photo.html#NORMCONV_FILTER">NORMCONV_FILTER</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.photo.Photo.RECURS_FILTER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/photo/Photo.html#RECURS_FILTER">RECURS_FILTER</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.video.<a href="org/opencv/video/DISOpticalFlow.html" title="class in org.opencv.video">DISOpticalFlow</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.video.DISOpticalFlow.PRESET_FAST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/video/DISOpticalFlow.html#PRESET_FAST">PRESET_FAST</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.video.DISOpticalFlow.PRESET_MEDIUM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/video/DISOpticalFlow.html#PRESET_MEDIUM">PRESET_MEDIUM</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.video.DISOpticalFlow.PRESET_ULTRAFAST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/video/DISOpticalFlow.html#PRESET_ULTRAFAST">PRESET_ULTRAFAST</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.video.<a href="org/opencv/video/Video.html" title="class in org.opencv.video">Video</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.video.Video.MOTION_AFFINE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/video/Video.html#MOTION_AFFINE">MOTION_AFFINE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.video.Video.MOTION_EUCLIDEAN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/video/Video.html#MOTION_EUCLIDEAN">MOTION_EUCLIDEAN</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.video.Video.MOTION_HOMOGRAPHY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/video/Video.html#MOTION_HOMOGRAPHY">MOTION_HOMOGRAPHY</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.video.Video.MOTION_TRANSLATION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/video/Video.html#MOTION_TRANSLATION">MOTION_TRANSLATION</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.video.Video.OPTFLOW_FARNEBACK_GAUSSIAN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/video/Video.html#OPTFLOW_FARNEBACK_GAUSSIAN">OPTFLOW_FARNEBACK_GAUSSIAN</a></code></td>
<td class="colLast"><code>256</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.video.Video.OPTFLOW_LK_GET_MIN_EIGENVALS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/video/Video.html#OPTFLOW_LK_GET_MIN_EIGENVALS">OPTFLOW_LK_GET_MIN_EIGENVALS</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.video.Video.OPTFLOW_USE_INITIAL_FLOW">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/video/Video.html#OPTFLOW_USE_INITIAL_FLOW">OPTFLOW_USE_INITIAL_FLOW</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.video.Video.TrackerSamplerCSC_MODE_DETECT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/video/Video.html#TrackerSamplerCSC_MODE_DETECT">TrackerSamplerCSC_MODE_DETECT</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.video.Video.TrackerSamplerCSC_MODE_INIT_NEG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/video/Video.html#TrackerSamplerCSC_MODE_INIT_NEG">TrackerSamplerCSC_MODE_INIT_NEG</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.video.Video.TrackerSamplerCSC_MODE_INIT_POS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/video/Video.html#TrackerSamplerCSC_MODE_INIT_POS">TrackerSamplerCSC_MODE_INIT_POS</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.video.Video.TrackerSamplerCSC_MODE_TRACK_NEG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/video/Video.html#TrackerSamplerCSC_MODE_TRACK_NEG">TrackerSamplerCSC_MODE_TRACK_NEG</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.video.Video.TrackerSamplerCSC_MODE_TRACK_POS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/video/Video.html#TrackerSamplerCSC_MODE_TRACK_POS">TrackerSamplerCSC_MODE_TRACK_POS</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>org.opencv.videoio.<a href="org/opencv/videoio/Videoio.html" title="class in org.opencv.videoio">Videoio</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_ANDROID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_ANDROID">CAP_ANDROID</a></code></td>
<td class="colLast"><code>1000</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_ANY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_ANY">CAP_ANY</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_ARAVIS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_ARAVIS">CAP_ARAVIS</a></code></td>
<td class="colLast"><code>2100</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_AVFOUNDATION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_AVFOUNDATION">CAP_AVFOUNDATION</a></code></td>
<td class="colLast"><code>1200</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_CMU1394">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_CMU1394">CAP_CMU1394</a></code></td>
<td class="colLast"><code>300</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_DC1394">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_DC1394">CAP_DC1394</a></code></td>
<td class="colLast"><code>300</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_DSHOW">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_DSHOW">CAP_DSHOW</a></code></td>
<td class="colLast"><code>700</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_FFMPEG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_FFMPEG">CAP_FFMPEG</a></code></td>
<td class="colLast"><code>1900</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_FIREWARE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_FIREWARE">CAP_FIREWARE</a></code></td>
<td class="colLast"><code>300</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_FIREWIRE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_FIREWIRE">CAP_FIREWIRE</a></code></td>
<td class="colLast"><code>300</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_GIGANETIX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_GIGANETIX">CAP_GIGANETIX</a></code></td>
<td class="colLast"><code>1300</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_GPHOTO2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_GPHOTO2">CAP_GPHOTO2</a></code></td>
<td class="colLast"><code>1700</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_GSTREAMER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_GSTREAMER">CAP_GSTREAMER</a></code></td>
<td class="colLast"><code>1800</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_IEEE1394">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_IEEE1394">CAP_IEEE1394</a></code></td>
<td class="colLast"><code>300</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_IMAGES">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_IMAGES">CAP_IMAGES</a></code></td>
<td class="colLast"><code>2000</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_INTEL_MFX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_INTEL_MFX">CAP_INTEL_MFX</a></code></td>
<td class="colLast"><code>2300</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_INTELPERC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_INTELPERC">CAP_INTELPERC</a></code></td>
<td class="colLast"><code>1500</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_INTELPERC_DEPTH_GENERATOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_INTELPERC_DEPTH_GENERATOR">CAP_INTELPERC_DEPTH_GENERATOR</a></code></td>
<td class="colLast"><code>536870912</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_INTELPERC_DEPTH_MAP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_INTELPERC_DEPTH_MAP">CAP_INTELPERC_DEPTH_MAP</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_INTELPERC_GENERATORS_MASK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_INTELPERC_GENERATORS_MASK">CAP_INTELPERC_GENERATORS_MASK</a></code></td>
<td class="colLast"><code>939524096</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_INTELPERC_IMAGE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_INTELPERC_IMAGE">CAP_INTELPERC_IMAGE</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_INTELPERC_IMAGE_GENERATOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_INTELPERC_IMAGE_GENERATOR">CAP_INTELPERC_IMAGE_GENERATOR</a></code></td>
<td class="colLast"><code>268435456</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_INTELPERC_IR_GENERATOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_INTELPERC_IR_GENERATOR">CAP_INTELPERC_IR_GENERATOR</a></code></td>
<td class="colLast"><code>134217728</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_INTELPERC_IR_MAP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_INTELPERC_IR_MAP">CAP_INTELPERC_IR_MAP</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_INTELPERC_UVDEPTH_MAP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_INTELPERC_UVDEPTH_MAP">CAP_INTELPERC_UVDEPTH_MAP</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_MSMF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_MSMF">CAP_MSMF</a></code></td>
<td class="colLast"><code>1400</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OBSENSOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OBSENSOR">CAP_OBSENSOR</a></code></td>
<td class="colLast"><code>2600</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OBSENSOR_BGR_IMAGE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OBSENSOR_BGR_IMAGE">CAP_OBSENSOR_BGR_IMAGE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OBSENSOR_DEPTH_GENERATOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OBSENSOR_DEPTH_GENERATOR">CAP_OBSENSOR_DEPTH_GENERATOR</a></code></td>
<td class="colLast"><code>536870912</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OBSENSOR_DEPTH_MAP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OBSENSOR_DEPTH_MAP">CAP_OBSENSOR_DEPTH_MAP</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OBSENSOR_GENERATORS_MASK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OBSENSOR_GENERATORS_MASK">CAP_OBSENSOR_GENERATORS_MASK</a></code></td>
<td class="colLast"><code>939524096</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OBSENSOR_IMAGE_GENERATOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OBSENSOR_IMAGE_GENERATOR">CAP_OBSENSOR_IMAGE_GENERATOR</a></code></td>
<td class="colLast"><code>268435456</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OBSENSOR_IR_GENERATOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OBSENSOR_IR_GENERATOR">CAP_OBSENSOR_IR_GENERATOR</a></code></td>
<td class="colLast"><code>134217728</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OBSENSOR_IR_IMAGE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OBSENSOR_IR_IMAGE">CAP_OBSENSOR_IR_IMAGE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENCV_MJPEG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENCV_MJPEG">CAP_OPENCV_MJPEG</a></code></td>
<td class="colLast"><code>2200</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI">CAP_OPENNI</a></code></td>
<td class="colLast"><code>900</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_ASUS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_ASUS">CAP_OPENNI_ASUS</a></code></td>
<td class="colLast"><code>910</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_BGR_IMAGE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_BGR_IMAGE">CAP_OPENNI_BGR_IMAGE</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_GENERATOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_DEPTH_GENERATOR">CAP_OPENNI_DEPTH_GENERATOR</a></code></td>
<td class="colLast"><code>-2147483648</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_GENERATOR_BASELINE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_DEPTH_GENERATOR_BASELINE">CAP_OPENNI_DEPTH_GENERATOR_BASELINE</a></code></td>
<td class="colLast"><code>-2147483546</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_GENERATOR_FOCAL_LENGTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_DEPTH_GENERATOR_FOCAL_LENGTH">CAP_OPENNI_DEPTH_GENERATOR_FOCAL_LENGTH</a></code></td>
<td class="colLast"><code>-2147483545</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_GENERATOR_PRESENT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_DEPTH_GENERATOR_PRESENT">CAP_OPENNI_DEPTH_GENERATOR_PRESENT</a></code></td>
<td class="colLast"><code>-2147483539</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION">CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION</a></code></td>
<td class="colLast"><code>-2147483544</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION_ON">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION_ON">CAP_OPENNI_DEPTH_GENERATOR_REGISTRATION_ON</a></code></td>
<td class="colLast"><code>-2147483544</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_DEPTH_MAP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_DEPTH_MAP">CAP_OPENNI_DEPTH_MAP</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_DISPARITY_MAP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_DISPARITY_MAP">CAP_OPENNI_DISPARITY_MAP</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_DISPARITY_MAP_32F">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_DISPARITY_MAP_32F">CAP_OPENNI_DISPARITY_MAP_32F</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_GENERATORS_MASK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_GENERATORS_MASK">CAP_OPENNI_GENERATORS_MASK</a></code></td>
<td class="colLast"><code>-536870912</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_GRAY_IMAGE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_GRAY_IMAGE">CAP_OPENNI_GRAY_IMAGE</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_IMAGE_GENERATOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_IMAGE_GENERATOR">CAP_OPENNI_IMAGE_GENERATOR</a></code></td>
<td class="colLast"><code>1073741824</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_IMAGE_GENERATOR_OUTPUT_MODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_IMAGE_GENERATOR_OUTPUT_MODE">CAP_OPENNI_IMAGE_GENERATOR_OUTPUT_MODE</a></code></td>
<td class="colLast"><code>1073741924</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_IMAGE_GENERATOR_PRESENT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_IMAGE_GENERATOR_PRESENT">CAP_OPENNI_IMAGE_GENERATOR_PRESENT</a></code></td>
<td class="colLast"><code>1073741933</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_IR_GENERATOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_IR_GENERATOR">CAP_OPENNI_IR_GENERATOR</a></code></td>
<td class="colLast"><code>536870912</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_IR_GENERATOR_PRESENT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_IR_GENERATOR_PRESENT">CAP_OPENNI_IR_GENERATOR_PRESENT</a></code></td>
<td class="colLast"><code>536871021</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_IR_IMAGE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_IR_IMAGE">CAP_OPENNI_IR_IMAGE</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_POINT_CLOUD_MAP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_POINT_CLOUD_MAP">CAP_OPENNI_POINT_CLOUD_MAP</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_QVGA_30HZ">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_QVGA_30HZ">CAP_OPENNI_QVGA_30HZ</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_QVGA_60HZ">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_QVGA_60HZ">CAP_OPENNI_QVGA_60HZ</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_SXGA_15HZ">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_SXGA_15HZ">CAP_OPENNI_SXGA_15HZ</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_SXGA_30HZ">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_SXGA_30HZ">CAP_OPENNI_SXGA_30HZ</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_VALID_DEPTH_MASK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_VALID_DEPTH_MASK">CAP_OPENNI_VALID_DEPTH_MASK</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI_VGA_30HZ">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI_VGA_30HZ">CAP_OPENNI_VGA_30HZ</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI2">CAP_OPENNI2</a></code></td>
<td class="colLast"><code>1600</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI2_ASTRA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI2_ASTRA">CAP_OPENNI2_ASTRA</a></code></td>
<td class="colLast"><code>1620</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_OPENNI2_ASUS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_OPENNI2_ASUS">CAP_OPENNI2_ASUS</a></code></td>
<td class="colLast"><code>1610</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_APERTURE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_APERTURE">CAP_PROP_APERTURE</a></code></td>
<td class="colLast"><code>17008</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_ARAVIS_AUTOTRIGGER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_ARAVIS_AUTOTRIGGER">CAP_PROP_ARAVIS_AUTOTRIGGER</a></code></td>
<td class="colLast"><code>600</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_AUDIO_BASE_INDEX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_AUDIO_BASE_INDEX">CAP_PROP_AUDIO_BASE_INDEX</a></code></td>
<td class="colLast"><code>63</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_AUDIO_DATA_DEPTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_AUDIO_DATA_DEPTH">CAP_PROP_AUDIO_DATA_DEPTH</a></code></td>
<td class="colLast"><code>61</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_AUDIO_POS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_AUDIO_POS">CAP_PROP_AUDIO_POS</a></code></td>
<td class="colLast"><code>59</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_AUDIO_SAMPLES_PER_SECOND">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_AUDIO_SAMPLES_PER_SECOND">CAP_PROP_AUDIO_SAMPLES_PER_SECOND</a></code></td>
<td class="colLast"><code>62</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_AUDIO_SHIFT_NSEC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_AUDIO_SHIFT_NSEC">CAP_PROP_AUDIO_SHIFT_NSEC</a></code></td>
<td class="colLast"><code>60</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_AUDIO_STREAM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_AUDIO_STREAM">CAP_PROP_AUDIO_STREAM</a></code></td>
<td class="colLast"><code>58</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_AUDIO_SYNCHRONIZE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_AUDIO_SYNCHRONIZE">CAP_PROP_AUDIO_SYNCHRONIZE</a></code></td>
<td class="colLast"><code>66</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_AUDIO_TOTAL_CHANNELS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_AUDIO_TOTAL_CHANNELS">CAP_PROP_AUDIO_TOTAL_CHANNELS</a></code></td>
<td class="colLast"><code>64</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_AUDIO_TOTAL_STREAMS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_AUDIO_TOTAL_STREAMS">CAP_PROP_AUDIO_TOTAL_STREAMS</a></code></td>
<td class="colLast"><code>65</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_AUTO_EXPOSURE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_AUTO_EXPOSURE">CAP_PROP_AUTO_EXPOSURE</a></code></td>
<td class="colLast"><code>21</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_AUTO_WB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_AUTO_WB">CAP_PROP_AUTO_WB</a></code></td>
<td class="colLast"><code>44</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_AUTOFOCUS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_AUTOFOCUS">CAP_PROP_AUTOFOCUS</a></code></td>
<td class="colLast"><code>39</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_BACKEND">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_BACKEND">CAP_PROP_BACKEND</a></code></td>
<td class="colLast"><code>42</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_BACKLIGHT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_BACKLIGHT">CAP_PROP_BACKLIGHT</a></code></td>
<td class="colLast"><code>32</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_BITRATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_BITRATE">CAP_PROP_BITRATE</a></code></td>
<td class="colLast"><code>47</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_BRIGHTNESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_BRIGHTNESS">CAP_PROP_BRIGHTNESS</a></code></td>
<td class="colLast"><code>10</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_BUFFERSIZE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_BUFFERSIZE">CAP_PROP_BUFFERSIZE</a></code></td>
<td class="colLast"><code>38</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_CHANNEL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_CHANNEL">CAP_PROP_CHANNEL</a></code></td>
<td class="colLast"><code>43</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_CODEC_EXTRADATA_INDEX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_CODEC_EXTRADATA_INDEX">CAP_PROP_CODEC_EXTRADATA_INDEX</a></code></td>
<td class="colLast"><code>68</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_CODEC_PIXEL_FORMAT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_CODEC_PIXEL_FORMAT">CAP_PROP_CODEC_PIXEL_FORMAT</a></code></td>
<td class="colLast"><code>46</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_CONTRAST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_CONTRAST">CAP_PROP_CONTRAST</a></code></td>
<td class="colLast"><code>11</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_CONVERT_RGB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_CONVERT_RGB">CAP_PROP_CONVERT_RGB</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_DC1394_MAX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_DC1394_MAX">CAP_PROP_DC1394_MAX</a></code></td>
<td class="colLast"><code>31</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_DC1394_MODE_AUTO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_DC1394_MODE_AUTO">CAP_PROP_DC1394_MODE_AUTO</a></code></td>
<td class="colLast"><code>-2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_DC1394_MODE_MANUAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_DC1394_MODE_MANUAL">CAP_PROP_DC1394_MODE_MANUAL</a></code></td>
<td class="colLast"><code>-3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_DC1394_MODE_ONE_PUSH_AUTO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_DC1394_MODE_ONE_PUSH_AUTO">CAP_PROP_DC1394_MODE_ONE_PUSH_AUTO</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_DC1394_OFF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_DC1394_OFF">CAP_PROP_DC1394_OFF</a></code></td>
<td class="colLast"><code>-4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_EXPOSURE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_EXPOSURE">CAP_PROP_EXPOSURE</a></code></td>
<td class="colLast"><code>15</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_EXPOSUREPROGRAM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_EXPOSUREPROGRAM">CAP_PROP_EXPOSUREPROGRAM</a></code></td>
<td class="colLast"><code>17009</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_FOCUS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_FOCUS">CAP_PROP_FOCUS</a></code></td>
<td class="colLast"><code>28</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_FORMAT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_FORMAT">CAP_PROP_FORMAT</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_FOURCC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_FOURCC">CAP_PROP_FOURCC</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_FPS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_FPS">CAP_PROP_FPS</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_FRAME_COUNT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_FRAME_COUNT">CAP_PROP_FRAME_COUNT</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_FRAME_HEIGHT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_FRAME_HEIGHT">CAP_PROP_FRAME_HEIGHT</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_FRAME_TYPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_FRAME_TYPE">CAP_PROP_FRAME_TYPE</a></code></td>
<td class="colLast"><code>69</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_FRAME_WIDTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_FRAME_WIDTH">CAP_PROP_FRAME_WIDTH</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_GAIN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_GAIN">CAP_PROP_GAIN</a></code></td>
<td class="colLast"><code>14</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_GAMMA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_GAMMA">CAP_PROP_GAMMA</a></code></td>
<td class="colLast"><code>22</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_GIGA_FRAME_HEIGH_MAX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_GIGA_FRAME_HEIGH_MAX">CAP_PROP_GIGA_FRAME_HEIGH_MAX</a></code></td>
<td class="colLast"><code>10004</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_GIGA_FRAME_OFFSET_X">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_GIGA_FRAME_OFFSET_X">CAP_PROP_GIGA_FRAME_OFFSET_X</a></code></td>
<td class="colLast"><code>10001</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_GIGA_FRAME_OFFSET_Y">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_GIGA_FRAME_OFFSET_Y">CAP_PROP_GIGA_FRAME_OFFSET_Y</a></code></td>
<td class="colLast"><code>10002</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_GIGA_FRAME_SENS_HEIGH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_GIGA_FRAME_SENS_HEIGH">CAP_PROP_GIGA_FRAME_SENS_HEIGH</a></code></td>
<td class="colLast"><code>10006</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_GIGA_FRAME_SENS_WIDTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_GIGA_FRAME_SENS_WIDTH">CAP_PROP_GIGA_FRAME_SENS_WIDTH</a></code></td>
<td class="colLast"><code>10005</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_GIGA_FRAME_WIDTH_MAX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_GIGA_FRAME_WIDTH_MAX">CAP_PROP_GIGA_FRAME_WIDTH_MAX</a></code></td>
<td class="colLast"><code>10003</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_GPHOTO2_COLLECT_MSGS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_GPHOTO2_COLLECT_MSGS">CAP_PROP_GPHOTO2_COLLECT_MSGS</a></code></td>
<td class="colLast"><code>17005</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_GPHOTO2_FLUSH_MSGS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_GPHOTO2_FLUSH_MSGS">CAP_PROP_GPHOTO2_FLUSH_MSGS</a></code></td>
<td class="colLast"><code>17006</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_GPHOTO2_PREVIEW">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_GPHOTO2_PREVIEW">CAP_PROP_GPHOTO2_PREVIEW</a></code></td>
<td class="colLast"><code>17001</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_GPHOTO2_RELOAD_CONFIG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_GPHOTO2_RELOAD_CONFIG">CAP_PROP_GPHOTO2_RELOAD_CONFIG</a></code></td>
<td class="colLast"><code>17003</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_GPHOTO2_RELOAD_ON_CHANGE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_GPHOTO2_RELOAD_ON_CHANGE">CAP_PROP_GPHOTO2_RELOAD_ON_CHANGE</a></code></td>
<td class="colLast"><code>17004</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_GPHOTO2_WIDGET_ENUMERATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_GPHOTO2_WIDGET_ENUMERATE">CAP_PROP_GPHOTO2_WIDGET_ENUMERATE</a></code></td>
<td class="colLast"><code>17002</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_GSTREAMER_QUEUE_LENGTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_GSTREAMER_QUEUE_LENGTH">CAP_PROP_GSTREAMER_QUEUE_LENGTH</a></code></td>
<td class="colLast"><code>200</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_GUID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_GUID">CAP_PROP_GUID</a></code></td>
<td class="colLast"><code>29</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_HUE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_HUE">CAP_PROP_HUE</a></code></td>
<td class="colLast"><code>13</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_HW_ACCELERATION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_HW_ACCELERATION">CAP_PROP_HW_ACCELERATION</a></code></td>
<td class="colLast"><code>50</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_HW_ACCELERATION_USE_OPENCL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_HW_ACCELERATION_USE_OPENCL">CAP_PROP_HW_ACCELERATION_USE_OPENCL</a></code></td>
<td class="colLast"><code>52</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_HW_DEVICE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_HW_DEVICE">CAP_PROP_HW_DEVICE</a></code></td>
<td class="colLast"><code>51</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_IMAGES_BASE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_IMAGES_BASE">CAP_PROP_IMAGES_BASE</a></code></td>
<td class="colLast"><code>18000</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_IMAGES_LAST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_IMAGES_LAST">CAP_PROP_IMAGES_LAST</a></code></td>
<td class="colLast"><code>19000</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_DEPTH_CONFIDENCE_THRESHOLD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_INTELPERC_DEPTH_CONFIDENCE_THRESHOLD">CAP_PROP_INTELPERC_DEPTH_CONFIDENCE_THRESHOLD</a></code></td>
<td class="colLast"><code>11005</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_HORZ">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_HORZ">CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_HORZ</a></code></td>
<td class="colLast"><code>11006</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_VERT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_VERT">CAP_PROP_INTELPERC_DEPTH_FOCAL_LENGTH_VERT</a></code></td>
<td class="colLast"><code>11007</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_DEPTH_LOW_CONFIDENCE_VALUE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_INTELPERC_DEPTH_LOW_CONFIDENCE_VALUE">CAP_PROP_INTELPERC_DEPTH_LOW_CONFIDENCE_VALUE</a></code></td>
<td class="colLast"><code>11003</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_DEPTH_SATURATION_VALUE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_INTELPERC_DEPTH_SATURATION_VALUE">CAP_PROP_INTELPERC_DEPTH_SATURATION_VALUE</a></code></td>
<td class="colLast"><code>11004</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_PROFILE_COUNT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_INTELPERC_PROFILE_COUNT">CAP_PROP_INTELPERC_PROFILE_COUNT</a></code></td>
<td class="colLast"><code>11001</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_INTELPERC_PROFILE_IDX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_INTELPERC_PROFILE_IDX">CAP_PROP_INTELPERC_PROFILE_IDX</a></code></td>
<td class="colLast"><code>11002</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_IOS_DEVICE_EXPOSURE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_IOS_DEVICE_EXPOSURE">CAP_PROP_IOS_DEVICE_EXPOSURE</a></code></td>
<td class="colLast"><code>9002</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_IOS_DEVICE_FLASH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_IOS_DEVICE_FLASH">CAP_PROP_IOS_DEVICE_FLASH</a></code></td>
<td class="colLast"><code>9003</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_IOS_DEVICE_FOCUS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_IOS_DEVICE_FOCUS">CAP_PROP_IOS_DEVICE_FOCUS</a></code></td>
<td class="colLast"><code>9001</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_IOS_DEVICE_TORCH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_IOS_DEVICE_TORCH">CAP_PROP_IOS_DEVICE_TORCH</a></code></td>
<td class="colLast"><code>9005</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_IOS_DEVICE_WHITEBALANCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_IOS_DEVICE_WHITEBALANCE">CAP_PROP_IOS_DEVICE_WHITEBALANCE</a></code></td>
<td class="colLast"><code>9004</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_IRIS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_IRIS">CAP_PROP_IRIS</a></code></td>
<td class="colLast"><code>36</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_ISO_SPEED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_ISO_SPEED">CAP_PROP_ISO_SPEED</a></code></td>
<td class="colLast"><code>30</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_LRF_HAS_KEY_FRAME">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_LRF_HAS_KEY_FRAME">CAP_PROP_LRF_HAS_KEY_FRAME</a></code></td>
<td class="colLast"><code>67</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_MODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_MODE">CAP_PROP_MODE</a></code></td>
<td class="colLast"><code>9</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_MONOCHROME">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_MONOCHROME">CAP_PROP_MONOCHROME</a></code></td>
<td class="colLast"><code>19</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_N_THREADS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_N_THREADS">CAP_PROP_N_THREADS</a></code></td>
<td class="colLast"><code>70</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_OBSENSOR_INTRINSIC_CX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_OBSENSOR_INTRINSIC_CX">CAP_PROP_OBSENSOR_INTRINSIC_CX</a></code></td>
<td class="colLast"><code>26003</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_OBSENSOR_INTRINSIC_CY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_OBSENSOR_INTRINSIC_CY">CAP_PROP_OBSENSOR_INTRINSIC_CY</a></code></td>
<td class="colLast"><code>26004</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_OBSENSOR_INTRINSIC_FX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_OBSENSOR_INTRINSIC_FX">CAP_PROP_OBSENSOR_INTRINSIC_FX</a></code></td>
<td class="colLast"><code>26001</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_OBSENSOR_INTRINSIC_FY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_OBSENSOR_INTRINSIC_FY">CAP_PROP_OBSENSOR_INTRINSIC_FY</a></code></td>
<td class="colLast"><code>26002</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_OPEN_TIMEOUT_MSEC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_OPEN_TIMEOUT_MSEC">CAP_PROP_OPEN_TIMEOUT_MSEC</a></code></td>
<td class="colLast"><code>53</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_OPENNI_APPROX_FRAME_SYNC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_APPROX_FRAME_SYNC">CAP_PROP_OPENNI_APPROX_FRAME_SYNC</a></code></td>
<td class="colLast"><code>105</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_OPENNI_BASELINE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_BASELINE">CAP_PROP_OPENNI_BASELINE</a></code></td>
<td class="colLast"><code>102</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_OPENNI_CIRCLE_BUFFER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_CIRCLE_BUFFER">CAP_PROP_OPENNI_CIRCLE_BUFFER</a></code></td>
<td class="colLast"><code>107</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_OPENNI_FOCAL_LENGTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_FOCAL_LENGTH">CAP_PROP_OPENNI_FOCAL_LENGTH</a></code></td>
<td class="colLast"><code>103</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_OPENNI_FRAME_MAX_DEPTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_FRAME_MAX_DEPTH">CAP_PROP_OPENNI_FRAME_MAX_DEPTH</a></code></td>
<td class="colLast"><code>101</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_OPENNI_GENERATOR_PRESENT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_GENERATOR_PRESENT">CAP_PROP_OPENNI_GENERATOR_PRESENT</a></code></td>
<td class="colLast"><code>109</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_OPENNI_MAX_BUFFER_SIZE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_MAX_BUFFER_SIZE">CAP_PROP_OPENNI_MAX_BUFFER_SIZE</a></code></td>
<td class="colLast"><code>106</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_OPENNI_MAX_TIME_DURATION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_MAX_TIME_DURATION">CAP_PROP_OPENNI_MAX_TIME_DURATION</a></code></td>
<td class="colLast"><code>108</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_OPENNI_OUTPUT_MODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_OUTPUT_MODE">CAP_PROP_OPENNI_OUTPUT_MODE</a></code></td>
<td class="colLast"><code>100</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_OPENNI_REGISTRATION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_REGISTRATION">CAP_PROP_OPENNI_REGISTRATION</a></code></td>
<td class="colLast"><code>104</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_OPENNI_REGISTRATION_ON">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI_REGISTRATION_ON">CAP_PROP_OPENNI_REGISTRATION_ON</a></code></td>
<td class="colLast"><code>104</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_OPENNI2_MIRROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI2_MIRROR">CAP_PROP_OPENNI2_MIRROR</a></code></td>
<td class="colLast"><code>111</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_OPENNI2_SYNC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_OPENNI2_SYNC">CAP_PROP_OPENNI2_SYNC</a></code></td>
<td class="colLast"><code>110</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_ORIENTATION_AUTO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_ORIENTATION_AUTO">CAP_PROP_ORIENTATION_AUTO</a></code></td>
<td class="colLast"><code>49</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_ORIENTATION_META">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_ORIENTATION_META">CAP_PROP_ORIENTATION_META</a></code></td>
<td class="colLast"><code>48</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_PAN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_PAN">CAP_PROP_PAN</a></code></td>
<td class="colLast"><code>33</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_POS_AVI_RATIO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_POS_AVI_RATIO">CAP_PROP_POS_AVI_RATIO</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_POS_FRAMES">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_POS_FRAMES">CAP_PROP_POS_FRAMES</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_POS_MSEC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_POS_MSEC">CAP_PROP_POS_MSEC</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_PVAPI_BINNINGX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_PVAPI_BINNINGX">CAP_PROP_PVAPI_BINNINGX</a></code></td>
<td class="colLast"><code>304</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_PVAPI_BINNINGY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_PVAPI_BINNINGY">CAP_PROP_PVAPI_BINNINGY</a></code></td>
<td class="colLast"><code>305</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_PVAPI_DECIMATIONHORIZONTAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_PVAPI_DECIMATIONHORIZONTAL">CAP_PROP_PVAPI_DECIMATIONHORIZONTAL</a></code></td>
<td class="colLast"><code>302</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_PVAPI_DECIMATIONVERTICAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_PVAPI_DECIMATIONVERTICAL">CAP_PROP_PVAPI_DECIMATIONVERTICAL</a></code></td>
<td class="colLast"><code>303</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_PVAPI_FRAMESTARTTRIGGERMODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_PVAPI_FRAMESTARTTRIGGERMODE">CAP_PROP_PVAPI_FRAMESTARTTRIGGERMODE</a></code></td>
<td class="colLast"><code>301</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_PVAPI_MULTICASTIP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_PVAPI_MULTICASTIP">CAP_PROP_PVAPI_MULTICASTIP</a></code></td>
<td class="colLast"><code>300</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_PVAPI_PIXELFORMAT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_PVAPI_PIXELFORMAT">CAP_PROP_PVAPI_PIXELFORMAT</a></code></td>
<td class="colLast"><code>306</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_READ_TIMEOUT_MSEC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_READ_TIMEOUT_MSEC">CAP_PROP_READ_TIMEOUT_MSEC</a></code></td>
<td class="colLast"><code>54</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_RECTIFICATION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_RECTIFICATION">CAP_PROP_RECTIFICATION</a></code></td>
<td class="colLast"><code>18</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_ROLL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_ROLL">CAP_PROP_ROLL</a></code></td>
<td class="colLast"><code>35</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_SAR_DEN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_SAR_DEN">CAP_PROP_SAR_DEN</a></code></td>
<td class="colLast"><code>41</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_SAR_NUM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_SAR_NUM">CAP_PROP_SAR_NUM</a></code></td>
<td class="colLast"><code>40</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_SATURATION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_SATURATION">CAP_PROP_SATURATION</a></code></td>
<td class="colLast"><code>12</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_SETTINGS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_SETTINGS">CAP_PROP_SETTINGS</a></code></td>
<td class="colLast"><code>37</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_SHARPNESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_SHARPNESS">CAP_PROP_SHARPNESS</a></code></td>
<td class="colLast"><code>20</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_SPEED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_SPEED">CAP_PROP_SPEED</a></code></td>
<td class="colLast"><code>17007</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_STREAM_OPEN_TIME_USEC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_STREAM_OPEN_TIME_USEC">CAP_PROP_STREAM_OPEN_TIME_USEC</a></code></td>
<td class="colLast"><code>55</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_TEMPERATURE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_TEMPERATURE">CAP_PROP_TEMPERATURE</a></code></td>
<td class="colLast"><code>23</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_TILT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_TILT">CAP_PROP_TILT</a></code></td>
<td class="colLast"><code>34</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_TRIGGER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_TRIGGER">CAP_PROP_TRIGGER</a></code></td>
<td class="colLast"><code>24</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_TRIGGER_DELAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_TRIGGER_DELAY">CAP_PROP_TRIGGER_DELAY</a></code></td>
<td class="colLast"><code>25</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_VIDEO_STREAM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_VIDEO_STREAM">CAP_PROP_VIDEO_STREAM</a></code></td>
<td class="colLast"><code>57</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_VIDEO_TOTAL_CHANNELS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_VIDEO_TOTAL_CHANNELS">CAP_PROP_VIDEO_TOTAL_CHANNELS</a></code></td>
<td class="colLast"><code>56</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_VIEWFINDER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_VIEWFINDER">CAP_PROP_VIEWFINDER</a></code></td>
<td class="colLast"><code>17010</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_WB_TEMPERATURE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_WB_TEMPERATURE">CAP_PROP_WB_TEMPERATURE</a></code></td>
<td class="colLast"><code>45</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_WHITE_BALANCE_BLUE_U">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_WHITE_BALANCE_BLUE_U">CAP_PROP_WHITE_BALANCE_BLUE_U</a></code></td>
<td class="colLast"><code>17</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_WHITE_BALANCE_RED_V">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_WHITE_BALANCE_RED_V">CAP_PROP_WHITE_BALANCE_RED_V</a></code></td>
<td class="colLast"><code>26</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_ACQ_BUFFER_SIZE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_ACQ_BUFFER_SIZE">CAP_PROP_XI_ACQ_BUFFER_SIZE</a></code></td>
<td class="colLast"><code>548</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_ACQ_BUFFER_SIZE_UNIT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_ACQ_BUFFER_SIZE_UNIT">CAP_PROP_XI_ACQ_BUFFER_SIZE_UNIT</a></code></td>
<td class="colLast"><code>549</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_ACQ_FRAME_BURST_COUNT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_ACQ_FRAME_BURST_COUNT">CAP_PROP_XI_ACQ_FRAME_BURST_COUNT</a></code></td>
<td class="colLast"><code>499</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_ACQ_TIMING_MODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_ACQ_TIMING_MODE">CAP_PROP_XI_ACQ_TIMING_MODE</a></code></td>
<td class="colLast"><code>538</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_COMMIT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_COMMIT">CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_COMMIT</a></code></td>
<td class="colLast"><code>552</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_SIZE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_SIZE">CAP_PROP_XI_ACQ_TRANSPORT_BUFFER_SIZE</a></code></td>
<td class="colLast"><code>550</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_AE_MAX_LIMIT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_AE_MAX_LIMIT">CAP_PROP_XI_AE_MAX_LIMIT</a></code></td>
<td class="colLast"><code>417</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_AEAG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_AEAG">CAP_PROP_XI_AEAG</a></code></td>
<td class="colLast"><code>415</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_AEAG_LEVEL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_AEAG_LEVEL">CAP_PROP_XI_AEAG_LEVEL</a></code></td>
<td class="colLast"><code>419</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_AEAG_ROI_HEIGHT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_AEAG_ROI_HEIGHT">CAP_PROP_XI_AEAG_ROI_HEIGHT</a></code></td>
<td class="colLast"><code>442</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_AEAG_ROI_OFFSET_X">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_AEAG_ROI_OFFSET_X">CAP_PROP_XI_AEAG_ROI_OFFSET_X</a></code></td>
<td class="colLast"><code>439</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_AEAG_ROI_OFFSET_Y">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_AEAG_ROI_OFFSET_Y">CAP_PROP_XI_AEAG_ROI_OFFSET_Y</a></code></td>
<td class="colLast"><code>440</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_AEAG_ROI_WIDTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_AEAG_ROI_WIDTH">CAP_PROP_XI_AEAG_ROI_WIDTH</a></code></td>
<td class="colLast"><code>441</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_AG_MAX_LIMIT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_AG_MAX_LIMIT">CAP_PROP_XI_AG_MAX_LIMIT</a></code></td>
<td class="colLast"><code>418</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_APPLY_CMS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_APPLY_CMS">CAP_PROP_XI_APPLY_CMS</a></code></td>
<td class="colLast"><code>471</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_AUTO_BANDWIDTH_CALCULATION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_AUTO_BANDWIDTH_CALCULATION">CAP_PROP_XI_AUTO_BANDWIDTH_CALCULATION</a></code></td>
<td class="colLast"><code>573</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_AUTO_WB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_AUTO_WB">CAP_PROP_XI_AUTO_WB</a></code></td>
<td class="colLast"><code>414</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_AVAILABLE_BANDWIDTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_AVAILABLE_BANDWIDTH">CAP_PROP_XI_AVAILABLE_BANDWIDTH</a></code></td>
<td class="colLast"><code>539</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_BINNING_HORIZONTAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_BINNING_HORIZONTAL">CAP_PROP_XI_BINNING_HORIZONTAL</a></code></td>
<td class="colLast"><code>429</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_BINNING_PATTERN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_BINNING_PATTERN">CAP_PROP_XI_BINNING_PATTERN</a></code></td>
<td class="colLast"><code>430</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_BINNING_SELECTOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_BINNING_SELECTOR">CAP_PROP_XI_BINNING_SELECTOR</a></code></td>
<td class="colLast"><code>427</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_BINNING_VERTICAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_BINNING_VERTICAL">CAP_PROP_XI_BINNING_VERTICAL</a></code></td>
<td class="colLast"><code>428</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_BPC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_BPC">CAP_PROP_XI_BPC</a></code></td>
<td class="colLast"><code>445</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_BUFFER_POLICY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_BUFFER_POLICY">CAP_PROP_XI_BUFFER_POLICY</a></code></td>
<td class="colLast"><code>540</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_BUFFERS_QUEUE_SIZE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_BUFFERS_QUEUE_SIZE">CAP_PROP_XI_BUFFERS_QUEUE_SIZE</a></code></td>
<td class="colLast"><code>551</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_00">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_00">CAP_PROP_XI_CC_MATRIX_00</a></code></td>
<td class="colLast"><code>479</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_01">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_01">CAP_PROP_XI_CC_MATRIX_01</a></code></td>
<td class="colLast"><code>480</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_02">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_02">CAP_PROP_XI_CC_MATRIX_02</a></code></td>
<td class="colLast"><code>481</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_03">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_03">CAP_PROP_XI_CC_MATRIX_03</a></code></td>
<td class="colLast"><code>482</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_10">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_10">CAP_PROP_XI_CC_MATRIX_10</a></code></td>
<td class="colLast"><code>483</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_11">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_11">CAP_PROP_XI_CC_MATRIX_11</a></code></td>
<td class="colLast"><code>484</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_12">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_12">CAP_PROP_XI_CC_MATRIX_12</a></code></td>
<td class="colLast"><code>485</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_13">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_13">CAP_PROP_XI_CC_MATRIX_13</a></code></td>
<td class="colLast"><code>486</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_20">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_20">CAP_PROP_XI_CC_MATRIX_20</a></code></td>
<td class="colLast"><code>487</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_21">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_21">CAP_PROP_XI_CC_MATRIX_21</a></code></td>
<td class="colLast"><code>488</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_22">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_22">CAP_PROP_XI_CC_MATRIX_22</a></code></td>
<td class="colLast"><code>489</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_23">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_23">CAP_PROP_XI_CC_MATRIX_23</a></code></td>
<td class="colLast"><code>490</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_30">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_30">CAP_PROP_XI_CC_MATRIX_30</a></code></td>
<td class="colLast"><code>491</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_31">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_31">CAP_PROP_XI_CC_MATRIX_31</a></code></td>
<td class="colLast"><code>492</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_32">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_32">CAP_PROP_XI_CC_MATRIX_32</a></code></td>
<td class="colLast"><code>493</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_CC_MATRIX_33">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_CC_MATRIX_33">CAP_PROP_XI_CC_MATRIX_33</a></code></td>
<td class="colLast"><code>494</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_CHIP_TEMP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_CHIP_TEMP">CAP_PROP_XI_CHIP_TEMP</a></code></td>
<td class="colLast"><code>468</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_CMS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_CMS">CAP_PROP_XI_CMS</a></code></td>
<td class="colLast"><code>470</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_COLOR_FILTER_ARRAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_COLOR_FILTER_ARRAY">CAP_PROP_XI_COLOR_FILTER_ARRAY</a></code></td>
<td class="colLast"><code>475</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_COLUMN_FPN_CORRECTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_COLUMN_FPN_CORRECTION">CAP_PROP_XI_COLUMN_FPN_CORRECTION</a></code></td>
<td class="colLast"><code>555</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_COOLING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_COOLING">CAP_PROP_XI_COOLING</a></code></td>
<td class="colLast"><code>466</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_COUNTER_SELECTOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_COUNTER_SELECTOR">CAP_PROP_XI_COUNTER_SELECTOR</a></code></td>
<td class="colLast"><code>536</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_COUNTER_VALUE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_COUNTER_VALUE">CAP_PROP_XI_COUNTER_VALUE</a></code></td>
<td class="colLast"><code>537</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_DATA_FORMAT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_DATA_FORMAT">CAP_PROP_XI_DATA_FORMAT</a></code></td>
<td class="colLast"><code>401</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_DEBOUNCE_EN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_DEBOUNCE_EN">CAP_PROP_XI_DEBOUNCE_EN</a></code></td>
<td class="colLast"><code>507</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_DEBOUNCE_POL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_DEBOUNCE_POL">CAP_PROP_XI_DEBOUNCE_POL</a></code></td>
<td class="colLast"><code>510</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_DEBOUNCE_T0">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_DEBOUNCE_T0">CAP_PROP_XI_DEBOUNCE_T0</a></code></td>
<td class="colLast"><code>508</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_DEBOUNCE_T1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_DEBOUNCE_T1">CAP_PROP_XI_DEBOUNCE_T1</a></code></td>
<td class="colLast"><code>509</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_DEBUG_LEVEL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_DEBUG_LEVEL">CAP_PROP_XI_DEBUG_LEVEL</a></code></td>
<td class="colLast"><code>572</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_DECIMATION_HORIZONTAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_DECIMATION_HORIZONTAL">CAP_PROP_XI_DECIMATION_HORIZONTAL</a></code></td>
<td class="colLast"><code>433</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_DECIMATION_PATTERN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_DECIMATION_PATTERN">CAP_PROP_XI_DECIMATION_PATTERN</a></code></td>
<td class="colLast"><code>434</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_DECIMATION_SELECTOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_DECIMATION_SELECTOR">CAP_PROP_XI_DECIMATION_SELECTOR</a></code></td>
<td class="colLast"><code>431</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_DECIMATION_VERTICAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_DECIMATION_VERTICAL">CAP_PROP_XI_DECIMATION_VERTICAL</a></code></td>
<td class="colLast"><code>432</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_DEFAULT_CC_MATRIX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_DEFAULT_CC_MATRIX">CAP_PROP_XI_DEFAULT_CC_MATRIX</a></code></td>
<td class="colLast"><code>495</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_DEVICE_MODEL_ID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_DEVICE_MODEL_ID">CAP_PROP_XI_DEVICE_MODEL_ID</a></code></td>
<td class="colLast"><code>521</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_DEVICE_RESET">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_DEVICE_RESET">CAP_PROP_XI_DEVICE_RESET</a></code></td>
<td class="colLast"><code>554</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_DEVICE_SN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_DEVICE_SN">CAP_PROP_XI_DEVICE_SN</a></code></td>
<td class="colLast"><code>522</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_DOWNSAMPLING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_DOWNSAMPLING">CAP_PROP_XI_DOWNSAMPLING</a></code></td>
<td class="colLast"><code>400</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_DOWNSAMPLING_TYPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_DOWNSAMPLING_TYPE">CAP_PROP_XI_DOWNSAMPLING_TYPE</a></code></td>
<td class="colLast"><code>426</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_EXP_PRIORITY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_EXP_PRIORITY">CAP_PROP_XI_EXP_PRIORITY</a></code></td>
<td class="colLast"><code>416</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_EXPOSURE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_EXPOSURE">CAP_PROP_XI_EXPOSURE</a></code></td>
<td class="colLast"><code>421</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_EXPOSURE_BURST_COUNT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_EXPOSURE_BURST_COUNT">CAP_PROP_XI_EXPOSURE_BURST_COUNT</a></code></td>
<td class="colLast"><code>422</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_FFS_ACCESS_KEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_FFS_ACCESS_KEY">CAP_PROP_XI_FFS_ACCESS_KEY</a></code></td>
<td class="colLast"><code>583</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_FFS_FILE_ID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_FFS_FILE_ID">CAP_PROP_XI_FFS_FILE_ID</a></code></td>
<td class="colLast"><code>594</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_FFS_FILE_SIZE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_FFS_FILE_SIZE">CAP_PROP_XI_FFS_FILE_SIZE</a></code></td>
<td class="colLast"><code>580</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_FRAMERATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_FRAMERATE">CAP_PROP_XI_FRAMERATE</a></code></td>
<td class="colLast"><code>535</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_FREE_FFS_SIZE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_FREE_FFS_SIZE">CAP_PROP_XI_FREE_FFS_SIZE</a></code></td>
<td class="colLast"><code>581</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_GAIN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_GAIN">CAP_PROP_XI_GAIN</a></code></td>
<td class="colLast"><code>424</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_GAIN_SELECTOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_GAIN_SELECTOR">CAP_PROP_XI_GAIN_SELECTOR</a></code></td>
<td class="colLast"><code>423</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_GAMMAC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_GAMMAC">CAP_PROP_XI_GAMMAC</a></code></td>
<td class="colLast"><code>477</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_GAMMAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_GAMMAY">CAP_PROP_XI_GAMMAY</a></code></td>
<td class="colLast"><code>476</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_GPI_LEVEL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_GPI_LEVEL">CAP_PROP_XI_GPI_LEVEL</a></code></td>
<td class="colLast"><code>408</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_GPI_MODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_GPI_MODE">CAP_PROP_XI_GPI_MODE</a></code></td>
<td class="colLast"><code>407</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_GPI_SELECTOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_GPI_SELECTOR">CAP_PROP_XI_GPI_SELECTOR</a></code></td>
<td class="colLast"><code>406</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_GPO_MODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_GPO_MODE">CAP_PROP_XI_GPO_MODE</a></code></td>
<td class="colLast"><code>410</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_GPO_SELECTOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_GPO_SELECTOR">CAP_PROP_XI_GPO_SELECTOR</a></code></td>
<td class="colLast"><code>409</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_HDR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_HDR">CAP_PROP_XI_HDR</a></code></td>
<td class="colLast"><code>559</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_HDR_KNEEPOINT_COUNT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_HDR_KNEEPOINT_COUNT">CAP_PROP_XI_HDR_KNEEPOINT_COUNT</a></code></td>
<td class="colLast"><code>560</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_HDR_T1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_HDR_T1">CAP_PROP_XI_HDR_T1</a></code></td>
<td class="colLast"><code>561</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_HDR_T2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_HDR_T2">CAP_PROP_XI_HDR_T2</a></code></td>
<td class="colLast"><code>562</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_HEIGHT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_HEIGHT">CAP_PROP_XI_HEIGHT</a></code></td>
<td class="colLast"><code>452</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_HOUS_BACK_SIDE_TEMP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_HOUS_BACK_SIDE_TEMP">CAP_PROP_XI_HOUS_BACK_SIDE_TEMP</a></code></td>
<td class="colLast"><code>590</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_HOUS_TEMP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_HOUS_TEMP">CAP_PROP_XI_HOUS_TEMP</a></code></td>
<td class="colLast"><code>469</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_HW_REVISION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_HW_REVISION">CAP_PROP_XI_HW_REVISION</a></code></td>
<td class="colLast"><code>571</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_IMAGE_BLACK_LEVEL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_IMAGE_BLACK_LEVEL">CAP_PROP_XI_IMAGE_BLACK_LEVEL</a></code></td>
<td class="colLast"><code>565</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_IMAGE_DATA_BIT_DEPTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_IMAGE_DATA_BIT_DEPTH">CAP_PROP_XI_IMAGE_DATA_BIT_DEPTH</a></code></td>
<td class="colLast"><code>462</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_IMAGE_DATA_FORMAT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_IMAGE_DATA_FORMAT">CAP_PROP_XI_IMAGE_DATA_FORMAT</a></code></td>
<td class="colLast"><code>435</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_IMAGE_DATA_FORMAT_RGB32_ALPHA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_IMAGE_DATA_FORMAT_RGB32_ALPHA">CAP_PROP_XI_IMAGE_DATA_FORMAT_RGB32_ALPHA</a></code></td>
<td class="colLast"><code>529</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_IMAGE_IS_COLOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_IMAGE_IS_COLOR">CAP_PROP_XI_IMAGE_IS_COLOR</a></code></td>
<td class="colLast"><code>474</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_IMAGE_PAYLOAD_SIZE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_IMAGE_PAYLOAD_SIZE">CAP_PROP_XI_IMAGE_PAYLOAD_SIZE</a></code></td>
<td class="colLast"><code>530</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_IS_COOLED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_IS_COOLED">CAP_PROP_XI_IS_COOLED</a></code></td>
<td class="colLast"><code>465</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_IS_DEVICE_EXIST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_IS_DEVICE_EXIST">CAP_PROP_XI_IS_DEVICE_EXIST</a></code></td>
<td class="colLast"><code>547</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_KNEEPOINT1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_KNEEPOINT1">CAP_PROP_XI_KNEEPOINT1</a></code></td>
<td class="colLast"><code>563</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_KNEEPOINT2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_KNEEPOINT2">CAP_PROP_XI_KNEEPOINT2</a></code></td>
<td class="colLast"><code>564</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_LED_MODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_LED_MODE">CAP_PROP_XI_LED_MODE</a></code></td>
<td class="colLast"><code>412</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_LED_SELECTOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_LED_SELECTOR">CAP_PROP_XI_LED_SELECTOR</a></code></td>
<td class="colLast"><code>411</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_APERTURE_VALUE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_LENS_APERTURE_VALUE">CAP_PROP_XI_LENS_APERTURE_VALUE</a></code></td>
<td class="colLast"><code>512</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_FEATURE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_LENS_FEATURE">CAP_PROP_XI_LENS_FEATURE</a></code></td>
<td class="colLast"><code>518</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_FEATURE_SELECTOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_LENS_FEATURE_SELECTOR">CAP_PROP_XI_LENS_FEATURE_SELECTOR</a></code></td>
<td class="colLast"><code>517</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_FOCAL_LENGTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_LENS_FOCAL_LENGTH">CAP_PROP_XI_LENS_FOCAL_LENGTH</a></code></td>
<td class="colLast"><code>516</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_FOCUS_DISTANCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_LENS_FOCUS_DISTANCE">CAP_PROP_XI_LENS_FOCUS_DISTANCE</a></code></td>
<td class="colLast"><code>515</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_FOCUS_MOVE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_LENS_FOCUS_MOVE">CAP_PROP_XI_LENS_FOCUS_MOVE</a></code></td>
<td class="colLast"><code>514</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_FOCUS_MOVEMENT_VALUE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_LENS_FOCUS_MOVEMENT_VALUE">CAP_PROP_XI_LENS_FOCUS_MOVEMENT_VALUE</a></code></td>
<td class="colLast"><code>513</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_LENS_MODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_LENS_MODE">CAP_PROP_XI_LENS_MODE</a></code></td>
<td class="colLast"><code>511</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_LIMIT_BANDWIDTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_LIMIT_BANDWIDTH">CAP_PROP_XI_LIMIT_BANDWIDTH</a></code></td>
<td class="colLast"><code>459</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_LUT_EN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_LUT_EN">CAP_PROP_XI_LUT_EN</a></code></td>
<td class="colLast"><code>541</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_LUT_INDEX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_LUT_INDEX">CAP_PROP_XI_LUT_INDEX</a></code></td>
<td class="colLast"><code>542</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_LUT_VALUE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_LUT_VALUE">CAP_PROP_XI_LUT_VALUE</a></code></td>
<td class="colLast"><code>543</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_MANUAL_WB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_MANUAL_WB">CAP_PROP_XI_MANUAL_WB</a></code></td>
<td class="colLast"><code>413</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_OFFSET_X">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_OFFSET_X">CAP_PROP_XI_OFFSET_X</a></code></td>
<td class="colLast"><code>402</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_OFFSET_Y">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_OFFSET_Y">CAP_PROP_XI_OFFSET_Y</a></code></td>
<td class="colLast"><code>403</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_OUTPUT_DATA_BIT_DEPTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_OUTPUT_DATA_BIT_DEPTH">CAP_PROP_XI_OUTPUT_DATA_BIT_DEPTH</a></code></td>
<td class="colLast"><code>461</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_OUTPUT_DATA_PACKING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_OUTPUT_DATA_PACKING">CAP_PROP_XI_OUTPUT_DATA_PACKING</a></code></td>
<td class="colLast"><code>463</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_OUTPUT_DATA_PACKING_TYPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_OUTPUT_DATA_PACKING_TYPE">CAP_PROP_XI_OUTPUT_DATA_PACKING_TYPE</a></code></td>
<td class="colLast"><code>464</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_RECENT_FRAME">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_RECENT_FRAME">CAP_PROP_XI_RECENT_FRAME</a></code></td>
<td class="colLast"><code>553</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_REGION_MODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_REGION_MODE">CAP_PROP_XI_REGION_MODE</a></code></td>
<td class="colLast"><code>595</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_REGION_SELECTOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_REGION_SELECTOR">CAP_PROP_XI_REGION_SELECTOR</a></code></td>
<td class="colLast"><code>589</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_ROW_FPN_CORRECTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_ROW_FPN_CORRECTION">CAP_PROP_XI_ROW_FPN_CORRECTION</a></code></td>
<td class="colLast"><code>591</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_BOARD_TEMP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_SENSOR_BOARD_TEMP">CAP_PROP_XI_SENSOR_BOARD_TEMP</a></code></td>
<td class="colLast"><code>596</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_CLOCK_FREQ_HZ">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_SENSOR_CLOCK_FREQ_HZ">CAP_PROP_XI_SENSOR_CLOCK_FREQ_HZ</a></code></td>
<td class="colLast"><code>532</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_CLOCK_FREQ_INDEX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_SENSOR_CLOCK_FREQ_INDEX">CAP_PROP_XI_SENSOR_CLOCK_FREQ_INDEX</a></code></td>
<td class="colLast"><code>533</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_DATA_BIT_DEPTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_SENSOR_DATA_BIT_DEPTH">CAP_PROP_XI_SENSOR_DATA_BIT_DEPTH</a></code></td>
<td class="colLast"><code>460</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_FEATURE_SELECTOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_SENSOR_FEATURE_SELECTOR">CAP_PROP_XI_SENSOR_FEATURE_SELECTOR</a></code></td>
<td class="colLast"><code>585</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_FEATURE_VALUE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_SENSOR_FEATURE_VALUE">CAP_PROP_XI_SENSOR_FEATURE_VALUE</a></code></td>
<td class="colLast"><code>586</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_MODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_SENSOR_MODE">CAP_PROP_XI_SENSOR_MODE</a></code></td>
<td class="colLast"><code>558</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_OUTPUT_CHANNEL_COUNT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_SENSOR_OUTPUT_CHANNEL_COUNT">CAP_PROP_XI_SENSOR_OUTPUT_CHANNEL_COUNT</a></code></td>
<td class="colLast"><code>534</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_SENSOR_TAPS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_SENSOR_TAPS">CAP_PROP_XI_SENSOR_TAPS</a></code></td>
<td class="colLast"><code>437</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_SHARPNESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_SHARPNESS">CAP_PROP_XI_SHARPNESS</a></code></td>
<td class="colLast"><code>478</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_SHUTTER_TYPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_SHUTTER_TYPE">CAP_PROP_XI_SHUTTER_TYPE</a></code></td>
<td class="colLast"><code>436</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_TARGET_TEMP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_TARGET_TEMP">CAP_PROP_XI_TARGET_TEMP</a></code></td>
<td class="colLast"><code>467</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_TEST_PATTERN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_TEST_PATTERN">CAP_PROP_XI_TEST_PATTERN</a></code></td>
<td class="colLast"><code>588</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_TEST_PATTERN_GENERATOR_SELECTOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_TEST_PATTERN_GENERATOR_SELECTOR">CAP_PROP_XI_TEST_PATTERN_GENERATOR_SELECTOR</a></code></td>
<td class="colLast"><code>587</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_TIMEOUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_TIMEOUT">CAP_PROP_XI_TIMEOUT</a></code></td>
<td class="colLast"><code>420</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_TRANSPORT_PIXEL_FORMAT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_TRANSPORT_PIXEL_FORMAT">CAP_PROP_XI_TRANSPORT_PIXEL_FORMAT</a></code></td>
<td class="colLast"><code>531</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_TRG_DELAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_TRG_DELAY">CAP_PROP_XI_TRG_DELAY</a></code></td>
<td class="colLast"><code>544</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_TRG_SELECTOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_TRG_SELECTOR">CAP_PROP_XI_TRG_SELECTOR</a></code></td>
<td class="colLast"><code>498</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_TRG_SOFTWARE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_TRG_SOFTWARE">CAP_PROP_XI_TRG_SOFTWARE</a></code></td>
<td class="colLast"><code>405</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_TRG_SOURCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_TRG_SOURCE">CAP_PROP_XI_TRG_SOURCE</a></code></td>
<td class="colLast"><code>404</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_TS_RST_MODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_TS_RST_MODE">CAP_PROP_XI_TS_RST_MODE</a></code></td>
<td class="colLast"><code>545</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_TS_RST_SOURCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_TS_RST_SOURCE">CAP_PROP_XI_TS_RST_SOURCE</a></code></td>
<td class="colLast"><code>546</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_USED_FFS_SIZE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_USED_FFS_SIZE">CAP_PROP_XI_USED_FFS_SIZE</a></code></td>
<td class="colLast"><code>582</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_WB_KB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_WB_KB">CAP_PROP_XI_WB_KB</a></code></td>
<td class="colLast"><code>450</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_WB_KG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_WB_KG">CAP_PROP_XI_WB_KG</a></code></td>
<td class="colLast"><code>449</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_WB_KR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_WB_KR">CAP_PROP_XI_WB_KR</a></code></td>
<td class="colLast"><code>448</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_XI_WIDTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_XI_WIDTH">CAP_PROP_XI_WIDTH</a></code></td>
<td class="colLast"><code>451</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PROP_ZOOM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PROP_ZOOM">CAP_PROP_ZOOM</a></code></td>
<td class="colLast"><code>27</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PVAPI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PVAPI">CAP_PVAPI</a></code></td>
<td class="colLast"><code>800</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PVAPI_DECIMATION_2OUTOF16">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PVAPI_DECIMATION_2OUTOF16">CAP_PVAPI_DECIMATION_2OUTOF16</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PVAPI_DECIMATION_2OUTOF4">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PVAPI_DECIMATION_2OUTOF4">CAP_PVAPI_DECIMATION_2OUTOF4</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PVAPI_DECIMATION_2OUTOF8">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PVAPI_DECIMATION_2OUTOF8">CAP_PVAPI_DECIMATION_2OUTOF8</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PVAPI_DECIMATION_OFF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PVAPI_DECIMATION_OFF">CAP_PVAPI_DECIMATION_OFF</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PVAPI_FSTRIGMODE_FIXEDRATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PVAPI_FSTRIGMODE_FIXEDRATE">CAP_PVAPI_FSTRIGMODE_FIXEDRATE</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PVAPI_FSTRIGMODE_FREERUN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PVAPI_FSTRIGMODE_FREERUN">CAP_PVAPI_FSTRIGMODE_FREERUN</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PVAPI_FSTRIGMODE_SOFTWARE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PVAPI_FSTRIGMODE_SOFTWARE">CAP_PVAPI_FSTRIGMODE_SOFTWARE</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PVAPI_FSTRIGMODE_SYNCIN1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PVAPI_FSTRIGMODE_SYNCIN1">CAP_PVAPI_FSTRIGMODE_SYNCIN1</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PVAPI_FSTRIGMODE_SYNCIN2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PVAPI_FSTRIGMODE_SYNCIN2">CAP_PVAPI_FSTRIGMODE_SYNCIN2</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_BAYER16">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PVAPI_PIXELFORMAT_BAYER16">CAP_PVAPI_PIXELFORMAT_BAYER16</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_BAYER8">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PVAPI_PIXELFORMAT_BAYER8">CAP_PVAPI_PIXELFORMAT_BAYER8</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_BGR24">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PVAPI_PIXELFORMAT_BGR24">CAP_PVAPI_PIXELFORMAT_BGR24</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_BGRA32">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PVAPI_PIXELFORMAT_BGRA32">CAP_PVAPI_PIXELFORMAT_BGRA32</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_MONO16">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PVAPI_PIXELFORMAT_MONO16">CAP_PVAPI_PIXELFORMAT_MONO16</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_MONO8">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PVAPI_PIXELFORMAT_MONO8">CAP_PVAPI_PIXELFORMAT_MONO8</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_RGB24">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PVAPI_PIXELFORMAT_RGB24">CAP_PVAPI_PIXELFORMAT_RGB24</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_PVAPI_PIXELFORMAT_RGBA32">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_PVAPI_PIXELFORMAT_RGBA32">CAP_PVAPI_PIXELFORMAT_RGBA32</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_QT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_QT">CAP_QT</a></code></td>
<td class="colLast"><code>500</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_REALSENSE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_REALSENSE">CAP_REALSENSE</a></code></td>
<td class="colLast"><code>1500</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_UEYE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_UEYE">CAP_UEYE</a></code></td>
<td class="colLast"><code>2500</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_UNICAP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_UNICAP">CAP_UNICAP</a></code></td>
<td class="colLast"><code>600</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_V4L">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_V4L">CAP_V4L</a></code></td>
<td class="colLast"><code>200</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_V4L2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_V4L2">CAP_V4L2</a></code></td>
<td class="colLast"><code>200</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_VFW">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_VFW">CAP_VFW</a></code></td>
<td class="colLast"><code>200</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_WINRT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_WINRT">CAP_WINRT</a></code></td>
<td class="colLast"><code>1410</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_XIAPI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_XIAPI">CAP_XIAPI</a></code></td>
<td class="colLast"><code>1100</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.CAP_XINE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#CAP_XINE">CAP_XINE</a></code></td>
<td class="colLast"><code>2400</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.VIDEO_ACCELERATION_ANY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#VIDEO_ACCELERATION_ANY">VIDEO_ACCELERATION_ANY</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.VIDEO_ACCELERATION_D3D11">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#VIDEO_ACCELERATION_D3D11">VIDEO_ACCELERATION_D3D11</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.VIDEO_ACCELERATION_MFX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#VIDEO_ACCELERATION_MFX">VIDEO_ACCELERATION_MFX</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.VIDEO_ACCELERATION_NONE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#VIDEO_ACCELERATION_NONE">VIDEO_ACCELERATION_NONE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.VIDEO_ACCELERATION_VAAPI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#VIDEO_ACCELERATION_VAAPI">VIDEO_ACCELERATION_VAAPI</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.VIDEOWRITER_PROP_DEPTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#VIDEOWRITER_PROP_DEPTH">VIDEOWRITER_PROP_DEPTH</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.VIDEOWRITER_PROP_FRAMEBYTES">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#VIDEOWRITER_PROP_FRAMEBYTES">VIDEOWRITER_PROP_FRAMEBYTES</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.VIDEOWRITER_PROP_HW_ACCELERATION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#VIDEOWRITER_PROP_HW_ACCELERATION">VIDEOWRITER_PROP_HW_ACCELERATION</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.VIDEOWRITER_PROP_HW_ACCELERATION_USE_OPENCL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#VIDEOWRITER_PROP_HW_ACCELERATION_USE_OPENCL">VIDEOWRITER_PROP_HW_ACCELERATION_USE_OPENCL</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.VIDEOWRITER_PROP_HW_DEVICE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#VIDEOWRITER_PROP_HW_DEVICE">VIDEOWRITER_PROP_HW_DEVICE</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.VIDEOWRITER_PROP_IS_COLOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#VIDEOWRITER_PROP_IS_COLOR">VIDEOWRITER_PROP_IS_COLOR</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.VIDEOWRITER_PROP_NSTRIPES">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#VIDEOWRITER_PROP_NSTRIPES">VIDEOWRITER_PROP_NSTRIPES</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="org.opencv.videoio.Videoio.VIDEOWRITER_PROP_QUALITY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="org/opencv/videoio/Videoio.html#VIDEOWRITER_PROP_QUALITY">VIDEOWRITER_PROP_QUALITY</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">Frames</a></li>
<li><a href="constant-values.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2023-06-28 12:47:21 / OpenCV 4.8.0</small></p>
</body>
</html>
