[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: arm64-v8a", "file_": "/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/libcxx_helper/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON /Users/<USER>/AndroidStudioProjects/MyApplication/opencv/.cxx/Debug/4i02kwe6/arm64-v8a/android_gradle_build.json due to:", "file_": "/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/libcxx_helper/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/libcxx_helper/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from '/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/.cxx/Debug/4i02kwe6/arm64-v8a'", "file_": "/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/libcxx_helper/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder '/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/.cxx/Debug/4i02kwe6/arm64-v8a'", "file_": "/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/libcxx_helper/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  -H/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/libcxx_helper \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=24 \\\n  -DANDROID_PLATFORM=android-24 \\\n  -DANDROID_ABI=arm64-v8a \\\n  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \\\n  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/build/intermediates/cxx/Debug/4i02kwe6/obj/arm64-v8a \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/build/intermediates/cxx/Debug/4i02kwe6/obj/arm64-v8a \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  -B/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/.cxx/Debug/4i02kwe6/arm64-v8a \\\n  -GNinja \\\n  -DANDROID_STL=c++_shared\n", "file_": "/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/libcxx_helper/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  -H/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/libcxx_helper \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=24 \\\n  -DANDROID_PLATFORM=android-24 \\\n  -DANDROID_ABI=arm64-v8a \\\n  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \\\n  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/build/intermediates/cxx/Debug/4i02kwe6/obj/arm64-v8a \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/build/intermediates/cxx/Debug/4i02kwe6/obj/arm64-v8a \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  -B/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/.cxx/Debug/4i02kwe6/arm64-v8a \\\n  -GNinja \\\n  -DANDROID_STL=c++_shared\n", "file_": "/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/libcxx_helper/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/libcxx_helper/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of /Users/<USER>/AndroidStudioProjects/MyApplication/opencv/.cxx/Debug/4i02kwe6/arm64-v8a/compile_commands.json.bin normally", "file_": "/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/libcxx_helper/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/libcxx_helper/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked /Users/<USER>/AndroidStudioProjects/MyApplication/opencv/.cxx/Debug/4i02kwe6/arm64-v8a/compile_commands.json to /Users/<USER>/AndroidStudioProjects/MyApplication/opencv/.cxx/tools/debug/arm64-v8a/compile_commands.json", "file_": "/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/libcxx_helper/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/libcxx_helper/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]