/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \
  -H/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/libcxx_helper \
  -DCMAKE_SYSTEM_NAME=Android \
  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
  -DCMAKE_SYSTEM_VERSION=24 \
  -DANDROID_PLATFORM=android-24 \
  -DANDROID_ABI=armeabi-v7a \
  -DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a \
  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \
  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \
  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake \
  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \
  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/build/intermediates/cxx/Debug/4i02kwe6/obj/armeabi-v7a \
  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/build/intermediates/cxx/Debug/4i02kwe6/obj/armeabi-v7a \
  -DCMAKE_BUILD_TYPE=Debug \
  -B/Users/<USER>/AndroidStudioProjects/MyApplication/opencv/.cxx/Debug/4i02kwe6/armeabi-v7a \
  -GNinja \
  -DANDROID_STL=c++_shared
